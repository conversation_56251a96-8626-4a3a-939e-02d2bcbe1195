package cec.jiutian.core.data.condition;

import cec.jiutian.core.data.config.QueryOperator;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/8
 */
@Data
public class SearchCondition implements Condition {
    private String key;
    private String value;
    private QueryOperator operator;

    @Override
    public String conditionExpStr() {
        return switch (operator) {
            case EQ -> key + " =:" + key;
            case LIKE -> key + " like :" + key;
            case RANGE -> key + " between :" + " l_" + key + " and :" + " r_" + key;
            case IN -> key + " in (:" + key + ")";
        };
    }
}
