package cec.jiutian.core.data.crud;

import java.util.List;

/**
 * 统一的对外接口，用以实现单表的基础的增删查改
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public interface Crud {

    /**
     * 根据PO类进行单表查询，传入的类中的非空字段将作为查询参数
     *
     * @param entity PO(persistent object) do not allow null <br>
     *               The not null and not empty field will be the parameters to query. <br>
     * @param <T>    Generally, it is an entity class that inherits BaseEntity.
     * @return entity list
     */
    <T> List<T> select(T entity);

    /**
     * According to PO update record. All field including empty value will update to database.
     *
     * @param entity PO(persistent object) do not allow null <br>
     * @param <T>    Generally, it is an entity class that inherits BaseEntity.
     * @return the numbers of insert record successfully, if fails,return integer less than 0.
     */
    <T> int update(T entity);

    /**
     * According to PO update record. <b>Only non-empty fields will be updated</b>.
     *
     * @param entity PO(persistent object) do not allow null <br>
     * @param <T>    Generally, it is an entity class that inherits BaseEntity.
     * @return the numbers of insert record successfully, if fails,return integer less than 0.
     */
    <T> int updateSelective(T entity);

    /**
     * According to PO insert record and return id value. <br>
     *
     * @param entity PO(persistent object) do not allow null <br>
     *               The not null and not empty field will insert to database. <br>
     *               Same as insertSelective, this is the default behavior of the insert operation.
     * @return the numbers of insert record successfully, if fails,return integer less than 0.
     */
    <T> int insert(T entity);

    /**
     * According to entity object delete record.
     *
     * @param entity table's entity(do not allow null).<br>
     *               The entity corresponding to table and can not be null.<br>
     *               If the field value is not null and not empty field as filter condition, <br>
     *               the operator is equal sign.eg:field=value
     * @return the numbers of delete records successfully, if fails,return integer less than 0.
     */
    <T> int delete(T entity);

    /**
     * Delete record by id.
     *
     * @param c  table's entity class(do not allow null).
     * @param id value of entity's id field.
     * @return the number of deleted record(s) successfully,if fails, return integer less than 0.
     */
    <T, PK> int deleteById(Class<T> c, PK id);

    /**
     * Select record by id.
     *
     * @param c  table's entity class(do not allow null).
     * @param id value of entity's id field.
     * @return return one entity which owns this id.
     */
    <T, PK> T getById(Class<T> c, PK id);

    /**
     * According entityList to insert batch.
     *
     * @param entityList do not allow null
     * @param <T>        entity class
     * @return the number of deleted record(s) successfully,if fails, return integer less than 0.
     */
    <T> int insertBatch(List<T> entityList);

    <T> int updateBatchById(List<T> list);

    <T> int deleteBatchById(List<T> list);
}
