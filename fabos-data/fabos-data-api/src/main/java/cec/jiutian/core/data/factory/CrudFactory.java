package cec.jiutian.core.data.factory;

import cec.jiutian.core.data.crud.Crud;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
public class CrudFactory {
    public static Crud getCrud() {
        // 此处可以更换实现逻辑
        return SpringBeanUtils.getBean(Crud.class);
    }
//
//    public static CrudExample getCrudExample() {
//        // 此处可以更换实现逻辑
//        return SpringBeanUtils.getBean(CrudExample.class);
//    }
//
//    public static CrudEnhanced getCrudEnhanced() {
//        // 此处可以更换实现逻辑
//        return SpringBeanUtils.getBean(CrudEnhanced.class);
//    }
//
//    public static CrudCondition getCrudCondition() {
//        // 目前没有默认实现
//        throw new RuntimeException("none implement！");
//    }
}
