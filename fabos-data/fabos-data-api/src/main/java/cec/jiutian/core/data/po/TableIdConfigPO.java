package cec.jiutian.core.data.po;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "fd_table_id_config")
public class TableIdConfigPO {

    /**
     * 自增ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long identifier;

    /**
     * table名称
     */
    @Column(name = "table_name")
    private String tableName;

    /**
     * 对应序列名称
     */
    @Column(name = "sequence_name")
    private String sequenceName;

    /**
     * 当前序列
     */
    @Column(name = "now_sequence")
    private Long nowSequence;

    /**
     * id类型 GENERAL:虚拟序列，SEQUENCE:真实序列
     */
    @Column(name = "id_type")
    private String identifierType;

    @Transient
    private String columnName;

    @Transient
    private Long serviceId;
}
