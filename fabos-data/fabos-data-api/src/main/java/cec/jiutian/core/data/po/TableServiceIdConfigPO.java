package cec.jiutian.core.data.po;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "fd_table_service_id_config")
public class TableServiceIdConfigPO {

    /**
     * 自增ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long identifier;

    /**
     * 服务id
     */
    @Column(name = "service_id")
    private String serviceName;

    /**
     * mac名称
     */
    @Column(name = "mac_address")
    private String macAddress;

    /**
     * 端口号
     */
    @Column(name = "port")
    private String port;
}
