package cec.jiutian.core.data.config;

import lombok.Data;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @date: 2022-3-29 16:55
 * <p>
 * 用于Response前端分页
 */
@Data
public class Pager {
    private int page;
    private int perPage;
    private long count;
    private Collection<Map<String, Object>> rows;

    public static Pager emptyPage() {
        Pager pager = new Pager();
        pager.setCount(0);
        pager.setRows(Collections.EMPTY_LIST);
        return pager;
    }
}
