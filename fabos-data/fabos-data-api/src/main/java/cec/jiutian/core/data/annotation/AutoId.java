package cec.jiutian.core.data.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Description: 主键注解
 * 支持主键生成方式： 数据库虚拟序列、数据库序列、时间序列
 * 格式为001.002.0005sa.45451
 * 其中 '001':表id、'002':服务器id、'0005sa':36进制序列、'45451':10进制序列
 * @date 2023/4/21 下午4:51
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface AutoId {

    /**
     * @return id类型（默认为普通id）
     */
    IdType type() default IdType.GENERAL;

    /**
     * id类型
     */
    enum IdType {
        /**
         * 虚拟序列:基于数据库数字for update 数字加1的序列生成方式(性能差)
         */
        GENERAL,
        /**
         * 时间序列:基于雪花算法，去掉其中服务器为和机房位，只保留时间+序列(性能强)
         */
        TIME_SEQUENCE
    }

}
