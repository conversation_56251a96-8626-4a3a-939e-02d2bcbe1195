#FROM hub.fabos.com:8090/fabos/openjdk:17-jre-alpine
FROM hub.fabos.com:8090/fabos/openjdk:17-jdk-alpine-with-fonts
LABEL maintainer="<EMAIL>"

# 包含字体信息的packages已经打到openjdk:17-jdk-alpine-with-fonts这个镜像里了
#RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
#    apk update && \
#    apk add --no-cache fontconfig ttf-dejavu

COPY target/wms-app-3.2.0.jar /entry.jar

ENV PARAMS=""

ENTRYPOINT ["sh","-c","java $PARAMS -jar /entry.jar" ]
