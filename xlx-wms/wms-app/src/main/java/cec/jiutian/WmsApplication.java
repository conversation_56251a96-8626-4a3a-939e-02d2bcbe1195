package cec.jiutian;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import cec.jiutian.fc.log.utils.LoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.util.StopWatch;

@EntityScan
@SpringBootApplication
@FabosJsonScan
//@EnableDubbo
@EnableJpaAuditing
@Slf4j
@EnableDiscoveryClient    // 启用Nacos服务发现
@EnableFeignClients
public class WmsApplication {
    public static void main(String[] args) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        SpringApplication.run(WmsApplication.class, args);
        stopWatch.stop();
        log.info("本次启动总计耗时：{}秒", stopWatch.getTotalTimeSeconds());
        LoggerUtil.startPrintStreamRedirecting();
    }
}
