package cec.jiutian.remote.feign;

import cec.jiutian.bc.si.remote.rpc.StockInRpc;
import cec.jiutian.bc.si.service.StockInService;
import cec.jiutian.constant.WmsCommonConstant;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.bc.si.domain.stockInRequest.service.StockInApplyService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;

/**
 * 提供给其他系统入库申请的feign接口
 */
@RestController
@RequestMapping(WmsCommonConstant.WMS_URL_PREFIX+ FabosJsonRestPath.FABOS_REMOTE_API)
public class StockInApplyFeign {
    @Resource
    private StockInApplyService stockInApplyService;

    private final StockInRpc stockInRpc;

    public StockInApplyFeign( StockInRpc stockInRpc) {
        this.stockInRpc = stockInRpc;
    }


    /**
     *<p>提供给MES,创建 生产退料申请，在MES系统上建立退料申请，确认发布后同步到WMS<p>
     *  主子表结构
     *  主表：org_id(申请部门)
     *  子表 details:serialLotId(本厂批号)，inventoryId（物料批次Id）,materialId(物料主键id),materialCode(物料编码)
     *                 ,materialName(物料名称)，measureUnit（物料单位）,materialSpecification(物料规格),requestQuantity（申请数量）,boxedCode(箱次编码),boxNo(箱号)
     */

    @PostMapping("/createMaterialReturnRequestByMes")
    public String createMaterialReturnRequestByMes(@RequestBody Map<String, Object> params) {
         return stockInApplyService.createMaterialReturnRequest(params);
    }

    /**
     *<p>提供给QMS,创建 样品归还申请，在QMS系统建立还样单，确认发布后同步到WMS<p>
     *  主子表结构
     *  主表：org_id(申请部门)
     *  子表 details:serialLotId(本厂批号)，inventoryId（物料批次Id）,materialId(物料主键id),materialCode(物料编码)
     *                 ,materialName(物料名称)，measureUnit（物料单位）,materialSpecification(物料规格)，requestQuantity（申请数量）,boxedCode(箱次编码),boxNo(箱号)
     */

    @PostMapping("/createSampleReturnRequestByQms")
    public String createSampleReturnRequestByQms(@RequestBody Map<String, Object> params) {
         return stockInApplyService.createSampleReturnRequest(params);
    }

    /**
     *<p>提供给其他系统，创建其他入库<p>
     *  主子表结构
     *  主表：orgId(申请部门Id)，originCode(来源单号)
     *  子表 details:factoryLotIdentifier(本厂批号)，originDetailCode(来源单明细编码),materialId(物料主键id),materialCode(物料编码)
     *                 ,materialName(物料名称)，unit（物料单位）,materialSpecification(物料规格),materialCategory(种类)，quantity（入库数量）
     */
    @PostMapping("/createRestStockIn")
    public String createRestStockIn(@RequestParam String param) throws IOException {
        return stockInRpc.createRestStockIn(param);
    }


    /**
     *<p>提供给QMS系统，创建其他入库<p>
     *  主子表结构
     *  主表：orgId(申请部门Id)，originCode(来源单号)
     *  子表 details:factoryLotIdentifier(本厂批号)，originDetailCode(来源单明细编码),materialId(物料主键id),materialCode(物料编码)
     *                 ,materialName(物料名称)，unit（物料单位）,materialSpecification(物料规格),materialCategory(种类)，quantity（入库数量）
     */
    @PostMapping("/createWaitConfirmRestStockIn")
    public String createWaitConfirmRestStockIn(@RequestParam String param) throws IOException {
        return stockInRpc.createWaitConfirmRestStockIn(param);
    }

}
