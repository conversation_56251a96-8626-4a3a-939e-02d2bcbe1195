package cec.jiutian.remote.feign;

import cec.jiutian.bc.outbound.domain.stockOutApply.model.*;
import cec.jiutian.bc.outbound.domain.stockOutApply.service.StockOutApplyService;
import cec.jiutian.constant.WmsCommonConstant;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.frame.service.FabosJsonService;
import cec.jiutian.core.frame.util.FabosJsonUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 提供给其他系统出库申请的feign接口
 */
@RestController
@RequestMapping(WmsCommonConstant.WMS_URL_PREFIX + FabosJsonRestPath.FABOS_REMOTE_API)
public class StockOutApplyFeign {
    private final StockOutApplyService stockoutApplyService;
    private final FabosJsonService fabosJsonService;

    public StockOutApplyFeign(StockOutApplyService stockoutApplyService, FabosJsonService fabosJsonService) {
        this.stockoutApplyService = stockoutApplyService;
        this.fabosJsonService = fabosJsonService;
    }

    /**
     * <p>提供给QMS,质量检验部门在QMS上根据检验通知单选择仓库库存建立检验任务，确认发布后同步到WMS，由WMS系统自动建立检验领料申请<p>
     * 主子表结构
     * 主表：org_id(申请部门)，inspectionTaskNumber(检验任务单号),inspectionRequestNumber(检验通知单号)
     * 子表 details:serialLotId(本厂批号)，inventoryId（库存主表Id）,materialId(物料主键id),materialCode(物料编码)
     * ,materialName(物料名称)，measureUnit（物料单位）,materialSpecification(物料规格)，requestQuantity（申请数量）
     * ,warehouseId(仓库id),boxedCode(箱次编码),boxNo(箱号),boxedDetailId(库存明细子表id)
     */

    @PostMapping("/createInspectMaterialApplyByQms")
    public String createInspectMaterialApplyByQms(@RequestBody Map<String, Object> params) {
         return stockoutApplyService.createInspectMaterialApply(params);
    }

    /**
     * <p>提供给MES,MES上建立生产领料申请，发布后同步到WMS<p>
     * 主子表结构
     * 主表：org_id(申请部门)，productionOrder(生产工单),productionOrderType(生产工单类型),productionReworkOrder(成品返工工单)
     * 子表 details:serialLotId(本厂批号)，inventoryId（库存主表Id）,materialId(物料主键id),materialCode(物料编码)
     * ,materialName(物料名称)，measureUnit（物料单位）,materialSpecification(物料规格)，requestQuantity（申请数量）
     * ,warehouseId(仓库id),boxedCode(箱次编码),boxNo(箱号),boxedDetailId(库存明细子表id)
     */

    @PostMapping("/createProductMaterialApplyMes")
    public String createProductMaterialApplyMes(@RequestBody Map<String, Object> params) {
        return stockoutApplyService.createProductMaterialApply(params);
    }

    /**
     * <p>提供给MES/QMS/EAM等系统，做出库申请<p>
     * 主子表结构
     * 主表：org_id(申请部门)，sourceTaskNumber(来源单号)
     * 子表 details:serialLotId(本厂批号)，inventoryId（库存主表Id）,materialId(物料主键id),materialCode(物料编码)
     * ,materialName(物料名称)，measureUnit（物料单位）,materialSpecification(物料规格)，requestQuantity（申请数量）
     * <p>
     * return: wms生成的领料申请单号
     */

    @PostMapping("/createOtherStockOutApply")
    public String createOtherStockOutApply(@RequestBody Map<String, Object> params) {
        return stockoutApplyService.createOtherStockOutApply(params);
    }

    /**
     * <p>条件查询模型<p>
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     */
    @PostMapping("/getStockOutApplyByCondition")
    public Map<String, Object> getStockOutApplyByCondition(@RequestParam String fabosJsonName, @RequestParam Map<String, Object> condition) {

        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        Object data = fabosJsonService.getByCustomConditions(fabosJsonModel, condition);
        Map<String, Object> objectMap = FabosJsonUtil.generateFabosJsonDataMap(fabosJsonModel, data);
        return objectMap;
    }


    /**
     * <p>根据code查询数据<p>
     * fabosJsonName:模型名称，出入库模型名称如下
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     */
    @PostMapping("/getStockOutApplyByCode")
    public Map<String, Object> getStockOutApplyByCode(@RequestParam String fabosJsonName, @RequestParam String generalCode) {

        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        Object data = fabosJsonService.getByCustomConditions(fabosJsonModel, Map.of("generalCode", generalCode));
        Map<String, Object> objectMap = FabosJsonUtil.generateFabosJsonDataMap(fabosJsonModel, data);
        return objectMap;
    }


    /**
     * <p>查询申请单<p>
     * fabosJsonName:模型名称，出入库模型名称如下
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     */
    @PostMapping("/getStockOutApply")
    public List<Map<String, Object>> getStockOutApply(@RequestParam String fabosJsonName) {
        List<Map<String, Object>> result = new ArrayList<>();
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        List<Object> data = fabosJsonService.getAllTableData(fabosJsonModel);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(item -> {
                result.add((Map<String, Object>) item);
            });
        }
        return result;
    }


    /**
     * <p>根据状态查询外包出库申请单p>
     * currentState:状态
     */
    @PostMapping("/getOutsourceStockOutApply")
    public List<Map<String, Object>> getOutsourceStockOutApply(@RequestParam String currentState) {
        List<Map<String, Object>> result = new ArrayList<>();
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson("OutsourceStockOutApply");
        List<Object> data = fabosJsonService.getTableDatasByCustomConditions(fabosJsonModel,Map.of("currentState", currentState));
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(item -> {
                result.add((Map<String, Object>) item);
            });
        }
        return result;
    }


    /**
     * <p>根据外包申请单号、内包批次号获取内包批次列表<p>
     * generalCode: 外包申请单号
     * excludeLots: 需要排除的内包批次号, 逗号隔开
     */
    @PostMapping("/getOutSourceStockOutInnerLot")
    public Object getOutSourceStockOutInnerLot(@RequestParam String generalCode, @RequestParam(required = false) String excludeLots) {

        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson("OutsourceStockOutApply");
        Object data = fabosJsonService.getByCustomConditions(fabosJsonModel, Map.of("generalCode", generalCode));
        if (Objects.isNull(data)) {
            return null;
        }
        OutsourceStockOutApply outsourceStockOutApply = (OutsourceStockOutApply) data;
        List<OutsourceStockOutApplyDetail> details = outsourceStockOutApply.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return null;
        }
        List<String> excludeLotList;
        if(Objects.nonNull(excludeLots)){
            excludeLotList = Arrays.asList(excludeLots.split(","));
        } else {
            excludeLotList = new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(excludeLotList)) {
            details = details.stream().filter(detail -> !excludeLotList.contains(detail.getSerialLotId()))
                    .collect(Collectors.toList());;
        }
        return Collections.singletonList(details);
    }


    /**
     * <p>根据外包申请单号获取外包批次列表<p>
     * generalCode: 外包申请单号
     */
    @PostMapping("/getOutSourceStockOutLot")
    public Object getOutSourceStockOutLot(@RequestParam String generalCode) {

        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson("OutsourceStockOutApply");
        Object data = fabosJsonService.getByCustomConditions(fabosJsonModel, Map.of("generalCode", generalCode));
        if (Objects.isNull(data)) {
            return null;
        }
        OutsourceStockOutApply outsourceStockOutApply = (OutsourceStockOutApply) data;
        List<OutsourceStockOutApplyMaterialAutoDetail> materialDetails = outsourceStockOutApply.getMaterialDetails();
        if (CollectionUtils.isEmpty(materialDetails)) {
            return null;
        }
        List<OutsourceStockOutApplyDetailAutoLotDTO> result = new ArrayList<>();
        for (OutsourceStockOutApplyMaterialAutoDetail materialDetail : materialDetails) {
            result.addAll(materialDetail.getDetails());
        }
        return Collections.singletonList(result);
    }


    /**
     * <p>1.基于外包批号修改外包剩余重量（批量修改）<p>
     * params: 外包批号与剩余重量键值对
     {
     "DBNN-20250515-0001": 1.5,
     "DBNN-20250515-0002": 0.6
     }
     */
    @PostMapping("/modifyOutSourceStockOutLot")
    public Boolean modifyOutSourceStockOutLot(@RequestBody Map<String, Double> params) {

        return stockoutApplyService.modifyOutSourceStockOutLot(params);
    }

    /**
     * <p>1.基于申请单号、内包批号（数组）对内包批次出库<p>
     * params: 申请单号与内包批号（数组）
     {
     "generalCode": "OSAN-20250514-0005",
     "serialLotIds": ["LOT-202502190003","LOT-202502190004"]
     }
     */
    @PostMapping("/outSourceStockOut")
    public String outSourceStockOut(@RequestBody Map<String, Object> params) {

        return stockoutApplyService.outSourceStockOut(params);
    }

    /**
     * <p>关闭单据<p>
     * fabosJsonName:模型名称，出入库模型名称如下
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     * id:单据id
     */

    @PostMapping("/closeApply")
    public void closeApply(@RequestParam String fabosJsonName, @RequestParam String id) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        ApplyStateModel data = (ApplyStateModel) fabosJsonService.getById(fabosJsonModel, id);
        stockoutApplyService.closeApply(data);
    }

    /**
     * <p>关闭单据<p>
     * fabosJsonName:模型名称，出入库模型名称如下
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     * generalCode:单据编码
     */

    @PostMapping("/closeApplyByCode")
    public void closeApplyByCode(@RequestParam String fabosJsonName, @RequestParam String generalCode) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        ApplyStateModel data = (ApplyStateModel) fabosJsonService.getByCustomConditions(fabosJsonModel, Map.of("generalCode", generalCode));
        stockoutApplyService.closeApply(data);
    }

    /**
     * <p>修改单据状态<p>
     * fabosJsonName:模型名称，出入库模型名称如下
     * 【检验领料申请：InspectMaterialApply】【外包出库申请：OutsourceStockOutApply】【生产领料申请：ProductMaterialApply】【供应商退货申请：SupplierReturnApply】【其他出库申请：OtherStockOutApplyProxy】
     * 【生产退料申请：MaterialReturnRequest】【样品归还申请：SampleReturnRequest】
     * generalCode:单据编码
     *
     * state:目标状态        【normal("正常")】,【waiting_outbound("待出库")】,【outbounding("出库中")】,【outbound("已出库")】
     */

    @PostMapping("/changeApplyStateByCode")
    public void changeApplyStateByCode(@RequestParam String fabosJsonName, @RequestParam String generalCode,  @RequestParam String state) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
        ApplyStateModel data = (ApplyStateModel) fabosJsonService.getByCustomConditions(fabosJsonModel, Map.of("generalCode", generalCode));
        stockoutApplyService.changeApplyState(data, state);
    }
}
