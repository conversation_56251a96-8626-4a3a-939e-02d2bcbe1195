<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="APP_ID" source="app.id"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %d{HH:mm:ss.SSS} %clr([%level]) %magenta(%20.20(%method)) - %cyan(%-40.40class) : %msg%n
            </pattern>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
    <appender name="DAILY_FILE_PROD" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/fabos/prod/log/${APP_ID}.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/app/fabos/prod/log/archived/${APP_ID}/%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] %class#%method : %msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="DAILY_FILE_UAT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/fabos/uat/log/${APP_ID}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/app/fabos/uat/log/archived/${APP_ID}/%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] %class#%method : %msg%n
            </pattern>
        </encoder>
    </appender>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="DAILY_FILE_PROD"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <root level="INFO">
<!--            <appender-ref ref="STDOUT"/>-->
<!--            <appender-ref ref="DAILY_FILE_PROD"/>-->
        </root>
    </springProfile>

    <springProfile name="uat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="DAILY_FILE_UAT"/>
        </root>
    </springProfile>
</configuration>
