# model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu,status
# FALSE,,usual,7,fa fa-cogs,出库管理,$OmCenter,,
# TRUE,ConsumeRequest,multiTable,5001,,,,$OmCenter,
# TRUE,ConsumeOrder,multiTable,5002,,,,$OmCenter,
# TRUE,CuttingOrder,multiTable,5003,,,,$OmCenter,
# TRUE,DeliveryRequest,multiTable,5004,,,,$OmCenter,
# TRUE,PriceAllocation,multiTable,5006,,,,$OmCenter,
# TRUE,OutboundOrder,multiTable,5005,,,,$OmCenter,
# TRUE,OtherStockOut,multiTable,5007,,,,$OmCenter,
# TRUE,RefundStockOut,multiTable,5008,,,,$OmCenter,
# TRUE,IssueNotice,multiTable,5009,,,,$OmCenter,
# TRUE,OutsourcingIssue,multiTable,5010,,,,$OmCenter,
# TRUE,MaterialControlPlan,multiTable,5011,,,,$OmCenter,
# FALSE,,usual,8,fa fa-cogs,下料管理,$OmCuttingCenter,,
# TRUE,CuttingPlan,multiTable,5101,,,,$OmCuttingCenter,
# TRUE,CuttingDelivery,usual,5102,,,,$OmCuttingCenter,
# TRUE,TailMaterialStockIn,multiTable,5103,,,,$OmCuttingCenter,
# TRUE,TailInventory,usual,5104,,,,$OmCuttingCenter,
