model-driven,modelClass,menuType,sort,menuIcon,menuName,menuValue,parentMenu,status
FALSE,,usual,9,fa fa-cogs,库存管理,$LmCenter,,
TRUE,OpeningInventory,multiTable,6001,,,,$LmCenter,
TRUE,InventoryDTO,usual,6002,,,,$LmCenter,
TRUE,Inventory,usual,6003,,,,$LmCenter,
TRUE,InventoryIqc,usual,6004,,,,$LmCenter,
TRUE,PurposeChangeInventoryIqcCreate,usual,1,,,,InventoryIqc,2
TRUE,OverDullInventoryIqcCreate,usual,2,,,,InventoryIqc,2
TRUE,InventoryIqcItem,usual,3,,,,InventoryIqc,2
TRUE,InventoryIqcResult,usual,4,,,,InventoryIqc,2
TRUE,InventoryPurposeChange,multiTable,6101,,,,$LmCenter,
TRUE,InventoryNumberChange,multiTable,6102,,,,$LmCenter,
TRUE,StockRelocation,multiTable,6103,,,,$LmCenter,
TRUE,StockMove,usual,6104,,,,$LmCenter,
TRUE,StockScrap,multiTable,6105,,,,$LmCenter,
FALSE,,usual,10,fa fa-cogs,库存预警,$LmWarning,,
TRUE,SafeInventoryWarning,usual,6201,,,,$LmWarning,
TRUE,AdventInventoryWarning,usual,6202,,,,$LmWarning,
TRUE,OverDullInventoryWarning,usual,6203,,,,$LmWarning,
TRUE,DullInventoryWarning,usual,6204,,,,$LmWarning,
FALSE,,usual,11,fa fa-cogs,盘点管理,$StocktakingCenter,,
TRUE,StocktakingPlan,multiTable,6301,,,,$StocktakingCenter,
TRUE,CountingTaskManage,multiTable,6302,,,,$StocktakingCenter,
TRUE,PendingStocktakingTask,multiTable,6304,,,,$StocktakingCenter,
TRUE,MyCountingSubTask,multiTable,6303,,,,$StocktakingCenter,
TRUE,StocktakingAdjustment,multiTable,6305,,,,$StocktakingCenter,
TRUE,StocktakingPlanWithArea,multiTable,2,,,,$StocktakingCenter,2
TRUE,StocktakingPlanWithMaterial,multiTable,3,,,,$StocktakingCenter,2