package cec.jiutian.view.fun;


import cec.jiutian.view.config.Comment;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.util.List;

/**
 *
 */
public interface OperationHandler<@Comment("行数据类型") Target, @Comment("表单输入对象类型") FabosJsonObject> {

    /**
     * @param data        行数据
     * @param modelObject 表单输入数据
     * @param param       注解回传参数
     * @return 事件触发成功后需要前端执行的 js 表达式
     */
    @Comment("按钮事件触发类")
    @Comment("返回值：事件触发成功后需要前端执行的 js 表达式，不需要此参数返回空即可")
    default String exec(List<Target> data, FabosJsonObject modelObject, String[] param) {
        return null;
    }

    @Comment("初始化 fabosJson 表单的值")
    default FabosJsonObject fabosJsonFormValue(List<Target> data, FabosJsonObject fabosJsonForm, String[] param) {
        return fabosJsonForm;
    }

    /**
     * 设置返回文件，需在@Row Operation内配置type=FILE
     * <br>
     * <code>
     * // Example1: Using java.io.File<br>
     * File file = new File("/path/to/your/file.xlsx");<br>
     * // modify your file<br>
     * return new DownloadableFile(new FileInputStream(file), "xxx.xx");<br>
     * // 两种rerurn都行<br>
     * return new DownloadableFile(file, "xxx.xx");<br>
     * <p>
     * // Example2: Using org.apache.poi.ss.usermodel.Workbook<br>
     * Workbook workbook = new XSSFWorkbook();<br>
     * // modify your workbook<br>
     * ByteArrayOutputStream outputStream = new ByteArrayOutputStream();<br>
     * workbook.write(outputStream);<br>
     * workbook.close();<br>
     * return new DownloadableFile(outputStream, "xxx.xx");<br>
     * // 两种rerurn都行<br>
     * return new DownloadableFile(new ByteArrayInputStream(outputStream.toByteArray()), "xxx.xx");
     * </code>
     *
     * @param selectedData 前端选择的行的数据
     * @param param        在RowOperation中定义的param
     * @return 组装好的DownloadableFile对象
     */
    default DownloadableFile fileOperator(List<Target> selectedData, String[] param) {
        return null;
    }

    @Data
    @NoArgsConstructor
    public class DownloadableFile implements Serializable {
        private InputStream contentStream;
        private String filename;
        private String contentType;

        /**
         * content type 只会取第一个值
         */
        public DownloadableFile(InputStream contentStream, String filename, String... contentType) {
            this.contentStream = contentStream;
            this.filename = filename;
            if (contentType != null && contentType.length > 0) {
                this.contentType = contentType[0];
            }
        }

        /**
         * content type 只会取第一个值
         */
        public DownloadableFile(ByteArrayOutputStream outputStream, String filename, String... contentType) {
            this.contentStream = new ByteArrayInputStream(outputStream.toByteArray());
            this.filename = filename;
            if (contentType != null && contentType.length > 0) {
                this.contentType = contentType[0];
            }
        }

        /**
         * content type 只会取第一个值
         */
        @SneakyThrows
        public DownloadableFile(File file, String filename, String... contentType) {
            this.contentStream = new FileInputStream(file);
            this.filename = filename;
            if (contentType != null && contentType.length > 0) {
                this.contentType = contentType[0];
            }
        }
    }
}
