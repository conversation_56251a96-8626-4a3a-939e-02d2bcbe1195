package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.model.PPKInspectionTaskDetail;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.model.PPKSampleTask;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskGetMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class PPKGetSampleOperationHandler implements OperationHandler<PPKSampleTask, PPKSampleTaskGetMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<PPKSampleTask> data, PPKSampleTaskGetMTO modelObject, String[] param) {
        if (modelObject != null) {
            PPKSampleTask condition = new PPKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            PPKSampleTask samplingTask = fabosJsonDao.selectOne(condition);
            samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
            samplingTask.setGetSampleDate(new Date());
            samplingTask.setActualSerialNumber(modelObject.getActualSerialNumber());
            fabosJsonDao.mergeAndFlush(samplingTask);

            PPKInspectionTaskDetail inspectionTaskDetail = fabosJsonDao.findById(PPKInspectionTaskDetail.class, samplingTask.getPpkInspectionTaskDetailId());
            inspectionTaskDetail.setCurrentState(samplingTask.getBusinessState());
            inspectionTaskDetail.setGetSampleDate(samplingTask.getGetSampleDate());
            inspectionTaskDetail.setActualSerialNumber(samplingTask.getActualSerialNumber());
            fabosJsonDao.mergeAndFlush(inspectionTaskDetail);
        }
        return "alert(操作成功)";
    }

    @Override
    public PPKSampleTaskGetMTO fabosJsonFormValue(List<PPKSampleTask> data, PPKSampleTaskGetMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            PPKSampleTask samplingTask = data.get(0);
            fabosJsonForm.setId(samplingTask.getId());
            fabosJsonForm.setGeneralCode(samplingTask.getGeneralCode());
            fabosJsonForm.setPpkInspectionTaskCode(samplingTask.getPpkInspectionTaskCode());
            fabosJsonForm.setProcessName(samplingTask.getProductProcessMTO().getName());
            fabosJsonForm.setMaterialCode(samplingTask.getMaterialCode());
            fabosJsonForm.setMaterialName(samplingTask.getMaterialName());
            fabosJsonForm.setSampleWeight(samplingTask.getSampleWeight());
        }
        return fabosJsonForm;
    }
}
