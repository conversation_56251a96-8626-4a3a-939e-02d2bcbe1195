//package cec.jiutian.bc.quatityTask.domain.taskFlow.model;
//
//import cec.jiutian.bc.modeler.domain.inspectionItem.handler.ItemCodeGenerateDynamicHandler;
//import cec.jiutian.bc.quatityTask.enumration.TaskFlowStatusEnum;
//import cec.jiutian.core.frame.module.MetaModel;
//import cec.jiutian.view.DependFiled;
//import cec.jiutian.view.DynamicField;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.SubTableField;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.EditType;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.ChoiceType;
//import cec.jiutian.view.field.edit.InputType;
//import cec.jiutian.view.field.edit.Search;
//
///**
// * <AUTHOR>
// * @date 2025/5/9
// * @description TODO
// */
////@FabosJson(
////        name = "任务审批流",
////        orderBy = "TaskFlow.createTime desc",
////        dataProxy = TaskFlowProxy.class,
////        rowBaseOperation = {
////                @RowBaseOperation(
////                        code = "edit",
////                        ifExpr = "status == 'Valid'",
////                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
////                ),
////                @RowBaseOperation(
////                        code = "delete",
////                        ifExpr = "status == 'Valid'",
////                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
////                )
////        },
////        rowOperation = {
////                @RowOperation(
////                        title = "生效",
////                        code = "TaskFlow@VALID",
////                        operationHandler = TaskFlowValidOprHandler.class,
////                        operationParam={"Valid"},
////                        mode = RowOperation.Mode.SINGLE,
////                        callHint = "是否确定操作？",
////                        show = @ExprBool(
////                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
////                                params = "TaskFlow@VALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
////                        ),
////                        ifExpr = "status != 'Invalid'",
////                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
////                ),
////                @RowOperation(
////                        title = "失效",
////                        code = "TaskFlow@INVALID",
////                        operationHandler = TaskFlowValidOprHandler.class,
////                        operationParam={"Invalid"},
////                        mode = RowOperation.Mode.SINGLE,
////                        callHint = "是否确定操作？",
////                        show = @ExprBool(
////                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
////                                params = "TaskFlow@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
////                        ),
////                        ifExpr = "status != 'Valid'",
////                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
////                ),
////        }
////)
////@Table(name = "qms_qt_task_flow",
////        uniqueConstraints = {
////                @UniqueConstraint(columnNames = {"generalCode"})
////        }
////)
////@Entity
////@Getter
////@Setter
////@TemplateType(type = "multiTable")
//public class TaskFlow extends MetaModel {
//
//    @FabosJsonField(
//            views = @View(title = "编号"),
//            edit = @Edit(title = "编号", notNull = true),
//            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
//                    dynamicHandler = ItemCodeGenerateDynamicHandler.class))
//    )
//    @SubTableField
//    private String generalCode;
//
//    @FabosJsonField(
//            views = @View(title = "检验项目名称"),
//            edit = @Edit(title = "检验项目名称", notNull = true, search = @Search(vague = true),
//                    inputType = @InputType(length = 40))
//    )
//    @SubTableField
//    private String name;
//
//    @FabosJsonField(
//            views = @View(title = "当前状态"),
//            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE, search = @Search(),
//                    choiceType = @ChoiceType(fetchHandler = TaskFlowStatusEnum.class))
//    )
//    private String status;
//
//    @FabosJsonField(
//            views = @View(title = "备注"),
//            edit = @Edit(title = "备注",
//                    type = EditType.TEXTAREA)
//    )
//    private String remark;
//}
