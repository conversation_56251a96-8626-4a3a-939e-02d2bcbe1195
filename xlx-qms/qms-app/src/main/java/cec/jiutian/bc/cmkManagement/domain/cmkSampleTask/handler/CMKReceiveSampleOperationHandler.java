package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto.CMKSampleTaskReceiveMTO;
import cec.jiutian.bc.deliveryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.abnormalFeedbackHandling.model.AbnormalFeedbackHandling;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class CMKReceiveSampleOperationHandler implements OperationHandler<CMKSampleTask, CMKSampleTaskReceiveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<CMKSampleTask> data, CMKSampleTaskReceiveMTO modelObject, String[] param) {
        if (modelObject != null) {
            CMKSampleTask condition = new CMKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            CMKSampleTask sampleTask = fabosJsonDao.selectOne(condition);

            if (!TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(sampleTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            if (InspectionResultEnum.Enum.QUALIFIED.name().equals(modelObject.getAppearanceInspect())) {
                sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name());
            }else {
                if (UnqualifiedHandleWayEnum.Enum.rePackage.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
                }else if (UnqualifiedHandleWayEnum.Enum.reSampling.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.EXCEPTION_STOP.name());
                    CMKSampleTask newSamplingTask = new CMKSampleTask();
                    BeanUtil.copyProperties(sampleTask, newSamplingTask);
                    newSamplingTask.setId(null);
                    newSamplingTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
                    newSamplingTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.CMKSampleTask.name(), 1, null).get(0)));
                    fabosJsonDao.mergeAndFlush(newSamplingTask);
                }else if (UnqualifiedHandleWayEnum.Enum.exception.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
                    if (StringUtils.isNotEmpty(modelObject.getAbnormalDescription())) {
                        AbnormalFeedbackHandling abnormalFeedbackHandling = new AbnormalFeedbackHandling();
                        abnormalFeedbackHandling.setAbnormalFeedbackHandlingFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null).get(0));
                        abnormalFeedbackHandling.setAnalyzeAssociatedDocumentNumber(modelObject.getGeneralCode());
                        abnormalFeedbackHandling.setAbnormalDescription(modelObject.getAbnormalDescription());
                        abnormalFeedbackHandling.setSubmissionTime(modelObject.getSubmissionTime());
                        abnormalFeedbackHandling.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
                        UserForInsTaskMTO userForInsTaskMTO = fabosJsonDao.getById(UserForInsTaskMTO.class, modelObject.getDiscoveredPersonId());
                        abnormalFeedbackHandling.setDiscoveredPersonName(userForInsTaskMTO == null ? null : userForInsTaskMTO.getName());
                        abnormalFeedbackHandling.setAbnormalAttachments(modelObject.getAbnormalAttachments());
                        fabosJsonDao.mergeAndFlush(abnormalFeedbackHandling);
                    }
                }
            }
            fabosJsonDao.mergeAndFlush(sampleTask);
        }
        return "alert(操作成功)";
    }
}
