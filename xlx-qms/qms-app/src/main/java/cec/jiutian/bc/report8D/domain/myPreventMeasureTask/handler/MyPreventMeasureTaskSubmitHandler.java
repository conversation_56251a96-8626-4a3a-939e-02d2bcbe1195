package cec.jiutian.bc.report8D.domain.myPreventMeasureTask.handler;

import cec.jiutian.bc.report8D.domain.myPreventMeasureTask.model.MyPreventMeasureTask;
import cec.jiutian.bc.report8D.domain.report8D.model.PreventMeasures;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MyPreventMeasureTaskSubmitHandler implements OperationHandler<MyPreventMeasureTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyPreventMeasureTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyPreventMeasureTask myPreventMeasureTask = data.get(0);
            List<PreventMeasures> updateDetailList = new ArrayList<>();
            for (PreventMeasures p : myPreventMeasureTask.getPreventMeasuresList()) {
                if(!p.getResponsiblePersonId().equals(UserContext.getUserId())){
                    continue;
                }
                if(!MeasureProgressEnum.Enum.EXECUTED.name().equals(p.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有任务才可提交");
                }
                updateDetailList.add(p);
            }

            updateDetailList.forEach(d->{
                d.setProgress(MeasureProgressEnum.Enum.WAIT_VERIFY.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            Report8D report8D = fabosJsonDao.findById(Report8D.class, myPreventMeasureTask.getId());
            if(checkCurrentStatus(report8D)){
                report8D.setPreventStatus(MeasureCompleteStatusEnum.Enum.FINISH.name());
                fabosJsonDao.mergeAndFlush(report8D);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(Report8D report8D) {
        for (PreventMeasures preventMeasures : report8D.getPreventMeasuresList()) {
            if (!MeasureProgressEnum.Enum.WAIT_VERIFY.name().equals(preventMeasures.getProgress())) {
                return false;
            }
        }
        return true;
    }
}
