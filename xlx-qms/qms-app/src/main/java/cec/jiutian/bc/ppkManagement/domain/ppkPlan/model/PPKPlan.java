package cec.jiutian.bc.ppkManagement.domain.ppkPlan.model;

import cec.jiutian.bc.cmkManagement.enumeration.GatherTimingTypeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler.PPKPlanCloseOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler.PPKPlanDetailReferenceAddHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler.PPKPlanGenerateTaskOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler.PPKPlanPublishOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.proxy.PPKPlanDataProxy;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@FabosJson(
        name = "PPK计划",
        orderBy = "createTime desc",
        dataProxy = PPKPlanDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "currentState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "currentState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "PPKPlan@PUBLISH",
                        operationHandler = PPKPlanPublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PPKPlan@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "任务生成",
                        code = "PPKPlan@TASK",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = PPKPlanGenerateTask.class,
                        operationHandler = PPKPlanGenerateTaskOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "PPKPlan@TASK"
                        ),
                        ifExpr = "currentState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "关闭",
                        code = "PPKPlan@CLOSE",
                        operationHandler = PPKPlanCloseOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PPKPlan@CLOSE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "currentState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "qms_ppk_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class PPKPlan extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.PPKPlan.name();
    }

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "id"))
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间"),
            edit = @Edit(title = "车间",
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",
                    filter = @Filter(value = "factoryAreaTypeCode = '03'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线"),
            edit = @Edit(title = "产线",
                    search = @Search(vague = true),
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "产品", column = "name"),
            edit = @Edit(title = "产品", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    @ManyToOne
    @JoinColumn(name = "specification_manage_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    private String materialSpecification;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "实施时间", type = ViewType.DATE),
            edit = @Edit(title = "实施时间", notNull = true, dateType = @DateType(type = DateType.Type.DATE))
    )
    private LocalDate applyTime;

    @FabosJsonField(
            views = @View(title = "采集时机"),
            edit = @Edit(title = "采集时机", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = GatherTimingTypeEnum.class))
    )
    private String gatherTiming;

    @FabosJsonField(
            views = @View(title = "其他采集时机"),
            edit = @Edit(title = "其他采集时机", notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "gatherTiming == 'Other'"))
    )
    private String otherGatherTiming;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "ppk_plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "设备详情", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "EquipmentArchiveMTO",
                    editable = {"getSamplePoint", "sendSamplePoint", "judgeItem", "sampleWeight", "sampleCount", "getSampleWay", "packageRequire"},
                    referenceAddHandler = PPKPlanDetailReferenceAddHandler.class),
            views = @View(title = "设备详情", type = ViewType.TABLE_VIEW, extraPK = "equipmentArchiveId", column = "name")
    )
    private List<PPKPlanEquipmentDetail> details;
}
