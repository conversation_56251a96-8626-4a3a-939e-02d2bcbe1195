package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.enumration.ArmBusinessStatusEnum;
import cec.jiutian.bc.accidentReportManagement.domain.accidentReportTask.model.AccidentReportTask;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.CpmBusinessStateEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.enumration.RelatedDocumentTypeEnum;
import cec.jiutian.bc.correctPreventMeasureManagement.domain.correctPreventMeasure.model.CorrectPreventMeasure;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.enumration.MRBAnalysisMethodEnum;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewDisposalPlanMTO;
import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.domain.otherDellTask.model.OtherDellTask;
import cec.jiutian.bc.qualityExceptionManagement.domain.reworkTask.model.ReworkTask;
import cec.jiutian.bc.qualityExceptionManagement.enums.ReworkTaskStatusEnum;
import cec.jiutian.bc.qualityExceptionManagement.enums.TaskTypeEnum;
import cec.jiutian.bc.questionPaperManagement.domain.questionPaper.model.QuestionPaper;
import cec.jiutian.bc.questionPaperManagement.enums.QuestionPaperStateEnum;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.bc.specialMaterialHandle.domain.model.SpecialMaterialHandleRequest;
import cec.jiutian.bc.supplierManagement.domain.problemRectificationList.model.ProblemRectification;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description
 */
@Component
@Slf4j
public class MRBUnqualifiedDisposalPlanHandler implements OperationHandler<MRBUnqualifiedReview, MRBUnqualifiedReviewDisposalPlanMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    private String doucumentName = "MRB不合格评审";

    @Override
    @Transactional
    public String exec(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewDisposalPlanMTO modelObject, String[] param) {
        if (modelObject != null) {
            MRBUnqualifiedReview model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.TO_BE_SUBMITTED.name());
            //创建分析方法
            model.setAnalysisMethodDocument(createAnalysisMethod(model));
            //创建处理意见
            model.setHandlingSuggestionDocument(createDisposalTaskDocument(model));
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public MRBUnqualifiedReviewDisposalPlanMTO fabosJsonFormValue(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewDisposalPlanMTO fabosJsonForm, String[] param) {
        MRBUnqualifiedReview model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private String createAnalysisMethod(MRBUnqualifiedReview model) {
        //根据分析方法（预防纠正措施表、8D、一页纸、事故报告、供应商问题解决报告），创建对应方法的单据，并关联单号
        String orderNumber = null;
        MRBAnalysisMethodEnum.Enum analysisMethodType = MRBAnalysisMethodEnum.Enum.valueOf(model.getAnalysisMethod());
        switch (analysisMethodType) {
            case Measure:
                orderNumber = createCorrectMeasureByReturnHandling(model);
                break;
            case EightD:
                orderNumber = createReport8D(model);
                break;
            case Paper:
                orderNumber = createQuestionPaper(model);
                break;
            case Report:
                orderNumber = createAccidentReportTask(model);
                break;
            case SupplierReport:
                orderNumber = creatProblemRectification(model);
                break;
        }
        return orderNumber;
    }

    private String createDisposalTaskDocument(MRBUnqualifiedReview model) {
        String orderNumber = null;
        String opinion = model.getHandlingSuggestion();
        if (opinion.equals(MRBDisposalOpinionEnum.Enum.REWORK.name())) {
            orderNumber = createReworkTaskByAfHandling(model);
        } else if (opinion.equals(MRBDisposalOpinionEnum.Enum.SCRAPPED.name()) ||
                opinion.equals(MRBDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSB.name()) ||
                opinion.equals(MRBDisposalOpinionEnum.Enum.DOWNGRADED_TO_CLASSC.name())) {
            orderNumber = createSpecialMaterialHandleRequestByAfHandling(model);
        } else {
            orderNumber = createOtherTaskByReturnHandling(model);
        }
        return orderNumber;
    }

    public String createCorrectMeasureByReturnHandling(MRBUnqualifiedReview model) {
        CorrectPreventMeasure measure = new CorrectPreventMeasure();
        measure.setCorrectPreventMeasureFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.CORRECT_PREVENT_MEASURE.name(), 1, null).get(0));
        measure.setRelatedDocumentType(RelatedDocumentTypeEnum.Enum.MRBUnqualifiedReview.name());
        measure.setRelatedDocumentMTOCode(model.getGeneralCode());
        measure.setRelatedDocumentMTOName(model.getGeneralCode());
        measure.setNonConformanceStatement(doucumentName + "单号：" + model.getGeneralCode());
        measure.setBusinessState(CpmBusinessStateEnum.Enum.TO_BE_RELEASED.name());
//        measure.setVerificationNonConformity(model.getCauseAnalysis());
        fabosJsonDao.saveOrUpdate(measure);

        return measure.getCorrectPreventMeasureFormNumber();
    }

    private String createReport8D(MRBUnqualifiedReview model) {
        Report8D report8D = new Report8D();
        report8D.setGeneralCode(namingRuleService.getNameCode(cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum.Report8D.name(), 1, null).get(0));
        report8D.setTitle(doucumentName + "单号：" + model.getGeneralCode());
        report8D.setOccurrenceTime(model.getDetectCompleteTime());
        report8D.setProductCode(model.getLotSerialId());
        report8D.setProductName(model.getMaterialName());
        report8D.setTotalBatchQuantity(model.getQuantity().intValue());
        report8D.setRaisedDate(new Date());
        report8D.setReporterId(UserContext.getUserId());
        report8D.setReporter(UserContext.getUserName());
        report8D.setContactPersonId(UserContext.getUserId());
        report8D.setContactPerson(UserContext.getUserName());
        report8D.setStatus(Report8DStatusEnum.Enum.WAIT_SUBMIT.name());
        report8D.setTempStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
        report8D.setLongStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
        report8D.setPreventStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
        fabosJsonDao.persistAndFlush(report8D);
        return report8D.getGeneralCode();
    }

    private String createQuestionPaper(MRBUnqualifiedReview model) {
        QuestionPaper questionPaper = new QuestionPaper();
        questionPaper.setGeneralCode(namingRuleService.getNameCode(questionPaper.getNamingCode(), 1, questionPaper.getParameters()).get(0));
        questionPaper.setQuestionDescription(doucumentName + "单号：" + model.getGeneralCode());
        questionPaper.setCurrentState(QuestionPaperStateEnum.Enum.Edit.name());
        questionPaper.setImproveFlag(false);
        questionPaper.setInterimFlag(false);
        fabosJsonDao.mergeAndFlush(questionPaper);
        return questionPaper.getGeneralCode();
    }

    private String createAccidentReportTask(MRBUnqualifiedReview model) {
        AccidentReportTask reportTask = new AccidentReportTask();
        reportTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.ACCIDENT_REPORT_TASK.name(), 1, null).get(0));
        reportTask.setAccidentDescription(doucumentName + "单号：" + model.getGeneralCode());
        reportTask.setBusinessStatus(ArmBusinessStatusEnum.Enum.TO_BE_CONFIRMED.name());
        fabosJsonDao.mergeAndFlush(reportTask);
        return reportTask.getGeneralCode();
    }

    private String creatProblemRectification(MRBUnqualifiedReview model) {
        ProblemRectification problemRectification = new ProblemRectification();
        problemRectification.setGeneralCode(namingRuleService.getNameCode(problemRectification.getNamingCode(), 1, problemRectification.getParameters()).get(0));
        problemRectification.setRemark(doucumentName + "单号：" + model.getGeneralCode());

        fabosJsonDao.mergeAndFlush(problemRectification);
        return problemRectification.getGeneralCode();
    }

    public String createReworkTaskByAfHandling(MRBUnqualifiedReview model) {
        ReworkTask reworkTask = new ReworkTask();
        reworkTask.setGeneralCode(namingRuleService.getNameCode(reworkTask.getNamingCode(), 1, reworkTask.getParameters()).get(0));
        reworkTask.setTaskType(TaskTypeEnum.Enum.MRB.name());
        reworkTask.setTaskCode(model.getGeneralCode());
        reworkTask.setProductCode(model.getMaterialCode());
        reworkTask.setProductName(model.getMaterialName());
        reworkTask.setBatchCode(model.getLotSerialId());
        reworkTask.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        reworkTask.setReworkQuantity(model.getQuantity());
        reworkTask.setExamineStatus("0");

        fabosJsonDao.mergeAndFlush(reworkTask);
        log.info("ReworkTask create success.");
        return reworkTask.getGeneralCode();
    }

    private String createSpecialMaterialHandleRequestByAfHandling(MRBUnqualifiedReview model) {
        log.info("Create SpecialMaterialHandleRequest");
        UserContext.CurrentUser currentUser = UserContext.get();
        SpecialMaterialHandleRequest materialHandleRequest = new SpecialMaterialHandleRequest();
        materialHandleRequest.setGeneralCode(namingRuleService.getNameCode(materialHandleRequest.getNamingCode(), 1, materialHandleRequest.getParameters()).get(0));
        String userId = currentUser.getUserId();
        materialHandleRequest.setApplicantId(userId);
        materialHandleRequest.setApplicant(currentUser.getUserName());

        Org condition = new Org();
        condition.setId(currentUser.getOrgId());
        Org org = fabosJsonDao.selectOne(condition);
        materialHandleRequest.setDepartmentId(org.getId());
        materialHandleRequest.setApplyDepartment(org.getName());

        materialHandleRequest.setBusinessState("EDIT");
        fabosJsonDao.mergeAndFlush(materialHandleRequest);
        log.info("SpecialMaterialHandleRequest create success.");
        return materialHandleRequest.getGeneralCode();
    }

    public String createOtherTaskByReturnHandling(MRBUnqualifiedReview model) {
        OtherDellTask task = new OtherDellTask();
        task.setGeneralCode(namingRuleService.getNameCode(task.getNamingCode(), 1, task.getParameters()).get(0));
        task.setPlan(model.getHandlingSuggestion());
        task.setTaskType(TaskTypeEnum.Enum.ABNORMAL_FEEDBACK_HANDLING.name());
        task.setTaskCode(model.getGeneralCode());
        task.setProductCode(model.getMaterialCode());
        task.setProductName(model.getMaterialName());
        task.setBatchCode(model.getLotSerialId());
        task.setStatus(ReworkTaskStatusEnum.Enum.CREATE.name());
        task.setReworkQuantity(model.getQuantity());
        task.setExamineStatus("0");

        fabosJsonDao.mergeAndFlush(task);
        log.info("OtherDellTask create success.");
        return task.getGeneralCode();
    }
}
