package cec.jiutian.bc.report8D.domain.myTempMeasureTask.handler;

import cec.jiutian.bc.report8D.domain.myTempMeasureTask.model.MyTempMeasure;
import cec.jiutian.bc.report8D.domain.myTempMeasureTask.model.MyTempMeasureTask;
import cec.jiutian.bc.report8D.domain.myTempMeasureTask.model.MyTempMeasureTaskExec;
import cec.jiutian.bc.report8D.domain.report8D.model.TempMeasure;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class MyTempMeasureTaskExecHandler implements OperationHandler<MyTempMeasureTask, MyTempMeasureTaskExec> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyTempMeasureTask> data, MyTempMeasureTaskExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getMyTempMeasureList().forEach(d->{
                TempMeasure tempMeasure = fabosJsonDao.findById(TempMeasure.class, d.getId());
                tempMeasure.setProgress(d.getProgress());
                tempMeasure.setCompletionDate(d.getCompletionDate());
                tempMeasure.setCompletionEvidence(d.getCompletionEvidence());
                fabosJsonDao.mergeAndFlush(tempMeasure);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public MyTempMeasureTaskExec fabosJsonFormValue(List<MyTempMeasureTask> data, MyTempMeasureTaskExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyTempMeasureTask myTempMeasureTask = data.get(0);
        BeanUtil.copyProperties(myTempMeasureTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myTempMeasureTask.getTempMeasureList())) {
            return fabosJsonForm;
        }

        List<MyTempMeasure> myTempMeasureList = new ArrayList<>();
        myTempMeasureTask.getTempMeasureList().forEach(d->{
            if (Objects.equals(d.getResponsiblePersonId(), userId)) {
                MyTempMeasure myTempMeasure = new MyTempMeasure();
                BeanUtil.copyProperties(d, myTempMeasure);
                myTempMeasureList.add(myTempMeasure);
            }
        });

        fabosJsonForm.setMyTempMeasureList(myTempMeasureList);
        return fabosJsonForm;
    }
}
