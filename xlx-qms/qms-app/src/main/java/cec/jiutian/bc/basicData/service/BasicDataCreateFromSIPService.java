package cec.jiutian.bc.basicData.service;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetail;
import cec.jiutian.bc.basicData.dto.MsSipDetailResultDTO;
import cec.jiutian.bc.basicData.dto.MsSipResultDTO;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.TargetTypeEnum;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.inspectionInstrument.model.InspectionInstrument;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItemTarget;
import cec.jiutian.bc.modeler.domain.inspectionMethod.InspectionMethodRepository;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.domain.samplingPlan.repository.SamplingPlanRepository;
import cec.jiutian.bc.modeler.enumration.ComparisonMethodEnum;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static java.util.stream.Collectors.groupingBy;

/**
 * SIP创建基础数据接口
 */
@Slf4j
@Service
public class BasicDataCreateFromSIPService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private InspectionMethodRepository inspectionMethodRepository;

    @Resource
    private SamplingPlanRepository samplingPlanRepository;

    @Resource
    private NamingRuleService namingRuleService;

    private final static String IQC_PROCESS_NAME = "来料";

    private final static String OQC_PROCESS_NAME = "发货";

    @Transactional
    public boolean createBasicData(MsSipResultDTO msSipResultDTO) {
        if (msSipResultDTO == null || CollectionUtils.isEmpty(msSipResultDTO.getDetailList())) {
            throw new ServiceException("同步数据为空");
        }

        //分组
        Map<List<String>, List<MsSipDetailResultDTO>> grouped = groupProcessStep(msSipResultDTO.getDetailList());
        InspectionStandard inspectionStandard = createInspectionStandard(msSipResultDTO);
        ArrayList<InspectionStandardDetail> standardDetails = new ArrayList<>();
        for (List<MsSipDetailResultDTO> value : grouped.values()) {
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            InspectionItem inspectionItem = createInspectionItem(value.get(0));
            inspectionItem.setSipName(msSipResultDTO.getSipName());
            inspectionItem.setSipVersion(msSipResultDTO.getSipVersion());

            ArrayList<InspectionItemTarget> itemTargets = new ArrayList<>();
            for (MsSipDetailResultDTO msSipDetailResultDTO : value) {
                InspectionItemTarget itemTarget = createInspectionItemTarget(msSipDetailResultDTO);
                itemTarget.setInspectionItem(inspectionItem);
                itemTargets.add(itemTarget);
            }
            inspectionItem.setInspectionItemTargetList(itemTargets);
            fabosJsonDao.persistAndFlush(inspectionItem);
            InspectionStandardDetail standardDetail = createInspectionStandardDetail(value.get(0));
            standardDetail.setInspectionItem(inspectionItem);
            standardDetail.setInspectionStandard(inspectionStandard);
            standardDetails.add(standardDetail);
        }
        inspectionStandard.setDetails(standardDetails);
        fabosJsonDao.mergeAndFlush(inspectionStandard);
        return true;
    }

    private void UpdateSIPInformationToInspectionItem(MsSipResultDTO dto) {
        InspectionItem query = new InspectionItem();
        List<InspectionItem> list = fabosJsonDao.select(query);
        if (!CollectionUtils.isEmpty(list)) {
            for (InspectionItem item : list) {
                item.setSipName(dto.getSipName());
                item.setSipVersion(dto.getSipVersion());
            }
            fabosJsonDao.updateBatchById(list);
        }
    }

    static final String hql = "from SpecificationManageMTO where code = :code and name = :name";

    static final String standardHql = "from InspectionStandard where name = :name";

    private InspectionStandard createInspectionStandard(MsSipResultDTO msSipResultDTO) {

        String standardName = msSipResultDTO.getSipName() + msSipResultDTO.getSipVersion();
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        TypedQuery<InspectionStandard> query = entityManager.createQuery(standardHql, InspectionStandard.class);
        query.setMaxResults(1);
        query.setParameter("name", standardName);
        List<InspectionStandard> result = query.getResultList();
        if (CollectionUtils.isNotEmpty(result)) {
            throw new ServiceException("标准已存在");
        }

        InspectionStandard standard = new InspectionStandard();
        standard.setName(standardName);
        standard.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionStandard.name(), 1, null).get(0));
        standard.setMaterialCode(msSipResultDTO.getSpecificationCode());
        standard.setMaterialName(msSipResultDTO.getSpecificationName());
        if (msSipResultDTO.getProcessName() != null && msSipResultDTO.getProcessName().indexOf(IQC_PROCESS_NAME) > 0) {
            standard.setType(StandardTypeEnum.Enum.IQC.name());
        } else if (msSipResultDTO.getProcessName() != null && msSipResultDTO.getProcessName().indexOf(OQC_PROCESS_NAME) > 0) {
            standard.setType(StandardTypeEnum.Enum.OQC.name());
        } else {
            standard.setType(StandardTypeEnum.Enum.IPQC.name());
        }
        standard.setFactoryAreaId(msSipResultDTO.getWorkshopId() != null ? msSipResultDTO.getWorkshopId().toString() : null);
        standard.setFactoryAreaName(msSipResultDTO.getWorkshopName());
        standard.setFactoryLineId(msSipResultDTO.getProductionLineId() != null ? msSipResultDTO.getProductionLineId().toString() : null);
        standard.setFactoryLineName(msSipResultDTO.getProductionLineName());
        standard.setProcessCode(msSipResultDTO.getProcessCode());
        standard.setProcessName(msSipResultDTO.getProcessName());
        standard.setKeepSampleFlag(YesOrNoEnum.Enum.N.name());
        standard.setStatus(StatusEnum.Enum.Invalid.name());
        return standard;
    }

    private InspectionStandardDetail createInspectionStandardDetail(MsSipDetailResultDTO msSipDetailResultDTO) {
        InspectionStandardDetail detail = new InspectionStandardDetail();
        detail.setOperationCode(msSipDetailResultDTO.getProcessOperationCode());
        detail.setOperationName(msSipDetailResultDTO.getProcessOperationName());
        InspectionItem inspectionItem = new InspectionItem();
        getInspectFrequency(msSipDetailResultDTO.getInspectFrequency(), inspectionItem);
        detail.setInspectFrequency(inspectionItem.getInspectFrequency());
        detail.setInspectFrequencyUnit(inspectionItem.getAccountUnit().getUnitChnName());
        //todo: 待确认这两个字段是否需要，暂时设置为false
//        detail.setIsOpenLine(YNMap.get(msSipDetailResultDTO.getOpenLineFlag()));
//        detail.setIsRawMaterial(YNMap.get(msSipDetailResultDTO.getMaterialUpdateFlag()));
        detail.setIsOpenLine(false);
        detail.setIsRawMaterial(false);

//        if (StringUtils.isNotBlank(msSipDetailResultDTO.getSampleQuantity())) {
//            try {
//                Double sampleNum = Double.valueOf(msSipDetailResultDTO.getSampleQuantity());
//                detail.setSampleQuantity(sampleNum);
//            } catch (NumberFormatException e) {
//                log.error("sampleNum error:{}", msSipDetailResultDTO.getSampleQuantity());
//            }
//        } else {
//            detail.setSampleQuantity(0.0);
//        }
//        detail.setIsOpenDevice();
        return detail;
    }


    private InspectionItem createInspectionItem(MsSipDetailResultDTO msSipDetailResultDTO) {
        InspectionItem inspectionItem = new InspectionItem();
        inspectionItem.setName(msSipDetailResultDTO.getItemName());
        inspectionItem.setManageMethod(msSipDetailResultDTO.getInspectScheme());
        inspectionItem.setMethod(msSipDetailResultDTO.getInspectScheme());
        inspectionItem.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionItem.name(), 1, null).get(0)));
//        String hql = "from SendPointMTO spm where spm.code =:code";
//        TypedQuery<SendPointMTO> query = fabosJsonDao.getEntityManager().createQuery(hql, SendPointMTO.class);
//        query.setParameter("code", msSipDetailResultDTO.getDeliverInspectPoint());
//        List<SendPointMTO> list = query.getResultList();
//        if (CollectionUtils.isNotEmpty(list)) {
//            inspectionItem.setSendPointMTO(list.get(0));
//        }
        inspectionItem.setSamplingPoint(msSipDetailResultDTO.getSamplePoint());
        //todo: 待确认这两个字段是否需要，暂时设置为false
//        inspectionItem.setIsOpenLine(YNMap.get(msSipDetailResultDTO.getOpenLineFlag()));
//        inspectionItem.setIsRawMaterial(YNMap.get(msSipDetailResultDTO.getMaterialUpdateFlag()));
        inspectionItem.setIsOpenLine(false);
        inspectionItem.setIsRawMaterial(false);

        inspectionItem.setPackageType(null);
        getInspectFrequency(msSipDetailResultDTO.getInspectFrequency(), inspectionItem);
        inspectionItem.setIsAutoJudged(null);

        inspectionItem.setStatus(StatusEnum.Enum.Effective.name());
        inspectionItem.setMethod(msSipDetailResultDTO.getInspectScheme());
        inspectionItem.setInspectionMethod(createInspectionMethod(msSipDetailResultDTO));
        inspectionItem.setPlan(msSipDetailResultDTO.getSampleScheme());

        inspectionItem.setSamplingPlan(createSamplingPlan(msSipDetailResultDTO));

        return inspectionItem;
    }


    private SamplingPlan createSamplingPlan(MsSipDetailResultDTO msSipDetailResultDTO) {

        if (StringUtils.isBlank(msSipDetailResultDTO.getSampleScheme())) {
            return null;
        }

        List<SamplingPlan> plans = samplingPlanRepository.findByName(msSipDetailResultDTO.getSampleScheme());
        if (CollectionUtils.isNotEmpty(plans)) {
            return plans.get(0);
        }
        SamplingPlan plan = new SamplingPlan();
        plan.setName(msSipDetailResultDTO.getSampleScheme());
        plan.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.SamplingPlan.name(), 1, null).get(0)));
        plan.setSamplingStandard(msSipDetailResultDTO.getSampleStandard());
        plan.setFixedCount(msSipDetailResultDTO.transSampleQuantityNumber());
        MesUnit mesUnit = queryUnitByName(msSipDetailResultDTO.getSampleQuantityUnit()).get(0);
        plan.setUnit(mesUnit);
        plan.setDeviationScope(msSipDetailResultDTO.getSampleQuantityDeviation());
        plan.setName(msSipDetailResultDTO.getSampleQuantityUnit());
        plan.setUnitCode(mesUnit.getCode());
        plan.setUnitId(mesUnit.getId());
        plan.setStatus(StatusEnum.Enum.Effective.name());
        fabosJsonDao.mergeAndFlush(mesUnit);
        return plan;
    }

    static final String unitNameQuery = "from MesUnit where name = :name";

    private List<MesUnit> queryUnitByName(String unitName) {
        TypedQuery<MesUnit> query = fabosJsonDao.getEntityManager().createQuery(unitNameQuery, MesUnit.class);
        query.setParameter("name", unitName);
        return query.getResultList();
    }

    /**
     * 创建检验方法
     */
    private static final String instrumentNameQuery = "from InspectionInstrument where name = :name";

    private InspectionMethod createInspectionMethod(MsSipDetailResultDTO msSipDetailResultDTO) {
        if (StringUtils.isBlank(msSipDetailResultDTO.getInspectScheme())) {
            return null;
        }
        List<InspectionMethod> methods = inspectionMethodRepository.findByName(msSipDetailResultDTO.getInspectScheme());
        InspectionMethod inspectionMethod = CollectionUtils.isEmpty(methods) ? null : methods.get(0);
        if (inspectionMethod != null) {
            return inspectionMethod;
        }
        inspectionMethod = new InspectionMethod();
        inspectionMethod.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionMethodCode.name(), 1, null).get(0)));
        inspectionMethod.setName(msSipDetailResultDTO.getInspectScheme());
        inspectionMethod.setIsDestructiveTest(false);
        inspectionMethod.setStatus(StatusEnum.Enum.Effective.name());
        inspectionMethod.setInspectStepDescription(msSipDetailResultDTO.getInspectSchemeDesc());

        inspectionMethod.setInspectionInstrumentList(createInspectionInstrument(msSipDetailResultDTO));

        fabosJsonDao.mergeAndFlush(inspectionMethod);
        return inspectionMethod;
    }


    private List<InspectionInstrument> createInspectionInstrument(MsSipDetailResultDTO msSipDetailResultDTO) {
        if (StringUtils.isEmpty(msSipDetailResultDTO.getInstrument())) {
            return new ArrayList<>(0);
        }
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        List<InspectionInstrument> inspectionInstruments = entityManager.createQuery(instrumentNameQuery, InspectionInstrument.class)
                .setParameter("name", msSipDetailResultDTO.getInstrument()).getResultList();
        if (CollectionUtils.isNotEmpty(inspectionInstruments)) {
            return inspectionInstruments;
        }
        ArrayList<InspectionInstrument> instruments = new ArrayList<>(1);
        InspectionInstrument inspectionInstrument = new InspectionInstrument();
        inspectionInstrument.setName(msSipDetailResultDTO.getInstrument());
        inspectionInstrument.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionInstrument.name(), 1, null).get(0)));
        inspectionInstrument.setStatus(StatusEnum.Enum.Effective.name());
        fabosJsonDao.mergeAndFlush(inspectionInstrument);
        instruments.add(inspectionInstrument);
        return instruments;
    }

    private void getInspectFrequency(String inspectFrequency, InspectionItem inspectionItem) {
        int i = 1;
        MeasureUnit condition = new MeasureUnit();
        condition.setUnitChnName("批");
        MeasureUnit measureUnit = fabosJsonDao.selectOne(condition);
        inspectionItem.setInspectFrequency(i);
        inspectionItem.setAccountUnit(measureUnit);
        if (inspectFrequency == null) {
            return;
        }
        try {
            int index = inspectFrequency.indexOf("/");
            if (index != -1) {
                String frequencyStr = inspectFrequency.substring(index, index + 1);
                String frequencyUnit = inspectFrequency.substring(index + 1, index + 2);
                MeasureUnit condition1 = new MeasureUnit();
                condition1.setUnitChnName(frequencyUnit);
                MeasureUnit measureUnit1 = fabosJsonDao.selectOne(condition1);
                if (measureUnit1 != null) {
                    inspectionItem.setAccountUnit(measureUnit1);
                }
                if (frequencyStr.matches("\\d+")) {
                    i = Integer.parseInt(frequencyStr);
                    inspectionItem.setInspectFrequency(i);
                }
            }
        } catch (NumberFormatException e) {
            log.error("获取检验频率失败", e.getMessage());
        }
    }

    private final static HashMap<String, Boolean> YNMap = new HashMap<String, Boolean>() {{
        put("Y", true);
        put("N", false);
    }};


    private InspectionItemTarget createInspectionItemTarget(MsSipDetailResultDTO resultDTO) {
        InspectionItemTarget target = new InspectionItemTarget();
        target.setName(resultDTO.getStepName());
        TargetTypeEnum targetTypeEnum = TargetTypeEnum.getTargetTypeEnum(resultDTO.getUnitCode());
        target.setInspectionValueType(targetTypeEnum.getValue().name());
        String standardValue = transformStandardValue(resultDTO.getStandard(), targetTypeEnum);
        target.setStandardValue(String.valueOf(standardValue));
        target.setUnit(resultDTO.getUnitCode());

        Double deviation = transformDeviation(resultDTO.getDeviation(), targetTypeEnum);
        if (cec.jiutian.bc.modeler.enumration.InspectionValueTypeEnum.Enum.text.name().equals(targetTypeEnum.getValue().name())) {
            target.setComparisonMethod(null);
            target.setIsContainLower(false);
            target.setIsContainUpper(false);
        } else {
            String flag = StringUtils.isBlank(resultDTO.getDeviation())? "0" : resultDTO.getDeviation();
            if (flag.startsWith("±")) {
                target.setComparisonMethod(ComparisonMethodEnum.Enum.range.name());
                target.setUpperValue(Double.parseDouble(standardValue) + deviation);
                target.setLowerValue(Double.parseDouble(standardValue) - deviation);

                target.setIsContainLower(true);
                target.setIsContainUpper(true);
            }
            //小于等于
            else if (flag.startsWith("≤")){
                target.setComparisonMethod(ComparisonMethodEnum.Enum.range.name());
                target.setUpperValue(Double.parseDouble(standardValue));
                target.setLowerValue(Double.MIN_NORMAL);
                target.setIsContainLower(true);
                target.setIsContainUpper(true);
            }
            //大于等于
            else if (flag.startsWith("≥")){
                target.setComparisonMethod(ComparisonMethodEnum.Enum.range.name());
                target.setUpperValue(Double.MAX_VALUE);
                target.setLowerValue(Double.parseDouble(standardValue));
                target.setIsContainLower(true);
                target.setIsContainUpper(true);
            }
            //等于
            else if (flag.equals("0")){
                target.setComparisonMethod(ComparisonMethodEnum.Enum.equal.name());
                target.setUpperValue(Double.parseDouble(standardValue));
                target.setLowerValue(Double.parseDouble(standardValue));
                target.setIsContainLower(true);
                target.setIsContainUpper(true);
            }
        }

        return target;
    }

    private static Double transformDeviation(String deviationValue, TargetTypeEnum targetTypeEnum) {
        if (StringUtils.isBlank(deviationValue)) {
            return null;
        }
        deviationValue = deviationValue.trim().replace("±", "");
        switch (targetTypeEnum) {
            case TEXT:
                return null;
            case NUMBER:
                try {
                    return Double.valueOf(deviationValue);
                } catch (NumberFormatException e) {
                    log.error("转换偏差值失败", e.getMessage());
                    return null;
                }
            case PERCENTAGE:
                try {
                    return Double.valueOf(deviationValue.replace("%", ""));
                } catch (NumberFormatException e) {
                    log.error("转换偏差值失败", e.getMessage());
                    return null;
                }
            default:
                return null;
        }
    }

    private static String transformStandardValue(String standardValue, TargetTypeEnum targetTypeEnum) {
        if (standardValue == null) {
            return null;
        }
        switch (targetTypeEnum) {
            case TEXT:
                return standardValue;
            case NUMBER:
                try {
                    return standardValue;
                } catch (NumberFormatException e) {
                    log.error("转换标准值失败", e.getMessage());
                    return null;
                }
            case PERCENTAGE:
                try {
                    return standardValue.replace("%", "");
                } catch (NumberFormatException e) {
                    log.error("转换标准值失败", e.getMessage());
                    return null;
                }
            default:
                return null;
        }

    }

    private Map<List<String>, List<MsSipDetailResultDTO>> groupProcessStep(List<MsSipDetailResultDTO> detailList) {
        //ProcessStepDTO 这三个字段相同即视为一个检验项 workStepName/ workStepNumber/ projectName;
        Map<List<String>, List<MsSipDetailResultDTO>> group = detailList.stream()
                .collect(groupingBy(dto -> {
                    return Arrays.asList(
                            dto.getStepName(),
                            dto.getStepIndex() + "",
                            dto.getItemName()
                    );
                }));
        return group;
    }

}
