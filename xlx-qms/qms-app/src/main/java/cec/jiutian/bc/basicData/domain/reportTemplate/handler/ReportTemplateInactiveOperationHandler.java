package cec.jiutian.bc.basicData.domain.reportTemplate.handler;

import cec.jiutian.bc.basicData.domain.reportTemplate.model.ReportTemplate;
import cec.jiutian.bc.basicData.enumeration.ReportTemplateStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ReportTemplateInactiveOperationHandler implements OperationHandler<ReportTemplate, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<ReportTemplate> data, Void modelObject, String[] param) {

        if (!CollectionUtils.isEmpty(data)) {
            ReportTemplate reportTemplate = data.get(0);
            reportTemplate.setStatus(ReportTemplateStatus.INACTIVE.name());
            fabosJsonDao.updateAndFlush(reportTemplate);
        }

        return "msg.success('操作成功')";
    }
}
