package cec.jiutian.bc.basicData.domain.inspectionStandard.mto;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.PackageTypeEnum;
import cec.jiutian.bc.modeler.enumration.SendPointTypeEnum;
import cec.jiutian.bc.modeler.enumration.StatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "检验组项目信息"

)
@QueryModel(hql = "select new map(g.id as itemGroupId, " +
        "i.generalCode as generalCode, i.id as id,i.name as name,i.feature as feature, " +
        "i.inspectionMethod as inspectionMethod, " +
        "i.manageMethod as manageMethod,i.method as method, i.isAutoJudged as isAutoJudged, " +
        "i.packageType as packageType, i.samplingPlan as samplingPlan, i.plan as plan, i.inspectFrequency as inspectFrequency, " +
        "i.accountUnit as accountUnit, i.accountUnit.unitChnName as accountUnit_unitChnName, " +
        "i.isOpenLine as isOpenLine, i.isOpenDevice as isOpenDevice, i.isRawMaterial as isRawMaterial, " +
        "i.isResultWaiting as isResultWaiting, i.samplingPoint as samplingPoint, i.sendPointType as sendPointType, " +
        "i.sendPoint as sendPoint, i.sipName as sipName, i.sipVersion as sipVersion, i.status as status, i.remark as remark) " +
        "from InspectionItemGroup as g " +
        "join g.insItems as i " +
        "left outer join i.inspectionMethod as im " +
        "left outer join i.samplingPlan as sp " +
        "left outer join i.accountUnit ")
@Getter
@Setter
@Entity
public class InspectionItemGroupWithItemMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "检验组id", show = false),
            edit = @Edit(title = "检验组id")
    )
    private String itemGroupId;

    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号", notNull = true)
    )
    @SubTableField
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "检验项目名称"),
            edit = @Edit(title = "检验项目名称", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "特性"),
            edit = @Edit(title = "特性", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @SubTableField
    private String feature;

//    @FabosJsonField(
//            views = @View(title = "检验项类型"),
//            edit = @Edit(title = "检验项类型", type = EditType.CHOICE, notNull = true,
//                    inputType = @InputType(length = 40),
//                    choiceType = @ChoiceType(fetchHandler = ItemTypeEnum.class))
//    )
//    @SubTableField
//    private String itemType;

    @ManyToOne
    //@JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT, name = "none"))
    @FabosJsonField(
            views = @View(title = "检验方法",
                    show = false,
                    column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验方法",
                    filter = @Filter(value = "InspectionMethod.status = 'Effective'"),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    @SubTableField
    private InspectionMethod inspectionMethod;

    @FabosJsonField(
            views = @View(title = "管理方案"),
            edit = @Edit(title = "管理方案", search = @Search(vague = true))
    )
    @SubTableField
    private String manageMethod;

    @FabosJsonField(
            views = @View(title = "检验方法"),
            edit = @Edit(title = "检验方法",
                    readonly = @Readonly(edit = true, add = true),
                    type = EditType.TEXTAREA),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(
                    changeBy = "inspectionMethod", beFilledBy = "name"))
    )
    @SubTableField
    private String method;

    @FabosJsonField(
            views = @View(title = "是否自动判断"),
            edit = @Edit(title = "是否自动判断", defaultVal = "false"
            )
    )
    private Boolean isAutoJudged;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = PackageTypeEnum.class))
    )
    private String packageType;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案",
                    show = false,
                    column = "name",
                    type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "SamplingPlan.status = 'Effective'"),
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search()
            )
    )
    private SamplingPlan samplingPlan;

    @FabosJsonField(
            views = @View(title = "抽样方案"),
            edit = @Edit(title = "抽样方案",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly(edit = true, add = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(
                    changeBy = "samplingPlan", beFilledBy = "name"))
    )
    private String plan;

    @FabosJsonField(
            views = @View(title = "检验频次"),
            edit = @Edit(title = "检验频次", notNull = true,
                    inputGroup = @InputGroup(postfix = "#{accountUnit},/次"),
                    numberType = @NumberType(min = 1, max = 10))
    )
    private Integer inspectFrequency;

    @ManyToOne(cascade = CascadeType.DETACH)
    @FabosJsonField(
            views = @View(title = "检验频次单位", column = "unitChnName"),
            edit = @Edit(title = "检验频次单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    private MeasureUnit accountUnit;

    @FabosJsonField(
            views = @View(title = "是否开线首检"),
            edit = @Edit(title = "是否开线首检", defaultVal = "false"
            )
    )
    private Boolean isOpenLine;

    @FabosJsonField(
            views = @View(title = "是否开机首检"),
            edit = @Edit(title = "是否开机首检", defaultVal = "false"
            )
    )
    private Boolean isOpenDevice;

    @FabosJsonField(
            views = @View(title = "是否原材料首检"),
            edit = @Edit(title = "是否原材料首检", defaultVal = "false"
            )
    )
    private Boolean isRawMaterial;

    @FabosJsonField(
            views = @View(title = "是否结果等待"),
            edit = @Edit(title = "是否结果等待", defaultVal = "false"
            )
    )
    private Boolean isResultWaiting;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点", notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String samplingPoint;

    @FabosJsonField(
            views = @View(title = "送检点类型"),
            edit = @Edit(title = "送检点类型", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SendPointTypeEnum.class))
    )
    private String sendPointType;

    @FabosJsonField(
            views = @View(title = "送检点"),
            edit = @Edit(title = "送检点",
                    inputType = @InputType(length = 40)
            )
    )
    @Column(length = 40)
    private String sendPoint;

    @FabosJsonField(
            views = @View(title = "SIP编号"),
            edit = @Edit(title = "SIP编号", show = false)
    )
    @SubTableField
    private String sipName;

    @FabosJsonField(
            views = @View(title = "SIP版本"),
            edit = @Edit(title = "SIP版本", show = false)
    )
    @SubTableField
    private String sipVersion;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA)
    )
    private String remark;

//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
//    @JoinColumn(name = "inspection_item_id")
//    @FabosJsonField(
//            views = @View(title = "检验项指标", type = ViewType.TABLE_VIEW),
//            edit = @Edit(title = "检验项指标", type = EditType.TAB_TABLE_ADD)
//    )
//    private List<InspectionItemTarget> inspectionItemTargetList;

}
