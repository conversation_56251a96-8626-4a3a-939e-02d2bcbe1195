package cec.jiutian.bc.basicData.domain.inspectionStandard.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetailIPQC;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardIPQC;
import cec.jiutian.bc.mto.ProcessOperationMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class IPQCStandardDetailDynamicHandler implements ReferenceAddType.ReferenceAddHandler<InspectionStandardIPQC, ProcessOperationMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InspectionStandardIPQC entity, List<ProcessOperationMTO> operationMTOList) {
        Map<String, Object> result = new HashMap<>();
        List<InspectionStandardDetailIPQC> list = new ArrayList<>();
        if (entity != null && CollectionUtils.isNotEmpty(operationMTOList)) {
            if (null == entity.getMesUnit() || null == entity.getTechnologyFlow()) {
                throw new FabosJsonApiErrorTip("先选择工艺流程和样品单位");
            }
            for (ProcessOperationMTO operationMTO : operationMTOList) {
                InspectionStandardDetailIPQC detail = new InspectionStandardDetailIPQC();
                detail.setOperationCode(operationMTO.getProcessOperationCode());
                detail.setOperationName(operationMTO.getProcessOperationName());
                detail.setSampleUnitId(entity.getSampleUnitId());
                list.add(detail);
            }
        }
        result.put("details", list);
        return result;
    }
}
