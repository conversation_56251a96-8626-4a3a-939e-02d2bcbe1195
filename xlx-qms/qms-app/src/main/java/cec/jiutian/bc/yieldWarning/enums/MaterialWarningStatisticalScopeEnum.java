package cec.jiutian.bc.yieldWarning.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MaterialWarningStatisticalScopeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {

        Single("单个供应商"),
        All("全部供应商"),
        ;
        private String value;

    }

    public static String getValueByKey(String key) {
        for (Enum e : Enum.values()) {
            if (e.name().equalsIgnoreCase(key)) {
                return e.getValue();
            }
        }
        return null;
    }
}
