package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto.CMKSampleTaskGetMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/3/20 14:35
 * @description：
 */
@Component
public class CMKSampleGetDynamicHandler implements DependFiled.DynamicHandler<CMKSampleTaskGetMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(CMKSampleTaskGetMTO cmkSampleTaskGetMTO) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(cmkSampleTaskGetMTO.getGeneralCode())) {
            CMKSampleTask condition = new CMKSampleTask();
            condition.setGeneralCode(cmkSampleTaskGetMTO.getGeneralCode());
            CMKSampleTask samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("generalCode",samplingTask.getGeneralCode());
            result.put("cmkInspectionTaskCode",samplingTask.getCmkInspectionTaskCode());
            result.put("processName", samplingTask.getProductProcessMTO().getName());
            result.put("materialName", samplingTask.getMaterialName());
            result.put("materialCode", samplingTask.getMaterialCode());
            result.put("sampleWeight", samplingTask.getSampleWeight());
        }
        return result;
    }
}
