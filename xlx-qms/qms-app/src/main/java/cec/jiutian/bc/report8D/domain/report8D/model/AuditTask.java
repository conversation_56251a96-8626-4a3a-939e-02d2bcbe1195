package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.bc.report8D.domain.report8D.handler.AuditTaskAuditHandler;
import cec.jiutian.bc.report8D.domain.report8D.proxy.AuditTaskDataProxy;
import cec.jiutian.bc.report8D.enums.AuditResultEnum;
import cec.jiutian.bc.report8D.enums.AuditTaskStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report_audit_task")
@FabosJson(
        name = "8D审核任务",
        orderBy = "AuditTask.createTime desc",
        dataProxy = AuditTaskDataProxy.class,
        power = @Power(add = false, edit = false, export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status != 'COMPLETE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1 == 1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "审核",
                        code = "AuditTask@Audit",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = AuditTaskAuditHandler.class,
                        fabosJsonClass = AuditTaskAudit.class,
                        ifExpr = "selectedItems[0].status != 'WAIT_AUDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "AuditTask@Audit"
                        )
                ),
        }
)
public class AuditTask extends BaseModel {

    @FabosJsonField(
            views = @View(title = "8D报告", type = ViewType.TABLE_FORM, column = "generalCode"),
            edit = @Edit(title = "8D报告", type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Report8D report8D;

    @FabosJsonField(
            views = @View(title = "8D报告ID", show = false),
            edit = @Edit(title = "8D报告ID", show = false
            )
    )
    @Column(name = "reprot_8d_id", length = 40, nullable = false)
    private String report8dId;

    @FabosJsonField(
            views = @View(title = "8D报告状态"),
            edit = @Edit(
                    title = "8D报告状态",
                    inputType = @InputType(length = 20),
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = Report8DStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "reprot_8d_status", length = 20)
    private String report8dStatus;

    @FabosJsonField(
            views = @View(title = "审核状态"),
            edit = @Edit(title = "审核状态",
                    defaultVal = "WAIT_AUDIT",
                    type = EditType.CHOICE,
                    search = @Search(defaultVal = "WAIT_AUDIT"),
                    choiceType = @ChoiceType(fetchHandler = AuditTaskStatusEnum.class),
                    inputType = @InputType(length = 20)
            )
    )
    @Column(length = 20, nullable = false)
    private String status;

    @FabosJsonField(
            views = @View(title = "审核意见"),
            edit = @Edit(
                    title = "审核意见",
                    inputType = @InputType(length = 10),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AuditResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;

    @FabosJsonField(
            views = @View(title = "审核人ID", show = false),
            edit = @Edit(title = "审核人ID", show = false)
    )
    @Column(name = "audit_person_id", length = 50)
    private String auditPersonId;

    @FabosJsonField(
            views = @View(title = "审核人", show = false),
            edit = @Edit(title = "审核人", show = false)
    )
    @Column(name = "audit_person_name", length = 50)
    private String auditPersonName;

    @FabosJsonField(
            views = @View(title = "审核时间",
                    type = ViewType.DATE_TIME),
            edit = @Edit(title = "审核时间",
                    show = false,
                    search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date auditTime;


    public static AuditTask createInstance(Report8D report8D) {
        AuditTask auditTask = new AuditTask();
        auditTask.setReport8dId(report8D.getId());
        auditTask.setReport8dStatus(report8D.getStatus());
        auditTask.setAuditPersonId(report8D.getAuditPersonId());
        auditTask.setAuditPersonName(report8D.getAuditPersonName());
        auditTask.setStatus(AuditTaskStatusEnum.Enum.WAIT_AUDIT.name());
        auditTask.setReport8D(report8D);
        return auditTask;
    }
}
