package cec.jiutian.bc.report8D.domain.myTempMeasureTask.handler;

import cec.jiutian.bc.report8D.domain.myTempMeasureTask.model.MyTempMeasureTask;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.TempMeasure;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MyTempMeasureTaskSubmitHandler implements OperationHandler<MyTempMeasureTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyTempMeasureTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyTempMeasureTask myTempMeasureTask = data.get(0);
            List<TempMeasure> updateDetailList = new ArrayList<>();
            for (TempMeasure d : myTempMeasureTask.getTempMeasureList()) {
                if(!d.getResponsiblePersonId().equals(UserContext.getUserId())){
                    continue;
                }
                if(!MeasureProgressEnum.Enum.EXECUTED.name().equals(d.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有任务才可提交");
                }
                updateDetailList.add(d);
            }

            updateDetailList.forEach(d->{
                d.setProgress(MeasureProgressEnum.Enum.WAIT_VERIFY.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            Report8D report8D = fabosJsonDao.findById(Report8D.class, myTempMeasureTask.getId());
            if(checkCurrentStatus(report8D)){
                report8D.setTempStatus(MeasureCompleteStatusEnum.Enum.FINISH.name());
                fabosJsonDao.mergeAndFlush(report8D);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(Report8D report8D) {
        for (TempMeasure tempMeasure : report8D.getTempMeasureList()) {
            if (!MeasureProgressEnum.Enum.WAIT_VERIFY.name().equals(tempMeasure.getProgress())) {
                return false;
            }
        }
        return true;
    }
}
