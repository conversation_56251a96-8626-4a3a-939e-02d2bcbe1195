package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "8D报告D5",
        orderBy = "Report8DD5.createTime desc"
)
public class Report8DD5 extends MetaModel {

    @FabosJsonField(
            views = @View(title = "流出对策"),
            edit = @Edit(
                    title = "流出对策",
                    type = EditType.TEXTAREA,
                    notNull = true,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "outflow_measure", length = 100)
    private String outflowMeasure;

    //长期措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "长期措施", column = "longMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "长期措施",
                    type = EditType.TAB_TABLE_ADD,
                    referenceTableType = @ReferenceTableType(label = "longMeasure")
            )
    )
    private List<D5LongMeasure> d5LongMeasureList;
}
