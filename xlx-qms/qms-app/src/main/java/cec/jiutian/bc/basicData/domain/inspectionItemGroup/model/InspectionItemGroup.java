package cec.jiutian.bc.basicData.domain.inspectionItemGroup.model;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler.InsGroupActiveOperationHandler;
import cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler.InsGroupCopyOperationHandler;
import cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler.InsGroupDynamicHandler;
import cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler.InsGroupInactiveOperationHandler;
import cec.jiutian.bc.basicData.domain.inspectionItemGroup.proxy.InspectionItemGroupDataProxy;
import cec.jiutian.bc.basicData.enumeration.GroupType;
import cec.jiutian.bc.basicData.enumeration.InsGroupStatusEnum;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.dto.SendPointMTO;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.enumration.SendPointTypeEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ‌检验项目组核心属性管理‌
 * <p>
 * 需包含组编号、名称、使用部门、组类型、备注字段‌1
 * <p>
 * 组编号需唯一且支持自动生成规则（如G-YYYYMMDD-001）‌2
 * <p>
 * 支持多部门数据隔离（如质量部仅管理本部门的检验项目组）‌3
 * ‌检验项目动态关联‌
 * <p>
 * 支持多对多关联检验项目（可动态增减项目）‌1
 * 检验项目调整仅允许在创建/编辑状态下操作‌4
 * ‌生命周期状态控制‌
 * <p>
 * 状态流转：创建 → 编辑 → 生效 → 失效‌5
 * 生效状态的组不可编辑/删除，仅失效后可删除‌6
 * ‌数据操作支持‌
 * <p>
 * 支持复制已有组生成新组（需深拷贝关联的检验项目）‌1
 * 提供批量导入功能（需校验部门及项目有效性）‌
 */
@Getter
@Setter
@Entity
@Table(name = "inspection_item_group",
        indexes = {
                @Index(name = "idx_group_code", columnList = "group_code", unique = true),  // 组编号唯一索引
                @Index(name = "idx_department_id", columnList = "department_id")                 // 部门查询优化
        })
@FabosJson(
        name = "检验项目组",
        orderBy = "InspectionItemGroup.createdAt desc",
        dataProxy = InspectionItemGroupDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "InspectionItemGroup@ACTIVE",
                        operationHandler = InsGroupActiveOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "status=='ACTIVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionItemGroup@ACTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "失效",
                        code = "InspectionItemGroup@INACTIVE",
                        operationHandler = InsGroupInactiveOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "status=='INACTIVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionItemGroup@INACTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "复制",
                        code = "InspectionItemGroup@COPY",
                        operationHandler = InsGroupCopyOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionItemGroup@COPY"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                )
        },
        power = @Power(print = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status=='ACTIVE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status!='INACTIVE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
public class InspectionItemGroup extends MetaModel {

    @FabosJsonField(
            views = @View(title = "组编号"),
            edit = @Edit(title = "组编号",
                    notNull = true,search = @Search),
            dynamicField = @DynamicField(dependFiled = @DependFiled(buttonName = "生成", changeBy = "id",
                    dynamicHandler = InsGroupDynamicHandler.class))
    )
    @Column(name = "group_code", nullable = false, length = 50)
    private String groupCode;

    // 组名称
    @FabosJsonField(
            views = @View(title = "组名称"),
            edit = @Edit(title = "组名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "group_name", nullable = false, length = 100)
    private String groupName;

    @FabosJsonField(
            views = @View(title = "组类型"),
            edit = @Edit(title = "组类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = GroupType.ChoiceFetch.class)
            )
    )
    @Column(name = "group_type", length = 20)
    private String groupType;

    @Transient
    @FabosJsonField(
            views = @View(title = "使用部门", show = false, column = "name"),
            edit = @Edit(title = "使用部门",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private OrgMTO orgMTO;

    // 使用部门（如"质量部"）
    @FabosJsonField(
            views = @View(title = "使用部门"),
            edit = @Edit(title = "使用部门",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    @Column(name = "department", length = 50)
    private String department;

    @FabosJsonField(
            views = @View(title = "部门ID", show = false),
            edit = @Edit(title = "部门ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    @Column(name = "department_id", length = 50)
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "包装方式"),
            edit = @Edit(title = "包装方式", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "packageType"),
                    search = @Search(vague = true)
            )
    )
    private String packageType;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "sample_point", length = 50)
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送检点类型"),
            edit = @Edit(title = "送检点类型", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SendPointTypeEnum.class))
    )
    private String sendPointType;

    @FabosJsonField(
            views = @View(title = "送检点", column = "name"),
            edit = @Edit(title = "送检点", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${sendPointType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "sendPointType != 'LM_PROCESS_LAB'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "send_point_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SendPointMTO sendPointMTO;

    @Transient
    @FabosJsonField(
            views = @View(title = "单位", show = false, column = "name"),
            edit = @Edit(title = "单位", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private MesUnit mesUnit;

    @FabosJsonField(
            views = @View(title = "单位id", show = false),
            edit = @Edit(title = "单位id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "mesUnit", beFilledBy = "id"))
    )
    private String usualUnitId;

    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "mesUnit", beFilledBy = "name"))
    )
    private String usualUnit;

//    @FabosJsonField(
//            views = @View(title = "单位"),
//            edit = @Edit(title = "单位", notNull = true,
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
//                            fetchHandlerParams = "USUAL_UNIT"),
//                    search = @Search(vague = true)
//            )
//    )
//    private String usualUnit;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    type = EditType.TEXTAREA
            )
    )
    @Column(name = "remark", length = 500)
    private String remark;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    readonly = @Readonly(
                            add = true,
                            edit = false
                    ),
                    defaultVal = "CREATED",
                    type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = InsGroupStatusEnum.ChoiceFetch.class)
            )
    )
    @Column(name = "status", nullable = false, length = 20)
    private String status;

    // 创建人
    @FabosJsonField(
            views = @View(title = "创建人"),
            edit = @Edit(title = "创建人",
                    show = false,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "created_by_id", length = 50)
    private String createdById;

    @FabosJsonField(
            views = @View(title = "创建时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "创建时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),search = @Search(vague = true)
            )
    )
    @CreationTimestamp
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdAt;

    // 最后修改时间
    @FabosJsonField(
            views = @View(title = "最后修改时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "最后修改时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updatedAt;

    // 关联检验项目（多对多关系）
    @FabosJsonField(
            views = @View(title = "关联检验项目", column = "name", extraPK = "id", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "关联检验项目",
                    type = EditType.TAB_TABLE_REFER,
                    filter = @Filter("InspectionItem.status= 'Effective'"),
                    referenceTableType = @ReferenceTableType()
            )
    )
    @ManyToMany(cascade = CascadeType.DETACH)
    @JoinTable(name = "group_item_mapping",
            joinColumns = @JoinColumn(name = "group_id"),
            inverseJoinColumns = @JoinColumn(name = "item_id"))
    private List<InspectionItem> insItems;


    public void addItem(InspectionItem item) {
        if (CollectionUtils.isEmpty(this.insItems)) {
            ArrayList<InspectionItem> inspectionItems = new ArrayList<>(1);
            inspectionItems.add(item);
            this.setInsItems(inspectionItems);
        }
        for (InspectionItem inspectionItem : this.insItems) {
            if (inspectionItem.getId().equals(item.getId())) {
                return;
            }
        }
        this.insItems.add(item);
    }
}
