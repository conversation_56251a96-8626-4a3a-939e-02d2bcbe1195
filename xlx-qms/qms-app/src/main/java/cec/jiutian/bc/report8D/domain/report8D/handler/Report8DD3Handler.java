package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.domain.report8D.model.*;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class Report8DD3Handler implements OperationHandler<Report8D, Report8DD3> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD3 modelObject, String[] param) {
        if (CollectionUtils.isEmpty(modelObject.getD3TempMeasureList())) {
            return "msg.success('填写的数据为空')";
        }
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D3_AUDIT.name() : report8D.getStatus());
            report8DSetTempMeasureListAndUserIds(report8D, modelObject.getD3TempMeasureList());
            report8D.setD3CreateBy(UserContext.getUserName());
            fabosJsonDao.mergeAndFlush(report8D);

            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    private void report8DSetTempMeasureListAndUserIds(Report8D report8D, List<D3TempMeasure> d3TempMeasureList) {
        List<TempMeasure> tempMeasureList = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>();
        d3TempMeasureList.forEach(d3TempMeasure -> {
            TempMeasure tempMeasure = new TempMeasure();
            BeanUtil.copyProperties(d3TempMeasure, tempMeasure);
            if (Objects.isNull(tempMeasure.getProgress())) {
                tempMeasure.setProgress(MeasureProgressEnum.Enum.WAIT_EXECUTE.name());
            }
            tempMeasureList.add(tempMeasure);
            userIdSet.add(d3TempMeasure.getResponsiblePersonId());
        });
        report8D.setTempMeasureList(tempMeasureList);

        ArrayList<String> tempUsrIds = new ArrayList<>(userIdSet);
        report8D.setTempUserIds(String.join(",", tempUsrIds));
    }

    @Override
    public Report8DD3 fabosJsonFormValue(List<Report8D> data, Report8DD3 fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            if (CollectionUtils.isNotEmpty(report8D.getTempMeasureList())) {
                List<D3TempMeasure> tempMeasureList = new ArrayList<>();
                report8D.getTempMeasureList().forEach(d -> {
                    D3TempMeasure d3TempMeasure = new D3TempMeasure();
                    BeanUtil.copyProperties(d, d3TempMeasure);
                    UserForInsTaskMTO mto = fabosJsonDao.getById(UserForInsTaskMTO.class, d.getResponsiblePersonId());
                    d3TempMeasure.setResponsibleUserMTO(mto);
                    tempMeasureList.add(d3TempMeasure);
                });
                fabosJsonForm.setD3TempMeasureList(tempMeasureList);
            }
        }
        return fabosJsonForm;
    }
}
