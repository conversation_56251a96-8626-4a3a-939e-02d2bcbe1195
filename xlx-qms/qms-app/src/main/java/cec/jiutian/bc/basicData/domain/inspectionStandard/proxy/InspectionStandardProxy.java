package cec.jiutian.bc.basicData.domain.inspectionStandard.proxy;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetail;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.mto.LabMTO;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.mto.TechnologyFLowMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class InspectionStandardProxy implements DataProxy<InspectionStandard> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeDelete(InspectionStandard entity) {
        if (!entity.getStatus().equals(StatusEnum.Enum.Invalid.name())) {
            throw new FabosJsonApiErrorTip("失效状态允许删除");
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("technologyFlowId") != null) {
            TechnologyFLowMTO flowMTO = fabosJsonDao.findById(TechnologyFLowMTO.class, map.get("technologyFlowId"));
            if (flowMTO != null) {
                map.put("technologyFlow", flowMTO);
                map.put("technologyFlow_flowName", flowMTO.getFlowName());
            } else {
                throw new FabosJsonApiErrorTip("未查到工艺流程源数据，请确认");
            }
        }
        if (map.get("specificationId") != null) {
            SpecificationManageMTO specificationManageMTO = fabosJsonDao.findById(SpecificationManageMTO.class, map.get("specificationId"));
            if (specificationManageMTO != null) {
                map.put("material", specificationManageMTO);
                map.put("material_name", specificationManageMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到物资：" + String.valueOf(map.get("materialName")) + "源数据，请确认");
            }
        }
        if (map.get("sampleUnitId") != null) {
            MesUnit mesUnit = fabosJsonDao.findById(MesUnit.class, map.get("sampleUnitId"));
            if (mesUnit != null) {
                map.put("mesUnit", mesUnit);
                map.put("mesUnit_name", mesUnit.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到单位：" + String.valueOf(map.get("sampleUnit")) + "源数据，请确认");
            }
        }
//        if (map.get("labId") != null) {
//            LabMTO labMTO = fabosJsonDao.findById(LabMTO.class, map.get("labId"));
//            if (labMTO != null) {
//                map.put("labMTO", labMTO);
//                map.put("labMTO_labName", labMTO.getLabName());
//            } else {
//                throw new FabosJsonApiErrorTip("未查到实验室：" + String.valueOf(map.get("labName")) + "源数据，请确认");
//            }
//        }

        // 来料、发货、其他类型的质检标准特殊处理
        if (!StringUtils.equals(StandardTypeEnum.Enum.IPQC.name(), map.get("type").toString())) {
            if (null != map.get("details")) {
                List<InspectionStandardDetail> detailList = fabosJsonDao.findById(InspectionStandard.class, map.get("id")).getDetails();
                ((List<?>) map.get("details")).forEach(e -> {
                    if (e instanceof HashMap<?, ?>) {
                        detailList.stream().filter(x -> x.getId().equals(((HashMap<?, ?>) e).get("id"))).findFirst()
                                .ifPresent(p -> ((HashMap) e).put("itemGroupIds", p.getInspectionItem().getGroupId()));
                    }
                });
            }
        }
    }

    private void bindInsItermAndGroup(InspectionStandardDetail detail) {
        if (detail == null) {
            return;
        }
        InspectionItem item = fabosJsonDao.findById(InspectionItem.class, detail.getInspectionItem().getId());
        InspectionItemGroup itemGroup = fabosJsonDao.findById(InspectionItemGroup.class, detail.getInspectionItemGroup().getId());
        if (item == null) return;
        if (itemGroup == null) return;

        item.setGroupId(InspectionItem.addGroupId(item.getGroupId(), itemGroup.getId()));
        itemGroup.addItem(item);
        fabosJsonDao.merge(item);
        fabosJsonDao.merge(itemGroup);

    }

}
