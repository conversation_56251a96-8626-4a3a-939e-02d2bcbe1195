package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD3;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD3SaveHandler implements OperationHandler<Report8D, Report8DD3> {

    @Resource
    private Report8DD3Handler report8DD3Handler;

    @Override
    public Report8DD3 fabosJsonFormValue(List<Report8D> data, Report8DD3 fabosJsonForm, String[] param) {
        // 没有走进这个方法
        String[] execParam = {"submit"};
        report8DD3Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
