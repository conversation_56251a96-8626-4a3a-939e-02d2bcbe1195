package cec.jiutian.bc.yieldWarning.domain.materialWarningRule.proxy;

import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.model.MaterialWarningRule;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class MaterialWarningRuleDataProxy implements DataProxy<MaterialWarningRule> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("departmentId") != null) {
            OrgMTO orgMTO = fabosJsonDao.findById(OrgMTO.class, map.get("departmentId"));
            if (orgMTO != null) {
                map.put("orgMTO", orgMTO);
                map.put("orgMTO_name", orgMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到部门：" + String.valueOf(map.get("departmentName")) + "源数据，请确认");
            }
        }
        if (map.get("roleId") != null) {
            RoleMTO roleMTO = fabosJsonDao.findById(RoleMTO.class, map.get("roleId"));
            if (roleMTO != null) {
                map.put("roleMTO", roleMTO);
                map.put("roleMTO_name", roleMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到角色：" + String.valueOf(map.get("roleName")) + "源数据，请确认");
            }
        }
    }

}
