package cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.enumeration.InsGroupStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InsGroupInactiveOperationHandler implements OperationHandler<InspectionItemGroup, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionItemGroup> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionItemGroup inspectionItem = data.get(0);
            inspectionItem.setStatus(InsGroupStatusEnum.INACTIVE.name());
            fabosJsonDao.updateAndFlush(inspectionItem);
        }
        return "msg.success('操作成功')";
    }
}
