package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewCreateMTO;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description 编号生成handler
 */
@Component
public class UnqualifiedReviewCodeGenerateDynamicHandler implements DependFiled.DynamicHandler<MRBUnqualifiedReviewCreateMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(MRBUnqualifiedReviewCreateMTO mrbUnqualifiedReview) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode",String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.MRBUnqualifiedReview.name(), 1, null).get(0)));
        return map;
    }
}
