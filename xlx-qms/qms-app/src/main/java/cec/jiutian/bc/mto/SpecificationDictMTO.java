package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/28
 * @description TODO
 */
@Entity
@Table(name = "ms_spcdct")
@Getter
@FabosJson(
        name = "物资分类",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class SpecificationDictMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            views = @View(title = "", show = false),
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "pid", columnDefinition = "int8")
    private Long pid;

    @FabosJsonField(
            views = @View(title = "分类编码"),
            edit = @Edit(title = "分类编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcdct_cd", length = 40)
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcdct_nm", length = 40)
    private String name;

    @Column(name = "crte_tm")
    private Date createTime;
}
