package cec.jiutian.bc.mrb.service;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.UnqualifiedReviewApprovalInfo;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.model.UnqualifiedReviewTask;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedReviewTaskPresentEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Slf4j
@Service
public class MRBUnqualifiedReviewService {
    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    //发布后的审批
    public void createUnqualifiedReviewTask(MRBUnqualifiedReview review, String auditType) {
        UnqualifiedReviewTask reviewTask = new UnqualifiedReviewTask();
        reviewTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.UNQUALIFIED_REVIEW_TASK.name(), 1, null).get(0)));
        reviewTask.setUnqualifiedReview(review);
        reviewTask.setUnqualifiedAuditType(auditType);
        reviewTask.setBusinessStatus(UnqualifiedReviewTaskPresentEnum.Enum.NOT_FINISH.name());
        fabosJsonDao.mergeAndFlush(reviewTask);
    }

    //处置后的审批
    public void createUnqualifiedReviewTaskByDisposal(MRBUnqualifiedReview review, String auditType, String reviewerId, String reviewerName) {
        UnqualifiedReviewTask reviewTask = new UnqualifiedReviewTask();
        reviewTask.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.UNQUALIFIED_REVIEW_TASK.name(), 1, null).get(0)));
        reviewTask.setUnqualifiedReview(review);
        reviewTask.setReviewerId(reviewerId);
        reviewTask.setReviewerName(reviewerName);
        reviewTask.setUnqualifiedAuditType(auditType);
        reviewTask.setBusinessStatus(UnqualifiedReviewTaskPresentEnum.Enum.NOT_FINISH.name());
        fabosJsonDao.mergeAndFlush(reviewTask);
    }

    public void createUnqualifiedReviewApprovalInfo(UnqualifiedReviewTask model) {
        MRBUnqualifiedReview review = fabosJsonDao.getById(MRBUnqualifiedReview.class, model.getUnqualifiedReview().getId());
        List<UnqualifiedReviewApprovalInfo> approvalInfoList = review.getApprovalInfoList();
        if (approvalInfoList == null) {
            approvalInfoList = new ArrayList<>();
        }
        UnqualifiedReviewApprovalInfo approvalInfo = new UnqualifiedReviewApprovalInfo();
        BeanUtil.copyProperties(model, approvalInfo);
        approvalInfo.setId(null);
        approvalInfoList.add(approvalInfo);
        review.setApprovalInfoList(approvalInfoList);
        fabosJsonDao.mergeAndFlush(review);
    }
}
