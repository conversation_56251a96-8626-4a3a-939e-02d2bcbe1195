package cec.jiutian.bc.basicData.domain.inspectionStandard.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardLims;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Component
public class LimsStandardDetailHandler implements DependFiled.DynamicHandler<InspectionStandardLims> {

    @Override
    public Map<String, Object> handle(InspectionStandardLims entity) {
        Map<String, Object> result = new HashMap<>();
        if (null == entity.getInspectionItemGroup()) {
            result.put("details", Collections.emptyList());
        }
        return result;
    }
}
