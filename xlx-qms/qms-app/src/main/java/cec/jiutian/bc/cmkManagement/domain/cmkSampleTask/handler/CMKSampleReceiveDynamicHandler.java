package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto.CMKSampleTaskReceiveMTO;
import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/3/20 14:35
 * @description：
 */
@Component
public class CMKSampleReceiveDynamicHandler implements DependFiled.DynamicHandler<CMKSampleTaskReceiveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(CMKSampleTaskReceiveMTO cmkSampleTaskReceiveMTO) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(cmkSampleTaskReceiveMTO.getGeneralCode())) {
            CMKSampleTask condition = new CMKSampleTask();
            condition.setGeneralCode(cmkSampleTaskReceiveMTO.getGeneralCode());
            condition.setBusinessState(SampleStateEnum.Enum.SEND_SAMPLE.name());
            CMKSampleTask samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("generalCode",samplingTask.getGeneralCode());
            result.put("cmkInspectionTaskCode",samplingTask.getCmkInspectionTaskCode());
            result.put("processName", samplingTask.getProductProcessMTO().getName());
            result.put("materialName", samplingTask.getMaterialName());
            result.put("materialCode", samplingTask.getMaterialCode());
            result.put("sampleWeight", samplingTask.getSampleWeight());
        }
        return result;
    }
}
