package cec.jiutian.bc.basicData.domain.patrolInspectionItem.proxy;

import cec.jiutian.bc.basicData.domain.patrolInspectionItem.model.PatrolInspectionItem;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PatrolInspectionItemProxy implements DataProxy<PatrolInspectionItem> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(PatrolInspectionItem entity) {
        // 校验名称
        PatrolInspectionItem condition = new PatrolInspectionItem();
        condition.setName(entity.getName());
        PatrolInspectionItem data = fabosJsonDao.selectOne(condition);
        if (null != data) {
            throw new FabosJsonApiErrorTip("巡检项名称不可重复，请确认");
        }

        entity.setStatus(StatusEnum.Enum.Effective.name());
    }

    @Override
    public void beforeUpdate(PatrolInspectionItem entity) {
        if (!entity.getStatus().equals(StatusEnum.Enum.Invalid.name())) {
            throw new FabosJsonApiErrorTip("失效状态允许编辑");
        }
    }

    @Override
    public void beforeDelete(PatrolInspectionItem entity) {
        if (!entity.getStatus().equals(StatusEnum.Enum.Invalid.name())) {
            throw new FabosJsonApiErrorTip("失效状态允许删除");
        }
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("operationId") != null) {
            String operationId = map.get("operationId").toString();
            ProductProcessMTO productProcessMTO = fabosJsonDao.findById(ProductProcessMTO.class, operationId);
            if (productProcessMTO != null) {
                map.put("processOperation", productProcessMTO);
                map.put("processOperation_code", productProcessMTO.getCode());
            }
        }
        if (map.get("factoryAreaId") != null) {
            String factoryAreaId = map.get("factoryAreaId").toString();
            FactoryArea factoryAreaMTO = fabosJsonDao.findById(FactoryArea.class, factoryAreaId);
            if (factoryAreaMTO != null) {
                map.put("factoryArea", factoryAreaMTO);
                map.put("factoryArea_factoryAreaName", factoryAreaMTO.getFactoryAreaName());
            }
        }
    }

}
