package cec.jiutian.bc.basicData.domain.inspectionStandard.model;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.modeler.domain.dto.ProcessStepMTO;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Filter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "质检标准详情"
)
@Table(name = "bd_inspection_standard_detail")
@Entity
@Getter
@Setter
public class InspectionStandardDetail extends MetaModel {

    @ManyToOne
    @JoinColumn(name = "inspection_standard_id")
    @FabosJsonField(
            views = {
                    @View(title = "质检标准", column = "generalCode", show = false)
            },
            edit = @Edit(title = "质检标准", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private InspectionStandard inspectionStandard;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码", readonly = @Readonly)
    )
    private String operationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", readonly = @Readonly)
    )
    private String operationName;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "工步", column = "stepName"),
            edit = @Edit(title = "工步",
                    type = EditType.REFERENCE_TABLE,
//                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "stepName"),
                    queryCondition = "{\"code\":\"${operationCode}\"}"
            )
    )
    private ProcessStepMTO processStep;

    @FabosJsonField(
            views = @View(title = "工步名称", show = false),
            edit = @Edit(title = "工步名称", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processStep", beFilledBy = "stepName"))
    )
    private String stepName;

    @FabosJsonField(
            views = @View(title = "检验项目", column = "name"),
            edit = @Edit(title = "检验项目",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "InspectionItem.status = 'Effective'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspection_item_id")
    private InspectionItem inspectionItem;

    @FabosJsonField(
            views = @View(title = "检验项目id", show = false),
            edit = @Edit(title = "检验项目id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "id"))
    )
    private String itemId;

    //    @Transient
    @FabosJsonField(
            views = @View(title = "检验项目中的检验项目组id，拼接值，只作为项目组查询条件", show = false),
            edit = @Edit(title = "检验项目中的检验项目组id，拼接值，只作为项目组查询条件", show = false)
    )
    private String itemGroupIds;

    @ManyToOne
    @JoinColumn(name = "inspection_item_group_id")
    @FabosJsonField(
            views = @View(title = "检验项目组", column = "groupName"),
            edit = @Edit(title = "检验项目组",
//                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    filter = @Filter(value = "status = 'ACTIVE'"),
                    queryCondition = "{\"_includeList\":\"${SPLIT(itemGroupIds, ',')}\"}"
            )
    )
    private InspectionItemGroup inspectionItemGroup;

//    @FabosJsonField(
//            views = @View(title = "样本数量"),
//            edit = @Edit(title = "样本数量", readonly = @Readonly,
//                    numberType = @NumberType(min = 0, max = 999999999, precision = 2))
//    )
//    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "检验频次"),
            edit = @Edit(title = "检验频次", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "inspectFrequency"))
    )
    private Integer inspectFrequency;

    @FabosJsonField(
            views = @View(title = "检验频次单位"),
            edit = @Edit(title = "检验频次单位", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "accountUnit_unitChnName"))
    )
    private String inspectFrequencyUnit;

    @FabosJsonField(
            views = @View(title = "是否开线首检"),
            edit = @Edit(title = "是否开线首检", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "isOpenLine"))
    )
    private Boolean isOpenLine;

    @FabosJsonField(
            views = @View(title = "是否开机首检"),
            edit = @Edit(title = "是否开机首检", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "isOpenDevice"))
    )
    private Boolean isOpenDevice;

    @FabosJsonField(
            views = @View(title = "是否原材料首检"),
            edit = @Edit(title = "是否原材料首检", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "inspectionItem", beFilledBy = "isRawMaterial"))
    )
    private Boolean isRawMaterial;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验方法", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "检验方法",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private InspectionMethod inspectionMethod;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "抽样方案", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "抽样方案",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    private SamplingPlan samplingPlan;

}
