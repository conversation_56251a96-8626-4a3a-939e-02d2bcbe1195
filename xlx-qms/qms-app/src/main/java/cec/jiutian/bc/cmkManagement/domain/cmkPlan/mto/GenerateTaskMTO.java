package cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler.CMKInspectionTaskMTOReferenceAddHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.*;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "CMK计划生成任务MTO"
)
public class GenerateTaskMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "CMK计划号"),
            edit = @Edit(title = "CMK计划号", readonly = @Readonly)
    )
    private String generalCode;

//    @FabosJsonField(
//            views = @View(title = "检验设备", column = "abbreviation"),
//            edit = @Edit(title = "检验设备",notNull = true,
//                    type = EditType.REFERENCE_TABLE,
//                    queryCondition = "{\"isGenerateTask\": \"false\", \"cmkPlanEquipmentDetail.CMKPlan.generalCode\": \"${generalCode}\"}",
//                    referenceTableType = @ReferenceTableType(id = "id", label = "abbreviation")
//            )
//    )
//    @ManyToOne
//    @JoinColumn(name = "cmk_plan_detail_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
//    private CMKPlanEquipmentDetail cmkPlanEquipmentDetail;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "Equipment_archive_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "设备部件详情", type = EditType.TAB_REFER_ADD, allowAddMultipleRows = false),
            referenceAddType = @ReferenceAddType(referenceClass = "CMKPlanEquipmentDetail",
                    filter = "CMKPlanEquipmentDetail.isGenerateTask = false and CMKPlanEquipmentDetail.cmkPlan.generalCode = '${generalCode}'",
                    referenceAddHandler = CMKInspectionTaskMTOReferenceAddHandler.class,
                    editable = {"productProcessMTO", "specificationCustomerRequire", "toleranceRequire", "specificationCenter",
                            "specificationUpperLimit", "specificationLowerLimit", "toleranceRange", "specificationManage", "instrumentRange", "precision"}),
            views = @View(title = "设备部件详情", type = ViewType.TABLE_VIEW, extraPK = "planDetailId")
    )
    private List<CMKInspectionTaskMTO> taskMTOList;
}
