package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.service.CMKService;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description TODO
 */
@Component
public class GenerateReportOperationHandler implements OperationHandler<CMKInspectionTask, CMKInspectionTask> {

    @Resource
    private CMKService cmkService;

    @Override
    public String exec(List<CMKInspectionTask> data, CMKInspectionTask modelObject, String[] param) {

        return "alert('操作成功')";
    }

    @Override
    public DownloadableFile fileOperator(List<CMKInspectionTask> selectedData, String[] param) {
        try {
            Workbook workbook = cmkService.generateReport(selectedData.get(0).getId());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            workbook.write(outputStream);
            workbook.close();
            return new DownloadableFile(outputStream, "CMK报告_"+" + LocalDate.now()"+"ttt." + (workbook instanceof HSSFWorkbook ? "xls" : "xlsx"), workbook instanceof HSSFWorkbook ?
                    "application/vnd.ms-excel" :
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
