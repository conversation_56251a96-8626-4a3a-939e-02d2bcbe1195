package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD2;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD2SaveHandler implements OperationHandler<Report8D, Report8DD2> {

    @Resource
    private Report8DD2Handler report8DD2Handler;

    @Override
    public Report8DD2 fabosJsonFormValue(List<Report8D> data, Report8DD2 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD2Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
