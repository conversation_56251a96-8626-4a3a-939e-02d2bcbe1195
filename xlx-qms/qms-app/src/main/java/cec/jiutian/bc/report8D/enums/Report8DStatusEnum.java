package cec.jiutian.bc.report8D.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class Report8DStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //待提交
        WAIT_SUBMIT("待提交"),
        D1("D1"),
        D2("D2"),
        D2_AUDIT("D2审核"),
        D3("D3"),
        D3_AUDIT("D3审核"),
        D4("D4"),
        D4_AUDIT("D4审核"),
        D5("D5"),
        D5_AUDIT("D5审核"),
        D6("D6"),
        D6_AUDIT("D6审核"),
        D7("D7"),
        D7_AUDIT("D7审核"),
        D8("D8"),
        D8_AUDIT("D8审核"),
        CLOSED("已关闭"),
        ;

        private final String value;

        public String getNextOnSuccess() {
            if (this.name().endsWith("_AUDIT")) {
                if (this == D8_AUDIT) {
                    return CLOSED.name();
                }
                Enum next = values()[this.ordinal() + 1];
                return next.name();
            }
            return null;
        }

        public String getPreOnFailure() {
            if (this.name().endsWith("_AUDIT")) {
                Enum prev = values()[this.ordinal() - 1];
                return prev.name();
            }
            return null;
        }

    }

    public static String getNextStatus(String currentStatusName) {
        Enum current = Enum.valueOf(currentStatusName);
        return current.getNextOnSuccess();
    }

    public static String getPreStatus(String currentStatusName) {
        Enum current = Enum.valueOf(currentStatusName);
        return current.getPreOnFailure();
    }
}
