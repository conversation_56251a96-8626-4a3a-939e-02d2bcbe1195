package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler;

import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.model.PPKSampleTask;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskSendMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class PPKSampleSendDynamicHandler implements DependFiled.DynamicHandler<PPKSampleTaskSendMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(PPKSampleTaskSendMTO ppkSampleTaskSendMTO) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotEmpty(ppkSampleTaskSendMTO.getGeneralCode())) {
            PPKSampleTask condition = new PPKSampleTask();
            condition.setGeneralCode(ppkSampleTaskSendMTO.getGeneralCode());
            condition.setBusinessState(SampleStateEnum.Enum.SAMPLING_FINISH.name());
            PPKSampleTask samplingTask = fabosJsonDao.selectOne(condition);
            if (samplingTask == null) {
                throw new FabosJsonApiErrorTip("未查询到取样单，请确认");
            }
            result.put("generalCode", samplingTask.getGeneralCode());
            result.put("ppkInspectionTaskCode", samplingTask.getPpkInspectionTaskCode());
            result.put("processName", samplingTask.getProductProcessMTO().getName());
            result.put("materialName", samplingTask.getMaterialName());
            result.put("materialCode", samplingTask.getMaterialCode());
            result.put("sampleWeight", samplingTask.getSampleWeight());
        }
        return result;
    }
}
