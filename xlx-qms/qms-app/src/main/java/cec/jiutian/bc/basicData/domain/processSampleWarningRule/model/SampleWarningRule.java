package cec.jiutian.bc.basicData.domain.processSampleWarningRule.model;

import cec.jiutian.bc.basicData.domain.processSampleWarningRule.handler.SampleWarningRuleInvalidOperationHandler;
import cec.jiutian.bc.basicData.domain.processSampleWarningRule.handler.SampleWarningRuleValidOperationHandler;
import cec.jiutian.bc.basicData.domain.processSampleWarningRule.proxy.SampleWarningRuleDataProxy;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "bd_sample_warning_rule",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "取样超时预警规则",
        dataProxy = SampleWarningRuleDataProxy.class,
        power = @Power(export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "SampleWarningRule@VALID",
                        operationHandler = SampleWarningRuleValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SampleWarningRule@VALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "失效",
                        code = "SampleWarningRule@INVALID",
                        operationHandler = SampleWarningRuleInvalidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "SampleWarningRule@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status !='Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
public class SampleWarningRule extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.SampleWarningRule.name();
    }

    @FabosJsonField(
            views = @View(title = "适用检验类型"),
            edit = @Edit(title = "适用检验类型", defaultVal = "IPQC",
                    readonly = @Readonly,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StandardTypeEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String inspectionType;

    @FabosJsonField(
            views = @View(title = "超出时限"),
            edit = @Edit(title = "超出时限", notNull = true, inputGroup = @InputGroup(postfix = "小时"))
    )
    private Integer timeLimit;

    @Transient
    @FabosJsonField(
            views = @View(title = "通知角色", column = "name", show = false),
            edit = @Edit(title = "通知角色", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private RoleMTO roleMTO;

    @FabosJsonField(
            views = @View(title = "角色ID", show = false),
            edit = @Edit(title = "角色ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "roleMTO", beFilledBy = "id"))
    )
    private String roleId;

    @FabosJsonField(
            views = @View(title = "通知角色"),
            edit = @Edit(title = "通知角色", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "roleMTO", beFilledBy = "name"))
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", search = @Search, readonly = @Readonly,
                    defaultVal = "Invalid",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;

}
