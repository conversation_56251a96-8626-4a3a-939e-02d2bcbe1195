package cec.jiutian.bc.basicData.domain.inspectionStandard.model;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.handler.LimsStandardDetailDynamicHandler;
import cec.jiutian.bc.basicData.domain.inspectionStandard.handler.LimsStandardDetailHandler;
import cec.jiutian.bc.basicData.domain.inspectionStandard.proxy.InspectionStandardLimsProxy;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.dto.SendPointMTO;
import cec.jiutian.bc.modeler.enumration.SendPointTypeEnum;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "质检标准-实验室",
        dataProxy = InspectionStandardLimsProxy.class
)
@Table(name = "bd_inspection_standard",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InspectionStandardLims extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.InspectionStandard.name();
    }

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", notNull = true, readonly = @Readonly(add = false))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "适用检验类型"),
            edit = @Edit(title = "适用检验类型", defaultVal = "LIMS", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StandardTypeEnum.class))
    )
    private String type;

    @Transient
    @FabosJsonField(
            views = @View(title = "物料名称", column = "name"),
            edit = @Edit(title = "物料名称", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '01%'")
            )
    )
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "物料id", show = false),
            edit = @Edit(title = "物料id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "id"))
    )
    private String specificationId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly, show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "name"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述", type = EditType.TEXTAREA)
    )
    private String description;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验项目组", column = "groupName"),
            edit = @Edit(title = "检验项目组",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    filter = @Filter(value = "status = 'ACTIVE' and sendPointType != 'LM_PROCESS_LAB'")
            )
    )
    private InspectionItemGroup inspectionItemGroup;

    @FabosJsonField(
            views = @View(title = "是否留样"),
            edit = @Edit(title = "是否留样", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String keepSampleFlag;

    @FabosJsonField(
            views = @View(title = "留样量"),
            edit = @Edit(title = "留样量",
                    dependFieldDisplay = @DependFieldDisplay(
                            notNull = "keepSampleFlag == 'Y'",
                            showOrHide = "keepSampleFlag == 'Y'"))
    )
    private Double sampleQuantity;

    @Transient
    @FabosJsonField(
            views = @View(title = "样品单位", show = false, column = "name"),
            edit = @Edit(title = "样品单位", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'")
            )
    )
    private MesUnit mesUnit;

    @FabosJsonField(
            views = @View(title = "样品单位id", show = false),
            edit = @Edit(title = "样品单位id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "mesUnit", beFilledBy = "id"))
    )
    private String sampleUnitId;

    @FabosJsonField(
            views = @View(title = "样品单位"),
            edit = @Edit(title = "样品单位", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "mesUnit", beFilledBy = "name"))
    )
    private String sampleUnit;

    @FabosJsonField(
            views = @View(title = "留样取样点"),
            edit = @Edit(title = "留样取样点",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'")
            )
    )
    @Column(name = "sample_point", length = 50)
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送检点类型"),
            edit = @Edit(title = "送检点类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SendPointTypeEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'"))
    )
    private String sendPointType;

    @FabosJsonField(
            views = @View(title = "留样送检点", column = "name"),
            edit = @Edit(title = "留样送检点", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${sendPointType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "send_point_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SendPointMTO sendPointMTO;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE, defaultVal = "Invalid",
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "inspection_standard_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "质检标准详情", type = EditType.TAB_REFER_ADD),
            views = @View(title = "质检标准详情", type = ViewType.TABLE_VIEW, extraPK = "itemId", column = "name"),
            referenceAddType = @ReferenceAddType(referenceClass = "InspectionItemGroupWithItemMTO",
                    filter = "i.status = 'Effective' and g.id = '${inspectionItemGroup.id}' and i.samplingPlan is not null",
//                    queryCondition = "{\"itemGroupId\":\"${inspectionItemGroup.id}\"}",
//                    editable = {"inspectionItemGroup"},
                    referenceAddHandler = LimsStandardDetailDynamicHandler.class),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "inspectionItemGroup", dynamicHandler = LimsStandardDetailHandler.class))
    )
    private List<InspectionStandardDetail> details;

}
