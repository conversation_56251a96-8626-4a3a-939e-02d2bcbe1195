package cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.handler;

import cec.jiutian.bc.cmkManagement.service.CMKService;
import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.model.PPKInspectionTask;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description TODO 暂时隐藏
 */
//@Component
public class PPKGenerateReportOperationHandler implements OperationHandler<PPKInspectionTask, PPKInspectionTask> {

    @Resource
    private CMKService cmkService;

    @Override
    public String exec(List<PPKInspectionTask> data, PPKInspectionTask modelObject, String[] param) {

        return "alert('操作成功')";
    }

    @Override
    public DownloadableFile fileOperator(List<PPKInspectionTask> selectedData, String[] param) {
        try {
            Workbook workbook = cmkService.generateReport(selectedData.get(0).getId());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            workbook.write(outputStream);
            workbook.close();
            return new DownloadableFile(outputStream, "PPK报告_"+" + LocalDate.now()"+"ttt." + (workbook instanceof HSSFWorkbook ? "xls" : "xlsx"), workbook instanceof HSSFWorkbook ?
                    "application/vnd.ms-excel" :
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
