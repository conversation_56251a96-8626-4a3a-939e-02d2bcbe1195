package cec.jiutian.bc.ppkManagement.domain.ppkPlan.model;

import cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler.PPKInspectionTaskMTOReferenceAddHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.mto.PPKInspectionTaskMTO;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.field.*;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Data
@FabosJson(
        name = "PPK计划生成任务"
)
public class PPKPlanGenerateTask extends BaseModel {

    @FabosJsonField(
            views = @View(title = "PPK计划号"),
            edit = @Edit(title = "PPK计划号", readonly = @Readonly)
    )
    private String generalCode;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "Equipment_archive_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "设备部件详情", type = EditType.TAB_REFER_ADD, allowAddMultipleRows = false),
            referenceAddType = @ReferenceAddType(referenceClass = "PPKPlanEquipmentDetail",
                    filter = "ppkPlan.generalCode = '${generalCode}' and isGenerateTask = false",
                    referenceAddHandler = PPKInspectionTaskMTOReferenceAddHandler.class,
                    editable = {"productProcessMTO", "specificationCustomerRequire", "toleranceRequire", "specificationCenter",
                            "specificationUpperLimit", "specificationLowerLimit", "toleranceRange", "specificationManage", "instrumentRange", "precision"}),
            views = @View(title = "设备部件详情", type = ViewType.TABLE_VIEW, extraPK = "planDetailId")
    )
    private List<PPKInspectionTaskMTO> taskMTOList;
}
