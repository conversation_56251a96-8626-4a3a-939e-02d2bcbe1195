package cec.jiutian.bc.basicData.domain.patrolInspectionItem.model;

import cec.jiutian.bc.basicData.domain.patrolInspectionItem.handler.PIItemInValidOperationHandler;
import cec.jiutian.bc.basicData.domain.patrolInspectionItem.handler.PIItemValidOperationHandler;
import cec.jiutian.bc.basicData.domain.patrolInspectionItem.proxy.PatrolInspectionItemProxy;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.basicData.enumeration.PatrolInspectionFrequencyDimensionUnitEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.domain.dto.FactoryAreaMTO;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "巡检项目",
        orderBy = "PatrolInspectionItem.createTime desc",
        dataProxy = PatrolInspectionItemProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "PatrolInspectionItem@EFFECTIVE",
                        operationHandler = PIItemValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PatrolInspectionItem@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "失效",
                        code = "PatrolInspectionItem@INVALID",
                        operationHandler = PIItemInValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PatrolInspectionItem@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status == 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "bd_patrol_inspection_item",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class PatrolInspectionItem extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.PatrolInspectionItem.name();
    }

    @FabosJsonField(
            views = @View(title = "巡检项名称"),
            edit = @Edit(title = "巡检项名称", search = @Search(vague = true),
                    readonly = @Readonly(add = false), notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "巡检项内容"),
            edit = @Edit(title = "巡检项内容",
                    type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "方法"),
            edit = @Edit(title = "方法",
                    type = EditType.TEXTAREA)
    )
    private String method;

    @FabosJsonField(
            views = @View(title = "检验频次维度"),
            edit = @Edit(title = "检验频次维度", notNull = true,
                    inputGroup = @InputGroup(postfix = "#{dimensionUnit}"),
                    numberType = @NumberType(min = 1, max = 10))
    )
    private Integer frequencyDimension;

    @FabosJsonField(
            views = @View(title = "检验频次维度单位"),
            edit = @Edit(title = "检验频次维度单位", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PatrolInspectionFrequencyDimensionUnitEnum.class)
            )
    )
    private String dimensionUnit;

    @FabosJsonField(
            views = @View(title = "检验频次数"),
            edit = @Edit(title = "检验频次数", notNull = true,
                    inputGroup = @InputGroup(postfix = "次"),
                    numberType = @NumberType(min = 1, max = 10))
    )
    private Integer frequency;

    @ManyToMany
    @JoinTable(
            name = "e_patrol_item_area", //中间表表名
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "patrol_item_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "factory_area_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "适用车间", type = ViewType.TABLE_VIEW, column = "id"),
            edit = @Edit(title = "适用车间", type = EditType.TAB_TABLE_REFER, search = @Search(vague = true),
                    notNull = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private List<FactoryAreaMTO> factoryAreaList;

    @Transient
    @FabosJsonField(
            views = @View(title = "工序", show = false, column = "code"),
            edit = @Edit(title = "工序",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
            )
    )
    private ProductProcessMTO processOperation;

    @FabosJsonField(
            views = @View(title = "工序ID", show = false),
            edit = @Edit(title = "工序ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processOperation", beFilledBy = "id"))
    )
    private String operationId;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称", show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "processOperation", beFilledBy = "name"))
    )
    private String operationName;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

}
