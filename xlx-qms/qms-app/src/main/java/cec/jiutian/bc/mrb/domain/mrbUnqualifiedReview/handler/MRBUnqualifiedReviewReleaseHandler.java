package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedAuditTypeEnum;
import cec.jiutian.bc.mrb.service.MRBUnqualifiedReviewService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description TODO
 */
@Component
public class MRBUnqualifiedReviewReleaseHandler implements OperationHandler<MRBUnqualifiedReview, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private MRBUnqualifiedReviewService reviewService;

    @Override
    @Transactional
    public String exec(List<MRBUnqualifiedReview> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MRBUnqualifiedReview review = data.get(0);
            review.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.UNDER_REVIEW.name());
            reviewService.createUnqualifiedReviewTask(review, UnqualifiedAuditTypeEnum.Enum.QUALITY.name());
            fabosJsonDao.mergeAndFlush(review);
        }
        return "msg.success('操作成功')";
    }
}
