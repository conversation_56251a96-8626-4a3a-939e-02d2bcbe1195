package cec.jiutian.bc.report8D.domain.report8D.proxy;

import cec.jiutian.bc.report8D.domain.report8D.model.AuditTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuditTaskDataProxy implements DataProxy<AuditTask> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        return "AuditTask.auditPersonId = '"+userId+"'";
    }
}
