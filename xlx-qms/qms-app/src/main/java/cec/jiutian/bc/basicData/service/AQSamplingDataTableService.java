package cec.jiutian.bc.basicData.service;

import cec.jiutian.bc.modeler.enumration.AQLValueEnum;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/4/24
 * @description TODO
 */
@Service
public class AQSamplingDataTableService implements CommandLineRunner {
    private HashMap<String, HashMap<String, String>> acMap;

    public String getAcValue(String sampleSizeCode, String aqlValue) {
        HashMap<String, String> seqMap = acMap.get(aqlValue);
        if (seqMap != null) {
            return seqMap.get(sampleSizeCode);
        }
        return null;
    }

    @Override
    public void run(String... args) throws Exception {
        acMap = new HashMap<>();

        HashMap<String, String> firstAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "0");
            put("N", "0");
            put("P", "0");
            put("Q", "0");
            put("R", "0");
        }};
        acMap.put(AQLValueEnum.Enum.first.name(), firstAcMap);

        HashMap<String, String> secondAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "0");
            put("N", "0");
            put("P", "0");
            put("Q", "0");
            put("R", "0");
        }};
        acMap.put(AQLValueEnum.Enum.second.name(), secondAcMap);

        HashMap<String, String> thirdAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "0");
            put("N", "0");
            put("P", "0");
            put("Q", "1");
            put("R", "1");
        }};
        acMap.put(AQLValueEnum.Enum.third.name(), thirdAcMap);

        HashMap<String, String> fourthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "0");
            put("N", "0");
            put("P", "1");
            put("Q", "1");
            put("R", "2");
        }};
        acMap.put(AQLValueEnum.Enum.fourth.name(), fourthAcMap);

        HashMap<String, String> fifthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "0");
            put("N", "1");
            put("P", "1");
            put("Q", "2");
            put("R", "3");
        }};
        acMap.put(AQLValueEnum.Enum.fifth.name(), fifthAcMap);

        HashMap<String, String> sixthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "0");
            put("M", "1");
            put("N", "1");
            put("P", "2");
            put("Q", "3");
            put("R", "5");
        }};
        acMap.put(AQLValueEnum.Enum.sixth.name(), sixthAcMap);

        HashMap<String, String> seventhAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "0");
            put("L", "1");
            put("M", "1");
            put("N", "2");
            put("P", "3");
            put("Q", "5");
            put("R", "7");
        }};
        acMap.put(AQLValueEnum.Enum.seventh.name(), seventhAcMap);

        HashMap<String, String> eighthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "0");
            put("K", "1");
            put("L", "1");
            put("M", "2");
            put("N", "3");
            put("P", "5");
            put("Q", "7");
            put("R", "10");
        }};
        acMap.put(AQLValueEnum.Enum.eighth.name(), eighthAcMap);

        HashMap<String, String> ninthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "0");
            put("J", "1");
            put("K", "1");
            put("L", "2");
            put("M", "3");
            put("N", "5");
            put("P", "7");
            put("Q", "10");
            put("R", "14");
        }};
        acMap.put(AQLValueEnum.Enum.ninth.name(), ninthAcMap);

        HashMap<String, String> tenthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "0");
            put("H", "1");
            put("J", "1");
            put("K", "2");
            put("L", "3");
            put("M", "5");
            put("N", "7");
            put("P", "10");
            put("Q", "14");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.tenth.name(), tenthAcMap);

        HashMap<String, String> eleventhAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "0");
            put("G", "1");
            put("H", "1");
            put("J", "2");
            put("K", "3");
            put("L", "5");
            put("M", "7");
            put("N", "10");
            put("P", "14");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.eleventh.name(), eleventhAcMap);

        HashMap<String, String> twelfthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "0");
            put("F", "1");
            put("G", "1");
            put("H", "2");
            put("J", "3");
            put("K", "5");
            put("L", "7");
            put("M", "10");
            put("N", "14");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.twelfth.name(), twelfthAcMap);

        HashMap<String, String> thirteenthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "0");
            put("E", "1");
            put("F", "1");
            put("G", "2");
            put("H", "3");
            put("J", "5");
            put("K", "7");
            put("L", "10");
            put("M", "14");
            put("N", "21");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.thirteenth.name(), thirteenthAcMap);

        HashMap<String, String> fourteenthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "0");
            put("D", "1");
            put("E", "1");
            put("F", "2");
            put("G", "3");
            put("H", "5");
            put("J", "7");
            put("K", "10");
            put("L", "14");
            put("M", "21");
            put("N", "21");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.fourteenth.name(), fourteenthAcMap);

        HashMap<String, String> fifteenthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "0");
            put("C", "1");
            put("D", "1");
            put("E", "2");
            put("F", "3");
            put("G", "5");
            put("H", "7");
            put("J", "10");
            put("K", "14");
            put("L", "21");
            put("M", "21");
            put("N", "21");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.fifteenth.name(), fifteenthAcMap);

        HashMap<String, String> sixteenthAcMap = new HashMap<>() {{
            put("A", "0");
            put("B", "1");
            put("C", "1");
            put("D", "2");
            put("E", "3");
            put("F", "5");
            put("G", "7");
            put("H", "10");
            put("J", "14");
            put("K", "21");
            put("L", "21");
            put("M", "21");
            put("N", "21");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.sixteenth.name(), sixteenthAcMap);

        HashMap<String, String> seventeenthAcMap = new HashMap<>() {{
            put("A", "1");
            put("B", "1");
            put("C", "2");
            put("D", "3");
            put("E", "5");
            put("F", "7");
            put("G", "10");
            put("H", "14");
            put("J", "21");
            put("K", "21");
            put("L", "21");
            put("M", "21");
            put("N", "21");
            put("P", "21");
            put("Q", "21");
            put("R", "21");
        }};
        acMap.put(AQLValueEnum.Enum.seventeenth.name(), seventeenthAcMap);
    }
}
