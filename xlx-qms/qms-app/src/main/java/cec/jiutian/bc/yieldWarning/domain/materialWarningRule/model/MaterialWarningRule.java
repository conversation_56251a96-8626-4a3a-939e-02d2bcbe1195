package cec.jiutian.bc.yieldWarning.domain.materialWarningRule.model;

import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.mto.OrgMTO;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.handler.MaterialWarningRuleInvalidOperationHandler;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.handler.MaterialWarningRuleValidOperationHandler;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.proxy.MaterialWarningRuleDataProxy;
import cec.jiutian.bc.yieldWarning.enums.MaterialWarningStatisticalScopeEnum;
import cec.jiutian.bc.yieldWarning.enums.MaterialWarningStatisticalTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "yw_material_warning_rule",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        })
@Getter
@Setter
@FabosJson(
        name = "来料预警规则",
        dataProxy = MaterialWarningRuleDataProxy.class,
        power = @Power(export = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "MaterialWarningRule@VALID",
                        operationHandler = MaterialWarningRuleValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaterialWarningRule@VALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status !='Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "失效",
                        code = "MaterialWarningRule@INVALID",
                        operationHandler = MaterialWarningRuleInvalidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MaterialWarningRule@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status !='Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
public class MaterialWarningRule extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.MaterialWarningRule.name();
    }

    @FabosJsonField(
            views = @View(title = "统计范围"),
            edit = @Edit(title = "统计范围", search = @Search, notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MaterialWarningStatisticalScopeEnum.class))
    )
    private String statisticalScope;

    @FabosJsonField(
            views = @View(title = "统计类型"),
            edit = @Edit(title = "统计类型", search = @Search, notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MaterialWarningStatisticalTypeEnum.class))
    )
    private String statisticalType;

    @FabosJsonField(
            views = @View(title = "合格率下限值"),
            edit = @Edit(title = "合格率下限值", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2),
                    inputGroup = @InputGroup(postfix = "%"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "statisticalType == 'LowerLimit' || statisticalType == 'Range'")
            )
    )
    private Double lowerLimit;

    @FabosJsonField(
            views = @View(title = "合格率上限值"),
            edit = @Edit(title = "合格率上限值", notNull = true,
                    numberType = @NumberType(min = 0, max = 100, precision = 2),
                    inputGroup = @InputGroup(postfix = "%"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "statisticalType == 'UpperLimit' || statisticalType == 'Range'")
            )
    )
    private Double upperLimit;

    @Transient
    @FabosJsonField(
            views = @View(title = "通知部门", column = "name", show = false),
            edit = @Edit(title = "通知部门", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private OrgMTO orgMTO;

    @FabosJsonField(
            views = @View(title = "部门id", show = false),
            edit = @Edit(title = "部门id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "id"))
    )
    private String departmentId;

    @FabosJsonField(
            views = @View(title = "通知部门"),
            edit = @Edit(title = "通知部门", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "orgMTO", beFilledBy = "name"))
    )
    private String departmentName;

    @Transient
    @FabosJsonField(
            views = @View(title = "通知角色", column = "name", show = false),
            edit = @Edit(title = "通知角色", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false
            )
    )
    private RoleMTO roleMTO;

    @FabosJsonField(
            views = @View(title = "角色ID", show = false),
            edit = @Edit(title = "角色ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "roleMTO", beFilledBy = "id"))
    )
    private String roleId;

    @FabosJsonField(
            views = @View(title = "通知角色"),
            edit = @Edit(title = "通知角色", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "roleMTO", beFilledBy = "name"))
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", search = @Search, readonly = @Readonly,
                    defaultVal = "Invalid",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String status;


}
