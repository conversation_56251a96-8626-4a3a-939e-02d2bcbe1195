package cec.jiutian.bc.cmkManagement.domain.cmkPlan.proxy;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlan;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/26
 * @description TODO
 */
@Component
public class CMKPlanProxy implements DataProxy<CMKPlan> {

    @Override
    public void beforeAdd(CMKPlan cmkPlan) {
        setDetail(cmkPlan);
    }

    private void setDetail(CMKPlan cmkPlan) {
        if (CollectionUtils.isNotEmpty(cmkPlan.getDetailList())) {
            cmkPlan.getDetailList().forEach(detail -> {
                if (detail.getIsGenerateTask() == null) {
                    detail.setIsGenerateTask(false);
                }
            });
        }
    }
}
