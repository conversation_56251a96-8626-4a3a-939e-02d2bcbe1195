package cec.jiutian.bc.report8D.domain.myTempMeasureTask.model;

import cec.jiutian.bc.changeRequestManagement.domain.myChangeTask.handler.MyEcrSubmitHandler;
import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.InventoryMTO;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.handler.MyIssueTaskRUNOperationHandler;
import cec.jiutian.bc.qualityExceptionManagement.domain.inspectionIssuesImproveAndTrace.MyIssueTasks.model.MyIssueTaskRUN;
import cec.jiutian.bc.report8D.domain.myTempMeasureTask.handler.MyTempMeasureTaskExecHandler;
import cec.jiutian.bc.report8D.domain.myTempMeasureTask.handler.MyTempMeasureTaskSubmitHandler;
import cec.jiutian.bc.report8D.domain.myTempMeasureTask.proxy.MyTempMeasureTaskProxy;
import cec.jiutian.bc.report8D.domain.report8D.model.MemberInfo;
import cec.jiutian.bc.report8D.domain.report8D.model.TempMeasure;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "我的临时措施",
        orderBy = "MyTempMeasureTask.createTime desc",
        power = @Power(add = false, edit = false, export = false, importable = false),
        filter = @Filter("status in ('D4','D4_AUDIT','D5','D5_AUDIT','D6')"),
        dataProxy = MyTempMeasureTaskProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "任务执行",
                        code = "MyTempMeasureTask@Exec",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        operationHandler = MyTempMeasureTaskExecHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = MyTempMeasureTaskExec.class,
                        ifExpr = "selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyTempMeasureTask@Exec"
                        )
                ),
                @RowOperation(
                        title = "任务提交",
                        code = "MyTempMeasureTask@Submit",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = MyTempMeasureTaskSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].rowOperationAuthFlag == 0",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "MyTempMeasureTask@Submit"
                        )
                ),
        }
)
@TemplateType(type = "multiTable")
public class MyTempMeasureTask extends MetaModel {
    // 8D编号
    @FabosJsonField(
            views = @View(title = "8D编号"),
            edit = @Edit(title = "8D编号",
                    notNull = true,
                    search = @Search
            )
    )
    private String generalCode;

    // 题目
    @FabosJsonField(
            views = @View(title = "题目"),
            edit = @Edit(
                    title = "题目",
                    inputType = @InputType(length = 255),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "title", length = 255)
    private String title;

    // 投诉类型
    @FabosJsonField(
            views = @View(title = "投诉类型"),
            edit = @Edit(
                    title = "投诉类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = {DictChoiceFetchHandler.class},
                            fetchHandlerParams = {"ComplaintType"}
                    )
            )
    )
    @Column(name = "complaint_type", length = 20)
    private String complaintType;

    // 发生时间
    @FabosJsonField(
            views = @View(title = "发生时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "发生时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "occurrence_time", nullable = false)
    private Date occurrenceTime;

    // 产品批号
    @Transient
    @FabosJsonField(
            views = @View(title = "选择产品",
                    type = ViewType.TABLE_VIEW,
                    column = "materialName",
                    show = false
            ),
            edit = @Edit(title = "选择产品",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "stockType = 'PRODUCT'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialName")
            )
    )
    private InventoryMTO productMTO;

    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(
                    title = "产品批号",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "lotSerialId"))
    )
    @Column(name = "product_code", length = 20)
    private String productCode;


    // 产品名称
    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(
                    title = "产品名称",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "materialName"))
    )
    @Column(name = "product_name", length = 20)
    private String productName;

    // 客户
    @FabosJsonField(
            views = @View(title = "客户"),
            edit = @Edit(
                    title = "客户",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "customer", length = 20)
    private String customer;

    // 总批量数
    @FabosJsonField(
            views = @View(title = "总批量数"),
            edit = @Edit(
                    title = "总批量数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "total_batch_quantity", length = 20)
    private Integer totalBatchQuantity;

    // 提出日期
    @FabosJsonField(
            views = @View(title = "提出日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "提出日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "raised_Date", nullable = false)
    private Date raisedDate;

    // 供应商
    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(
                    title = "供应商",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "supplier", length = 20)
    private String supplier;

    // 抽样数
    @FabosJsonField(
            views = @View(title = "抽样数"),
            edit = @Edit(
                    title = "抽样数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "sample_quantity", length = 20)
    private Integer sampleQuantity;

    // 提出人
    @Transient
    @FabosJsonField(
            views = @View(title = "选择提出人", column = "name", show = false),
            edit = @Edit(
                    title = "选择提出人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO reporterUserMTO;

    @FabosJsonField(
            views = @View(title = "提出人"),
            edit = @Edit(
                    title = "提出人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "name"))
    )
    @Column(name = "reporter", length = 20)
    private String reporter;

    @FabosJsonField(
            views = @View(title = "提出人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "提出人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "id"))
    )
    @Column(name = "reporter_id", length = 50)
    private String reporterId;

    // 联络人
    @Transient
    @FabosJsonField(
            views = @View(title = "选择联络人", column = "name", show = false),
            edit = @Edit(
                    title = "选择联络人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO contactUserMTO;

    @FabosJsonField(
            views = @View(title = "联络人"),
            edit = @Edit(
                    title = "联络人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "name"))
    )
    @Column(name = "contact_person", length = 20)
    private String contactPerson;

    @FabosJsonField(
            views = @View(title = "联络人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "联络人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "id"))
    )
    @Column(name = "contact_person_id", length = 50)
    private String contactPersonId;

    // 不良数量
    @FabosJsonField(
            views = @View(title = "不良数量"),
            edit = @Edit(
                    title = "不良数量",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "defective_count", length = 20)
    private Integer defectiveCount;

    // 要求完成日期
    @FabosJsonField(
            views = @View(title = "要求完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "要求完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "required_completion_date")
    private Date requiredCompletionDate;

    // 发生地点
    @FabosJsonField(
            views = @View(title = "发生地点"),
            edit = @Edit(
                    title = "发生地点",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "occurrence_location", length = 20)
    private String occurrenceLocation;

    @FabosJsonField(
            views = @View(title = "8D附件"),
            edit = @Edit(title = "8D附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String d8Attachment;

    // 状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "WAIT_SUBMIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = Report8DStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "status", length = 20)
    private String status;

    // 问题描述
    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(
                    title = "问题描述",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "problem_description", length = 100)
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "问题描述附件"),
            edit = @Edit(title = "问题描述附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String problemAttachment;

    // 根本原因
    @FabosJsonField(
            views = @View(title = "根本原因"),
            edit = @Edit(
                    title = "根本原因",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "root_cause", length = 100)
    private String rootCause;

    @FabosJsonField(
            views = @View(title = "根本原因附件"),
            edit = @Edit(title = "根本原因附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String causeAttachment;

    @FabosJsonField(
            views = @View(title = "审核人ID", show = false),
            edit = @Edit(title = "审核人ID", show = false)
    )
    @Column(name = "audit_person_id", length = 50)
    private String auditPersonId;

    @FabosJsonField(
            views = @View(title = "审核人", show = false),
            edit = @Edit(title = "审核人", show = false)
    )
    @Column(name = "audit_person_name", length = 50)
    private String auditPersonName;
    //临时措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "临时措施", column = "tempMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "临时措施",
                    type = EditType.TAB_TABLE_ADD,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(label = "tempMeasure")
            )
    )
    private List<TempMeasure> tempMeasureList;

    @FabosJsonField(
            views = @View(title = "临时措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "临时措施下所有责任人的id集合",show = false)
    )
    private String tempUserIds;
}
