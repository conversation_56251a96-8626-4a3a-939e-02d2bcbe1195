package cec.jiutian.bc.ppkManagement.domain.ppkPlan.proxy;

import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class PPKPlanDataProxy implements DataProxy<PPKPlan> {

    @Override
    public void beforeAdd(PPKPlan entity) {
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EDIT.name());
        if (CollectionUtils.isNotEmpty(entity.getDetails())) {
            entity.getDetails().forEach(d -> d.setIsGenerateTask(false));
        }
    }

    @Override
    public void beforeUpdate(PPKPlan entity) {
        if (CollectionUtils.isNotEmpty(entity.getDetails())) {
            entity.getDetails().forEach(d -> d.setIsGenerateTask(false));
        }
    }

}
