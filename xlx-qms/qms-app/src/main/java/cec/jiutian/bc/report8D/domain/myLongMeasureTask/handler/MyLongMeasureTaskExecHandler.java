package cec.jiutian.bc.report8D.domain.myLongMeasureTask.handler;

import cec.jiutian.bc.report8D.domain.myLongMeasureTask.model.MyLongMeasure;
import cec.jiutian.bc.report8D.domain.myLongMeasureTask.model.MyLongMeasureTask;
import cec.jiutian.bc.report8D.domain.myLongMeasureTask.model.MyLongMeasureTaskExec;
import cec.jiutian.bc.report8D.domain.report8D.model.LongMeasure;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class MyLongMeasureTaskExecHandler implements OperationHandler<MyLongMeasureTask, MyLongMeasureTaskExec> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyLongMeasureTask> data, MyLongMeasureTaskExec modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getMyLongMeasureList().forEach(d->{
                LongMeasure longMeasure = fabosJsonDao.findById(LongMeasure.class, d.getId());
                longMeasure.setProgress(d.getProgress());
                longMeasure.setCompletionDate(d.getCompletionDate());
                longMeasure.setCompletionEvidence(d.getCompletionEvidence());
                fabosJsonDao.mergeAndFlush(longMeasure);
            });
        }
        return "msg.success('操作成功')";
    }

    @Override
    public MyLongMeasureTaskExec fabosJsonFormValue(List<MyLongMeasureTask> data, MyLongMeasureTaskExec fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        String userId = UserContext.getUserId();
        MyLongMeasureTask myLongMeasureTask = data.get(0);
        BeanUtil.copyProperties(myLongMeasureTask, fabosJsonForm);

        //如果list为空 直接返回
        if (CollectionUtils.isEmpty(myLongMeasureTask.getLongMeasureList())) {
            return fabosJsonForm;
        }

        List<MyLongMeasure> myLongMeasureList = new ArrayList<>();
        myLongMeasureTask.getLongMeasureList().forEach(d->{
            if (Objects.equals(d.getResponsiblePersonId(), userId)) {
                MyLongMeasure myLongMeasure = new MyLongMeasure();
                BeanUtil.copyProperties(d, myLongMeasure);
                myLongMeasureList.add(myLongMeasure);
            }
        });

        fabosJsonForm.setMyLongMeasureList(myLongMeasureList);
        return fabosJsonForm;
    }
}
