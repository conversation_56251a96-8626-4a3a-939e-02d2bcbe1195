package cec.jiutian.bc.report8D.domain.report8D.proxy;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8DAdd;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class Report8DAddProxy implements DataProxy<Report8DAdd> {

    @Override
    public void beforeAdd(Report8DAdd report8DAdd) {
        report8DAdd.setStatus(Report8DStatusEnum.Enum.WAIT_SUBMIT.name());
        report8DAdd.setTempStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
        report8DAdd.setLongStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
        report8DAdd.setPreventStatus(MeasureCompleteStatusEnum.Enum.NOT_FINISH.name());
    }
}
