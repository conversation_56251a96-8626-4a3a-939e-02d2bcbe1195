package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.bc.report8D.enums.AuditResultEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report_audit_task")
@FabosJson(
        name = "我的审核任务",
        orderBy = "AuditTaskAudit.createTime desc",
        power = @Power(add = false, edit = false, delete = false, export = false)
)
public class AuditTaskAudit extends BaseModel {
    @FabosJsonField(
            views = @View(title = "审核意见"),
            edit = @Edit(
                    title = "审核意见",
                    inputType = @InputType(length = 10),
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = AuditResultEnum.class)
            )
    )
    @Column(length = 10)
    private String result;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(
                    title = "意见说明",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(length = 100)
    private String explain;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持5个大小为100M的文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String attachment;
}
