package cec.jiutian.bc.basicData.domain.inspectionStandard.proxy;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetail;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardLims;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class InspectionStandardLimsProxy implements DataProxy<InspectionStandardLims> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public void beforeAdd(InspectionStandardLims entity) {
        // 校验名称
        InspectionStandard condition = new InspectionStandard();
        condition.setName(entity.getName());
        InspectionStandard data = fabosJsonDao.selectOne(condition);
        if (null != data) {
            throw new FabosJsonApiErrorTip("名称不可重复，请确认");
        }
        checkNotNull(entity.getDetails());

        entity.setStatus(StatusEnum.Enum.Invalid.name());
        entity.setType(StandardTypeEnum.Enum.LIMS.name());

    }

    private void checkNotNull(List<InspectionStandardDetail> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            for (InspectionStandardDetail detail : details) {
                if (null == detail.getInspectionItemGroup() || null == detail.getInspectionItem()) {
                    throw new FabosJsonApiErrorTip("详情中检验项目和检验组必填，请确认");
                }
            }
        }
    }

    @Override
    @Transactional
    public void beforeUpdate(InspectionStandardLims entity) {
        if (!entity.getStatus().equals(StatusEnum.Enum.Invalid.name())) {
            throw new FabosJsonApiErrorTip("失效状态允许编辑");
        }
        checkNotNull(entity.getDetails());

    }

}
