package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.proxy;

import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.model.UnqualifiedReviewTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Component
public class UnqualifiedReviewTaskProxy implements DataProxy<UnqualifiedReviewTask> {
    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("当前用户未登录");
        }
        return "UnqualifiedReviewTask.unqualifiedAuditType in ('QUALITY', 'WORKMANSHIP', 'SQE') or UnqualifiedReviewTask.reviewerId = '" + userId + "'";
    }
}
