package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.AuditTask;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD4;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD4Handler implements OperationHandler<Report8D, Report8DD4> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD4 modelObject, String[] param) {

        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setRootCause(modelObject.getRootCause());
            report8D.setCauseAttachment(modelObject.getCauseAttachment());
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D4_AUDIT.name() : report8D.getStatus());
            report8D.setD4CreateBy(UserContext.getUserName());
            fabosJsonDao.mergeAndFlush(report8D);

            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }

        return "msg.success('操作成功')";
    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    @Override
    public Report8DD4 fabosJsonFormValue(List<Report8D> data, Report8DD4 fabosJsonForm, String[] param) {

        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            fabosJsonForm.setRootCause(report8D.getRootCause());
            fabosJsonForm.setCauseAttachment(report8D.getCauseAttachment());
        }
        return fabosJsonForm;
    }
}
