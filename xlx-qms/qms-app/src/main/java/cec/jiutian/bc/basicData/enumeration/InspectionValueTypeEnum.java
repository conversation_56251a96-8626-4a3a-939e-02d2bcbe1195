package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
public class InspectionValueTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
            enums.put(data.name(),data);
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        number("数值"),
        percentage("百分比"),
        text("文本"),
        ;

        private final String value;

    }
    private static HashMap<String,Enum> enums = new HashMap<>();

    public static Enum getEnum(String name) {
        return enums.get(name);
    }


}
