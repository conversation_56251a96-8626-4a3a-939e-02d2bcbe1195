package cec.jiutian.bc.basicData.enumeration;

import lombok.Getter;

import java.util.HashMap;

@Getter
public enum TargetTypeEnum {

    TEXT("/", InspectionValueTypeEnum.Enum.text),
    NUMBER("other", InspectionValueTypeEnum.Enum.number),
    PERCENTAGE("%", InspectionValueTypeEnum.Enum.percentage),
    ;

    private String key;

    private InspectionValueTypeEnum.Enum value;

    private TargetTypeEnum(String key, InspectionValueTypeEnum.Enum value) {
        this.key = key;
        this.value = value;
    }

    private static HashMap<String, TargetTypeEnum> map = new HashMap<>();
    static {
        for (TargetTypeEnum targetTypeEnum : TargetTypeEnum.values()) {
            map.put(targetTypeEnum.getKey(), targetTypeEnum);
        }
    }

    /**
     * 单位/ 为文本, % 为百分比，其余皆为数值
     * @param key
     * @return
     */
    public static TargetTypeEnum getTargetTypeEnum(String key) {
        return map.getOrDefault(key,NUMBER);
    }
}
