package cec.jiutian.bc.yieldWarning.domain.materialWarningRule.handler;

import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.model.MaterialWarningRule;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaterialWarningRuleInvalidOperationHandler implements OperationHandler<MaterialWarningRule, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MaterialWarningRule> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MaterialWarningRule entity = data.get(0);
            entity.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
