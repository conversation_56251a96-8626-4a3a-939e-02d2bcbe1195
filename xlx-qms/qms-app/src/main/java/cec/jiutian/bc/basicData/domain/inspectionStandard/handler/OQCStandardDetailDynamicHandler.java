package cec.jiutian.bc.basicData.domain.inspectionStandard.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetail;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardOQC;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class OQCStandardDetailDynamicHandler implements ReferenceAddType.ReferenceAddHandler<InspectionStandardOQC, InspectionItem> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InspectionStandardOQC entity, List<InspectionItem> inspectionItemList) {
        Map<String, Object> result = new HashMap<>();
        List<InspectionStandardDetail> list = new ArrayList<>();
        if (entity != null && CollectionUtils.isNotEmpty(inspectionItemList)) {
            for (InspectionItem item : inspectionItemList) {
                InspectionStandardDetail detail = new InspectionStandardDetail();
                detail.setOperationCode("FH");
                detail.setOperationName("发货");
                detail.setInspectionItem(item);
                detail.setItemId(item.getId());
                detail.setItemGroupIds(item.getGroupId());
                if (null != item.getGroupId()) {
                    String groupId = StringUtils.split(item.getGroupId(), ',')[0];
                    InspectionItemGroup group = fabosJsonDao.findById(InspectionItemGroup.class, groupId);
                    detail.setInspectionItemGroup(group);
                }
                detail.setInspectFrequency(item.getInspectFrequency());
                if (null != item.getAccountUnit().getId()) {
                    MeasureUnit unit = fabosJsonDao.findById(MeasureUnit.class, item.getAccountUnit().getId());
                    detail.setInspectFrequencyUnit(unit.getUnitChnName());
                }
                detail.setIsOpenDevice(item.getIsOpenDevice());
                detail.setIsRawMaterial(item.getIsRawMaterial());
                detail.setIsOpenLine(item.getIsOpenLine());

                if (null != item.getInspectionMethod()) {
                    InspectionMethod inspectionMethod = fabosJsonDao.findById(InspectionMethod.class, item.getInspectionMethod().getId());
                    detail.setInspectionMethod(inspectionMethod);
                }
                if (null != item.getSamplingPlan()) {
                    SamplingPlan samplingPlan = fabosJsonDao.findById(SamplingPlan.class, item.getSamplingPlan().getId());
                    detail.setSamplingPlan(samplingPlan);
                }
                list.add(detail);
            }
        }
        result.put("details", list);
        return result;
    }
}
