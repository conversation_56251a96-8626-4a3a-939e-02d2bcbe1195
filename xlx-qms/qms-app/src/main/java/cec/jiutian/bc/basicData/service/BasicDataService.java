package cec.jiutian.bc.basicData.service;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.CustomSamplingPlan;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.bc.modeler.enumration.AQLInspectionTypeEnum;
import cec.jiutian.bc.modeler.enumration.CustomSamplingTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description TODO
 */
@Service
public class BasicDataService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private AQLSamplingTables aqlSamplingTables;

    public void receiveBasicData(Map<String, Object> params) {
        InspectionStandard inspectionStandard = new InspectionStandard();
        inspectionStandard.setGeneralCode(String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionStandard.name(), 1, null).get(0)));

    }

    public Double getSampleQuantity(Double lotSize, SamplingPlan samplingPlan) {
        Double sampleSize = 0.0;
        if (samplingPlan != null && lotSize != null) {
            switch (samplingPlan.getSamplingStandard()) {
                case "AQLSampling" -> {
                    if (AQLInspectionTypeEnum.Enum.General.name().equals(samplingPlan.getAQLInspectionType())) {
                        sampleSize = (double) aqlSamplingTables.getAQlSampleQuantity(lotSize, samplingPlan.getGeneralLevelValue());
                    } else {
                        sampleSize = (double) aqlSamplingTables.getAQlSampleQuantity(lotSize, samplingPlan.getSpecialLevelValue());
                    }
                }
                case "fullSampling" -> sampleSize = lotSize;
                case "proportionSampling" -> {
                    try {
                        BigDecimal data = BigDecimal.valueOf(lotSize);
                        double per = samplingPlan.getProportion() / 100;
                        BigDecimal percentage = new BigDecimal(per);
                        sampleSize = data.multiply(percentage).doubleValue();
                    } catch (Exception e) {
                        throw new FabosJsonApiErrorTip("按比例抽样方案中比例输入格式有误");
                    }
                }
                case "fixedSampling" -> sampleSize = samplingPlan.getFixedCount();
                case "customSampling" -> sampleSize = getCustomSamplingPlanQuantity(samplingPlan, 1, lotSize);
            }
        }
        return sampleSize;
    }

    /**
     * 自定义抽样规则计算取样数量
     * @param samplingPlan
     * @param index
     * @param lotSize
     * @return
     */
    public Double getCustomSamplingPlanQuantity(SamplingPlan samplingPlan, int index, Double lotSize) {
        Double result = 0.0;
        if (samplingPlan != null && lotSize != null) {
            List<CustomSamplingPlan> customSamplingPlanList = samplingPlan.getCustomSamplingPlanList();
            if (CollectionUtils.isNotEmpty(customSamplingPlanList)) {
                for (CustomSamplingPlan customSamplingPlan : customSamplingPlanList) {
                    if (customSamplingPlan.getStartLot() <= index && index <= customSamplingPlan.getEndLot()) {
                        if (CustomSamplingTypeEnum.Enum.fixedSampling.name().equals(customSamplingPlan.getCustomSamplingType())) {
                            result = customSamplingPlan.getFixedCount();
                            break;
                        }else {
                            try {
                                BigDecimal data = BigDecimal.valueOf(lotSize);
                                double per = customSamplingPlan.getProportion() / 100;
                                BigDecimal percentage = new BigDecimal(per);
                                result = data.multiply(percentage).doubleValue();
                                break;
                            }catch (Exception e) {
                                throw new FabosJsonApiErrorTip("比例输入格式有误");
                            }
                        }
                    }
                }
            }
        }
        return result;
    }
}
