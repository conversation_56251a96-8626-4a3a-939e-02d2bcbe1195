package cec.jiutian.bc.mrb.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description
 */
public class MRBUnqualifiedBusinessStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        TO_BE_RELEASED("待发布"),
        UNDER_REVIEW("评审中"),
        PENDING("待处置"),
        TO_BE_SUBMITTED("待提交"),
        UNDER_DISPOSAL("处置中"),
        COMPLETED("已完成"),
        ;
        private final String value;
    }
}
