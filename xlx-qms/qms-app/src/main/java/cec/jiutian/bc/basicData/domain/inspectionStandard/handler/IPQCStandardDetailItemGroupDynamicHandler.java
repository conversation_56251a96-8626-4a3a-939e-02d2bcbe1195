package cec.jiutian.bc.basicData.domain.inspectionStandard.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetailIPQC;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.modeler.domain.inspectionMethod.model.InspectionMethod;
import cec.jiutian.bc.modeler.domain.samplingPlan.model.SamplingPlan;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class IPQCStandardDetailItemGroupDynamicHandler implements DependFiled.DynamicHandler<InspectionStandardDetailIPQC> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(InspectionStandardDetailIPQC detail) {
        Map<String, Object> result = new HashMap<>();
        InspectionItem item = detail.getInspectionItem();

        // todo 清空项目时，项目组也清空 未生效
        result.put("inspectionItemGroup", null);
        result.put("inspectionItemGroup_groupName", null);
        result.put("samplingPlan", null);
        result.put("samplingPlan_name", null);
        result.put("inspectionMethod", null);
        result.put("inspectionMethod_name", null);

        if (null != item) {
            if (null != item.getGroupId()) {
                String groupId = StringUtils.split(item.getGroupId(), ',')[0];
                InspectionItemGroup group = fabosJsonDao.findById(InspectionItemGroup.class, groupId);
                if (null != group) {
                    result.put("inspectionItemGroup", group);
                    result.put("inspectionItemGroup_groupName", group.getGroupName());
                }
            }
            if (null != item.getSamplingPlan()) {
                SamplingPlan samplingPlan = fabosJsonDao.findById(SamplingPlan.class, item.getSamplingPlan().getId());
                if (null != samplingPlan) {
                    result.put("samplingPlan", samplingPlan);
                    result.put("samplingPlan_name", samplingPlan.getName());
                }
            }
            if (null != item.getInspectionMethod()) {
                InspectionMethod inspectionMethod = fabosJsonDao.findById(InspectionMethod.class, item.getInspectionMethod().getId());
                if (null != inspectionMethod) {
                    result.put("inspectionMethod", inspectionMethod);
                    result.put("inspectionMethod_name", inspectionMethod.getName());
                }
            }
        }

        return result;
    }


}
