package cec.jiutian.bc.basicData.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @date 2025/3/10
 */
@Data
public class MsSipDetailResultDTO {
    private static final long serialVersionUID = 7879262570261663909L;

    @ApiModelProperty(name = "SIP GID", notes = "SIP GID")
    private String gid;

    @ApiModelProperty(name = "SIP ID", notes = "SIP ID")
    private String sipId;

    @ApiModelProperty(name = "step Index", notes = "序号")
    private Integer stepIndex;

    @ApiModelProperty(name = "item Name", notes = "项目名")
    private String itemName;

    @ApiModelProperty(name = "step Name", notes = "工步名称")
    private String stepName;

    @ApiModelProperty(name = "Process Operation Code", notes = "生产工序编码")
    private String processOperationCode;

    @ApiModelProperty(name = "Process Operation Name", notes = "生产工序名称")
    private String processOperationName;

    @ApiModelProperty(name = "material Code", notes = "辅料编码")
    private String materialCode;

    @ApiModelProperty(name = "material Name", notes = "辅料名称")
    private String materialName;

    @ApiModelProperty(name = "CC Flag", notes = "CC标识,TPOS:安全特殊性,TKP:关键特殊性")
    private String ccFlag;

    @ApiModelProperty(name = "SC Flag", notes = "SC标识,TIP:重要特殊性")
    private String scFlag;

    @ApiModelProperty(name = "unit Code", notes = "单位编码")
    private String unitCode;

    @ApiModelProperty(name = "standard", notes = "标准")
    private String standard;

    @ApiModelProperty(name = "deviation", notes = "偏差")
    private String deviation;

    @ApiModelProperty(name = "Inspect Frequency", notes = "检验频次")
    private String inspectFrequency;

    @ApiModelProperty(name = "deliver Inspect Point", notes = "送检点")
    private String deliverInspectPoint;

    @ApiModelProperty(name = "inspect Scheme", notes = "检测方法名称")
    private String inspectScheme;

    @ApiModelProperty(name = "inspect Scheme desc", notes = "检测方法描述")
    private String inspectSchemeDesc;

    @ApiModelProperty(name = "instrument", notes = "检测仪器")
    private String instrument;

    @ApiModelProperty(name = "Manage Scheme", notes = "管理方案")
    private String manageScheme;

    @ApiModelProperty(name = "Package Scheme", notes = "包装方案")
    private String packageScheme;

    @ApiModelProperty(name = "Sample Scheme", notes = "抽样方案")
    private String sampleScheme;

    @ApiModelProperty(name = "Sample Quantity", notes = "抽样标准量")
    private String sampleQuantity;

    @ApiModelProperty(name = "Sample Point", notes = "取样点")
    private String samplePoint;

    @ApiModelProperty(name = "Sample Quantity number", notes = "抽样样本个数")
    private String sampleQuantityNumber;

    @ApiModelProperty(name = "Sample Quantity deviation", notes = "样本偏差")
    private String sampleQuantityDeviation;

    @ApiModelProperty(name = "Sample Quantity unit", notes = "抽样单位")
    private String sampleQuantityUnit;

    @ApiModelProperty(name = "Sample packaging method", notes = "抽样包装")
    private String samplePackagingMethod;

    @ApiModelProperty(name = "Sample standard ", notes = "抽样标准")
    private String sampleStandard;


    public Double transSampleQuantityNumber() {
        if (StringUtils.isEmpty(this.sampleQuantityNumber) || "/".equals(this.sampleQuantityNumber)) {
            return null;
        }
        try {
            String substring = this.sampleQuantityNumber.substring(0, this.sampleQuantityNumber.length() - 2);
            return Double.valueOf(substring);
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
