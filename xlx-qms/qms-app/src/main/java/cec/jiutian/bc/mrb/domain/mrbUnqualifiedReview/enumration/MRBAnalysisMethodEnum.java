package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.enumration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
public class MRBAnalysisMethodEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Measure("预防纠正措施"),
        EightD("8D"),
        Paper("一页纸"),
        Report("事故报告"),
        SupplierReport("问题整改清单"),
        ;

        private final String value;

    }
}
