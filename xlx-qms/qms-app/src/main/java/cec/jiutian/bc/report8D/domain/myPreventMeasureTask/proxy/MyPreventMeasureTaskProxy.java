package cec.jiutian.bc.report8D.domain.myPreventMeasureTask.proxy;

import cec.jiutian.bc.report8D.domain.myPreventMeasureTask.model.MyPreventMeasureTask;
import cec.jiutian.bc.report8D.domain.report8D.model.PreventMeasures;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class MyPreventMeasureTaskProxy implements DataProxy<MyPreventMeasureTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        // 查询当前用户
        String userId = UserContext.getUserId();
        return "MyPreventMeasureTask.preventUserIds like '%"+userId+"%'";
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(d -> {
            MyPreventMeasureTask prevent = fabosJsonDao.findById(MyPreventMeasureTask.class, d.get("id").toString());
            int operateFlag = 0;
            for (PreventMeasures p : prevent.getPreventMeasuresList()) {
                // 不是当前用户 跳过
                if (!UserContext.getUserId().equals(p.getResponsiblePersonId())) {
                    continue;
                }
                // 只要前用户的状态 出现待执行 则显示按钮
                if (!MeasureProgressEnum.Enum.WAIT_VERIFY.name().equals(p.getProgress())) {
                    operateFlag = 1;
                    break;
                }
            }
            d.put("rowOperationAuthFlag", operateFlag);
        });
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        List<HashMap<String,Object>> list = (List<HashMap<String,Object>>) map.get("preventMeasuresList");
        List<HashMap<String,Object>> result = new ArrayList<>();
        for (HashMap<String, Object> data : list) {
            String responsiblePersonId = data.get("responsiblePersonId").toString();
            if (responsiblePersonId.equals(UserContext.getUserId())) {
                result.add(data);
            }
        }
        map.put("preventMeasuresList", result);
    }
}
