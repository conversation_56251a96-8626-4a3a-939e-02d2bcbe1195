package cec.jiutian.bc.basicData.domain.reportTemplate.handler;

import cec.jiutian.bc.basicData.domain.reportTemplate.model.MetaFieldMTO;
import cec.jiutian.bc.basicData.domain.reportTemplate.model.ShowField;
import cec.jiutian.bc.basicData.domain.reportTemplate.model.ReportTemplate;
import cec.jiutian.view.ReferenceAddType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class InsReportFiledAddHandler  implements ReferenceAddType.ReferenceAddHandler<ReportTemplate, MetaFieldMTO>  {
    @Override
    public Map<String, Object> handle(ReportTemplate inspectionReport, List<MetaFieldMTO> metaFieldMTOS) {

        Map<String, Object> result = new HashMap<>();
        List<ShowField> list = new ArrayList<>();
        for (MetaFieldMTO showFieldMTO : metaFieldMTOS) {
            ShowField showField = new ShowField();
            showField.setSimpleName(showFieldMTO.getName());
            showField.setFiledCnName(showFieldMTO.getDisplayName());
            showField.setMetaFieldId(showFieldMTO.getId());
            list.add(showField);
        }
        result.put("showFields", list);
        return result;
    }
}
