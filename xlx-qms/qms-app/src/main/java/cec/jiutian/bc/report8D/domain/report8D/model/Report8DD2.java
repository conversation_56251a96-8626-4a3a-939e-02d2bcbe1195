package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.InputType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "8D报告D2",
        orderBy = "Report8DD2.createTime desc"
)
public class Report8DD2 extends MetaModel {
    // 问题描述
    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(
                    title = "问题描述",
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "problem_description", length = 100)
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "问题描述附件"),
            edit = @Edit(title = "问题描述附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String problemAttachment;

}
