package cec.jiutian.bc.report8D.domain.myLongMeasureTask.handler;

import cec.jiutian.bc.report8D.domain.myLongMeasureTask.model.MyLongMeasureTask;
import cec.jiutian.bc.report8D.domain.report8D.model.LongMeasure;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MyLongMeasureTaskSubmitHandler implements OperationHandler<MyLongMeasureTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MyLongMeasureTask> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            MyLongMeasureTask myLongMeasureTask = data.get(0);
            List<LongMeasure> updateDetailList = new ArrayList<>();
            for (LongMeasure d : myLongMeasureTask.getLongMeasureList()) {
                if(!d.getResponsiblePersonId().equals(UserContext.getUserId())){
                    continue;
                }
                if(!MeasureProgressEnum.Enum.EXECUTED.name().equals(d.getProgress())){
                    throw new FabosJsonApiErrorTip("提交失败：需要完成所有任务才可提交");
                }
                updateDetailList.add(d);
            }

            updateDetailList.forEach(d->{
                d.setProgress(MeasureProgressEnum.Enum.WAIT_VERIFY.name());
                fabosJsonDao.mergeAndFlush(d);
            });

            Report8D report8D = fabosJsonDao.findById(Report8D.class, myLongMeasureTask.getId());
            if(checkCurrentStatus(report8D)){
                report8D.setLongStatus(MeasureCompleteStatusEnum.Enum.FINISH.name());
                fabosJsonDao.mergeAndFlush(report8D);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkCurrentStatus(Report8D report8D) {
        for (LongMeasure longMeasure : report8D.getLongMeasureList()) {
            if (!MeasureProgressEnum.Enum.WAIT_VERIFY.name().equals(longMeasure.getProgress())) {
                return false;
            }
        }
        return true;
    }
}
