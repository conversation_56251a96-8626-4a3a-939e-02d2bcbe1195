package cec.jiutian.bc.basicData.domain.processSampleWarningRule.proxy;

import cec.jiutian.bc.basicData.domain.processSampleWarningRule.model.SampleWarningRule;
import cec.jiutian.bc.changeRequestManagement.domain.ecr.mto.RoleMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SampleWarningRuleDataProxy implements DataProxy<SampleWarningRule> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("roleId") != null) {
            RoleMTO roleMTO = fabosJsonDao.findById(RoleMTO.class, map.get("roleId"));
            if (roleMTO != null) {
                map.put("roleMTO", roleMTO);
                map.put("roleMTO_name", roleMTO.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到角色：" + String.valueOf(map.get("roleName")) + "源数据，请确认");
            }
        }
    }

}
