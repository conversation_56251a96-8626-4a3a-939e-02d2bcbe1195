package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewCreateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description
 */
@Component
public class MRBUnqualifiedReviewCreateHandler implements OperationHandler<MRBUnqualifiedReview, MRBUnqualifiedReviewCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            modelObject.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.TO_BE_RELEASED.name());
            fabosJsonDao.mergeAndFlush(modelObject);
        } else {
            return "alert(数据异常)";
        }
        return "alert(操作成功)";
    }
}
