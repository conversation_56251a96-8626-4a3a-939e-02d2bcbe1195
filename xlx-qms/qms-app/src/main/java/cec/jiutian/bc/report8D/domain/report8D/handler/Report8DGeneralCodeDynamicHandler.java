package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DAdd;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class Report8DGeneralCodeDynamicHandler implements DependFiled.DynamicHandler<Report8DAdd> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(Report8DAdd report8DAdd) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(NamingRuleCodeEnum.Report8D.name(), 1, null).get(0)));
        return map;
    }
}
