package cec.jiutian.bc.mto;

import cec.jiutian.bc.modeler.enumration.LabViewTypeChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/7/16
 * @description TODO
 */
@FabosJson(
        name = "过程实验室MTO"
)
@Entity
@Getter
@Setter
@Table(name = "lm_lab")
public class LabMTO extends MetaModel {

    //实验室编码
    @FabosJsonField(
            views = @View(title = "编号"),
            edit = @Edit(title = "编号",notNull = true,search = @Search(vague = true))
    )
    private String labCode;
    //实验室名称
    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",notNull = true,search = @Search(vague = true))
    )
    private String labName;
    //实验室地址
    @FabosJsonField(
            views = @View(title = "地址"),
            edit = @Edit(title = "地址")
    )
    private String labAddress;
    //实验室属性
    @FabosJsonField(
            views = @View(title = "属性"),
            edit = @Edit(title = "属性")
    )
    private String labAttribute;

    // 实验室类型：包括检测中心、过程实验室、其他用户自定义的类型
    @FabosJsonField(
            views = @View(title = "类型"),
            edit = @Edit(title = "类型", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = LabViewTypeChoiceFetchHandler.class,
                            fetchHandlerParams = "LM_LAB_TYPE"),
                    search = @Search(vague = true))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "区域ID", show = false),
            edit = @Edit(title = "区域ID", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "lmFactoryAreaMTO", beFilledBy = "id"))
    )
    private String factoryAreaId;

    // 车间名称
    @FabosJsonField(
            views = @View(title = "生产区域名称"),
            edit = @Edit(title = "生产区域名称",
                    // type必须为过程实验室（02）时，才选择生产区域数据
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'LM_PROCESS_LAB'"),
                    search = @Search(vague = true), readonly = @Readonly)
    )
    private String factoryAreaName;

    //实验室介绍
    @FabosJsonField(
            views = @View(title = "介绍"),
            edit = @Edit(title = "介绍")
    )
    private String labIntroduce;
}
