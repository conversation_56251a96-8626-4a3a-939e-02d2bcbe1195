package cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlanEquipmentDetail;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlanGenerateTask;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.mto.PPKInspectionTaskMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class PPKInspectionTaskMTOReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<PPKPlanGenerateTask, PPKPlanEquipmentDetail> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(PPKPlanGenerateTask generateTaskMTO, List<PPKPlanEquipmentDetail> ppkPlanEquipmentDetails) {
        Map<String, Object> result = new HashMap<>();
        List<PPKInspectionTaskMTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ppkPlanEquipmentDetails)) {
            PPKPlanEquipmentDetail planDetail = ppkPlanEquipmentDetails.get(0);
            PPKPlan plan = fabosJsonDao.findById(PPKPlan.class, planDetail.getPpkPlan().getId());
            PPKInspectionTaskMTO taskMTO = new PPKInspectionTaskMTO();

            BeanUtils.copyProperties(planDetail, taskMTO);
            BeanUtils.copyProperties(plan, taskMTO);
            taskMTO.setId(null);
            taskMTO.setPpkPlanCode(plan.getGeneralCode());
            taskMTO.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.PPKInspectionTask.name(), 1, null).get(0));
            taskMTO.setMaterialName(plan.getMaterial().getName());
            taskMTO.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            taskMTO.setPlanDetailId(planDetail.getId());
            list.add(taskMTO);
            result.put("taskMTOList", list);
        }
        return result;
    }
}
