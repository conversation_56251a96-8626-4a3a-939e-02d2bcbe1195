package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD5;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD5SaveHandler implements OperationHandler<Report8D, Report8DD5> {

    @Resource
    private Report8DD5Handler report8DD5Handler;

    @Override
    public Report8DD5 fabosJsonFormValue(List<Report8D> data, Report8DD5 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD5Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
