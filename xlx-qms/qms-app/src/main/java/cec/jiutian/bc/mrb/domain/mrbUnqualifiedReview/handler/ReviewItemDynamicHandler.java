package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionItemDetail;
import cec.jiutian.bc.deliveryInspection.domain.inspectionTask.model.DeliveryInspectionTask;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionStandardDetail;
import cec.jiutian.bc.inventoryInspection.domain.inspectionTask.model.InventoryInspectionTask;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.IncomingInspectionTask;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.QualityInspectionStandardDetail;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.UnqualifiedReviewItem;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewCreateMTO;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTask;
import cec.jiutian.bc.processInspect.domain.processInspectionTask.model.ProcessInspectionTaskDetail;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnInspectionTask;
import cec.jiutian.bc.productReturnInspection.domain.inspectionTask.model.ProductReturnQualityInspectionStandardDetail;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description TODO
 */
@Component
public class ReviewItemDynamicHandler implements DependFiled.DynamicHandler<MRBUnqualifiedReviewCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public Map<String, Object> handle(MRBUnqualifiedReviewCreateMTO mrbUnqualifiedReview) {
        Map<String, Object> result = new HashMap<>();
        List<UnqualifiedReviewItem> itemList = new ArrayList<>();
        if (mrbUnqualifiedReview.getInspectionTaskMTO() != null && StringUtils.isNotEmpty(mrbUnqualifiedReview.getInspectionTaskType())) {
            switch (mrbUnqualifiedReview.getInspectionTaskType()) {
                case "processInspect" -> {
                    ProcessInspectionTask processInspectionTask = fabosJsonDao.findById(ProcessInspectionTask.class, mrbUnqualifiedReview.getInspectionTaskMTO().getId());
                    processInspectionTask.getDetails().forEach(detail -> {
                        InspectionItem inspectionItem = fabosJsonDao.findById(InspectionItem.class, detail.getItemId());
                        ProcessInspectionTaskDetail standardDetail = fabosJsonDao.findById(ProcessInspectionTaskDetail.class, detail.getId());
                        standardDetail.getTargetList().forEach(target -> {
                            UnqualifiedReviewItem item = new UnqualifiedReviewItem();
                            BeanUtils.copyProperties(target, item);
                            item.setItemId(detail.getItemId());
                            item.setName(detail.getItemName());
                            item.setInspectionMethodName(inspectionItem.getInspectionMethod().getName());
                            item.setId(null);
                            itemList.add(item);
                        });
                    });
                }
                case "materialInspect" -> {
                    IncomingInspectionTask incomingInspectionTask = fabosJsonDao.findById(IncomingInspectionTask.class, mrbUnqualifiedReview.getInspectionTaskMTO().getId());
                    incomingInspectionTask.getStandardDetailList().forEach(detail -> {
                        InspectionItem inspectionItem = fabosJsonDao.findById(InspectionItem.class, detail.getItemId());
                        QualityInspectionStandardDetail standardDetail = fabosJsonDao.findById(QualityInspectionStandardDetail.class, detail.getId());
                        standardDetail.getInspectionStandardItemTargetList().forEach(target -> {
                            UnqualifiedReviewItem item = new UnqualifiedReviewItem();
                            BeanUtils.copyProperties(target, item);
                            item.setItemId(detail.getItemId());
                            item.setName(detail.getName());
                            item.setInspectionMethodName(inspectionItem.getInspectionMethod().getName());
                            item.setId(null);
                            itemList.add(item);
                        });
                    });
                }
                case "deliveryInspect" -> {
                    DeliveryInspectionTask deliveryInspectionTask = fabosJsonDao.findById(DeliveryInspectionTask.class, mrbUnqualifiedReview.getInspectionTaskMTO().getId());
                    deliveryInspectionTask.getStandardDetailList().forEach(detail -> {
                        InspectionItem inspectionItem = fabosJsonDao.findById(InspectionItem.class, detail.getItemId());
                        DeliveryInspectionItemDetail standardDetail = fabosJsonDao.findById(DeliveryInspectionItemDetail.class, detail.getId());
                        standardDetail.getInspectionStandardItemTargetList().forEach(target -> {
                            UnqualifiedReviewItem item = new UnqualifiedReviewItem();
                            BeanUtils.copyProperties(target, item);
                            item.setItemId(detail.getItemId());
                            item.setName(detail.getName());
                            item.setInspectionMethodName(inspectionItem.getInspectionMethod().getName());
                            item.setId(null);
                            itemList.add(item);
                        });
                    });
                }
                case "inventoryInspect" -> {
                    InventoryInspectionTask inventoryInspectionTask = fabosJsonDao.findById(InventoryInspectionTask.class, mrbUnqualifiedReview.getInspectionTaskMTO().getId());
                    inventoryInspectionTask.getStandardDetailList().forEach(detail -> {
                        InspectionItem inspectionItem = fabosJsonDao.findById(InspectionItem.class, detail.getItemId());
                        InventoryInspectionStandardDetail standardDetail = fabosJsonDao.findById(InventoryInspectionStandardDetail.class, detail.getId());
                        standardDetail.getInventoryInspectionStandardItemTargetList().forEach(target -> {
                            UnqualifiedReviewItem item = new UnqualifiedReviewItem();
                            BeanUtils.copyProperties(target, item);
                            item.setItemId(detail.getItemId());
                            item.setName(detail.getName());
                            item.setInspectionMethodName(inspectionItem.getInspectionMethod().getName());
                            item.setId(null);
                            itemList.add(item);
                        });
                    });
                }
                case "productReturnInspect" -> {
                    ProductReturnInspectionTask productReturnInspectionTask = fabosJsonDao.findById(ProductReturnInspectionTask.class, mrbUnqualifiedReview.getInspectionTaskMTO().getId());
                    productReturnInspectionTask.getStandardDetailList().forEach(detail -> {
                        InspectionItem inspectionItem = fabosJsonDao.findById(InspectionItem.class, detail.getItemId());
                        ProductReturnQualityInspectionStandardDetail standardDetail = fabosJsonDao.findById(ProductReturnQualityInspectionStandardDetail.class, detail.getId());
                        standardDetail.getProductReturnInspectionStandardItemTargetList().forEach(target -> {
                            UnqualifiedReviewItem item = new UnqualifiedReviewItem();
                            BeanUtils.copyProperties(target, item);
                            item.setItemId(detail.getItemId());
                            item.setName(detail.getName());
                            item.setInspectionMethodName(inspectionItem.getInspectionMethod().getName());
                            item.setId(null);
                            itemList.add(item);
                        });
                    });
                }
            }
        }
        result.put("itemList",itemList);
        return result;
    }
}
