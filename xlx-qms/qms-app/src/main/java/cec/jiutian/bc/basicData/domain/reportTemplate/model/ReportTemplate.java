package cec.jiutian.bc.basicData.domain.reportTemplate.model;

import cec.jiutian.bc.basicData.domain.reportTemplate.handler.InsReportFiledAddHandler;
import cec.jiutian.bc.basicData.domain.reportTemplate.handler.ReportTemplateActiveOperationHandler;
import cec.jiutian.bc.basicData.domain.reportTemplate.handler.ReportTemplateInactiveOperationHandler;
import cec.jiutian.bc.basicData.domain.reportTemplate.proxy.ReportTemplateDataProxy;
import cec.jiutian.bc.basicData.enumeration.ReportTemplateStatus;
import cec.jiutian.bc.basicData.enumeration.ReportTemplateTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "ins_report_template")
@FabosJson(
        name = "报告模板管理",
        dataProxy = ReportTemplateDataProxy.class,
        orderBy = "createTime desc",
        power = @Power(importable = false, print = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "status=='ACTIVE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status!='INACTIVE'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "生效",
                        code = "ReportTemplate@ACTIVE",
                        operationHandler = ReportTemplateActiveOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "status=='ACTIVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReportTemplate@ACTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "失效",
                        code = "ReportTemplate@INACTIVE",
                        operationHandler = ReportTemplateInactiveOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        ifExpr = "status=='INACTIVE'", //禁用按钮
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "ReportTemplate@INACTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
public class ReportTemplate extends MetaModel {

    // 模板名称（如"IQC报告模板"）
    @FabosJsonField(
            views = @View(title = "模板名称"),
            edit = @Edit(title = "模板名称",
                    search = @Search(vague = true),
                    notNull = true
            )
    )
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @FabosJsonField(
            views = @View(title = "模板类型"),
            edit = @Edit(title = "模板类型",
                    search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ReportTemplateTypeEnum.ChoiceFetch.class)
            )
    )
    private String reportType;

    @FabosJsonField(
            views = @View(title = "报告显示字段",
                    column = "filedCnName",
                    type = ViewType.TABLE_VIEW,
                    extraPK = "metaFieldId"),
            edit = @Edit(title = "报告显示字段",
                    type = EditType.TAB_REFER_ADD

            ),
            referenceAddType = @ReferenceAddType(referenceClass = "MetaFieldMTO",
                    filter = "referenceModel.name = 'InspectionReportView'",
                    referenceAddHandler = InsReportFiledAddHandler.class
            )
    )
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "inspect_report_template_id")
    private List<ShowField> showFields;


    // 状态（CREATED/EDIT/ACTIVE/INACTIVE）
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    notNull = true,
                    readonly = @Readonly(
                            add = true,
                            edit = false
                    ),
                    search = @Search,
                    type = EditType.CHOICE,
                    defaultVal = "CREATED",
                    choiceType = @ChoiceType(fetchHandler = ReportTemplateStatus.ChoiceFetch.class)
            )
    )
    @Column(name = "status", nullable = false, length = 20)
    private String status;


    // 创建人（手动录入或系统记录）
    @FabosJsonField(
            views = @View(title = "创建人"),
            edit = @Edit(title = "创建人",
                    show = false,
                    readonly = @Readonly(
                            add = true,
                            edit = true
                    )
            )
    )
    @Column(name = "created_by", length = 50)
    private String createdBy;


    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",
                    show = false,
                    readonly = @Readonly(
                            add = true,
                            edit = true
                    )
            )
    )
    // 创建时间
    @CreationTimestamp
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createdAt;

    // 最后修改时间
    @FabosJsonField(
            views = @View(title = "最后修改时间"),
            edit = @Edit(title = "最后修改时间",
                    show = false,
                    readonly = @Readonly(
                            add = true,
                            edit = true
                    )
            )
    )
    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updatedAt;

}

