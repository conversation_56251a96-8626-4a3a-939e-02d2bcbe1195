package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Entity
@Table(name = "batch_serial")
@Getter
@Setter
@FabosJsonI18n
@FabosJson(
        name = "工序流水",
        orderBy = "serialNumber desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class ProcessFlowMTO {
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id")
    )
    @Id
    @Column(name = "id")
    private String id;

    @FabosJsonField(
            views = @View(title = "工序流水号"),
            edit = @Edit(title = "工序流水号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "车间ID"),
            edit = @Edit(title = "车间ID",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "workshop_id")
    private String workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "workshop_name")
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线ID"),
            edit = @Edit(title = "产线ID",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "production_line_id")
    private String productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "production_line_name")
    private String productionLineName;
}
