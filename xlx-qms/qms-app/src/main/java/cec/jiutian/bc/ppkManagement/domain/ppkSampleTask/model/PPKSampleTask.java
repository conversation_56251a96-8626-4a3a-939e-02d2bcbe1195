package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.model;

import cec.jiutian.bc.cmkManagement.enumeration.GatherTimingTypeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler.PPKGetSampleOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler.PPKReceiveSampleOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler.PPKSendSampleOperationHandler;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskGetMTO;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskReceiveMTO;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskSendMTO;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "PPK取样任务",
        orderBy = "createTime desc",
        power = @Power(print = true, add = false, edit = false, delete = false),
        rowOperation = {
                @RowOperation(
                        title = "取样",
                        code = "PPKSampleTask@GET",
                        operationHandler = PPKGetSampleOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = PPKSampleTaskGetMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PPKSampleTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].businessState != 'BE_SAMPLING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "送样",
                        code = "PPKSampleTask@SEND",
                        operationHandler = PPKSendSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = PPKSampleTaskSendMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PPKSampleTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "收样",
                        code = "PPKSampleTask@RECEIVE",
                        operationHandler = PPKReceiveSampleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = PPKSampleTaskReceiveMTO.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "PPKSampleTask@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
@Table(name = "qms_ppk_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class PPKSampleTask extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.PPKSampleTask.name();
    }

    @FabosJsonField(
            views = @View(title = "PPK计划单号"),
            edit = @Edit(title = "PPK计划单号", readonly = @Readonly)
    )
    private String ppkPlanCode;

    @FabosJsonField(
            views = @View(title = "PPK检验任务单号"),
            edit = @Edit(title = "PPK检验任务单号", readonly = @Readonly)
    )
    private String ppkInspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "PPK检验任务详情id", show = false),
            edit = @Edit(title = "PPK检验任务详情id", show = false)
    )
    private String ppkInspectionTaskDetailId;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            )
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            )
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "工序", column = "name"),
            edit = @Edit(title = "工序",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "product_process_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ProductProcessMTO productProcessMTO;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号", readonly = @Readonly)
    )
    private String equipmentArchiveCode;

    @FabosJsonField(
            views = @View(title = "设备简称"),
            edit = @Edit(title = "设备简称", readonly = @Readonly)
    )
    private String abbreviation;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点")
    )
    private String getSamplePoint;

    @FabosJsonField(
            views = @View(title = "送样点"),
            edit = @Edit(title = "送样点")
    )
    private String sendSamplePoint;

    @FabosJsonField(
            views = @View(title = "评判项目"),
            edit = @Edit(title = "评判项目", readonly = @Readonly)
    )
    private String judgeItem;

    @FabosJsonField(
            views = @View(title = "采集时机"),
            edit = @Edit(title = "采集时机", readonly = @Readonly, type = EditType.CHOICE, choiceType = @ChoiceType(fetchHandler = GatherTimingTypeEnum.class))
    )
    private String gatherTiming;

    @FabosJsonField(
            views = @View(title = "其他采集时机"),
            edit = @Edit(title = "其他采集时机", notNull = true, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "gatherTiming == 'Other'"))
    )
    private String otherGatherTiming;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量", readonly = @Readonly,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0, precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "取样次数"),
            edit = @Edit(title = "取样次数", readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)
    )
    private String sampleFrequency;

    @FabosJsonField(
            views = @View(title = "取样方式"),
            edit = @Edit(title = "取样方式")
    )
    private String getSampleWay;

    @FabosJsonField(
            views = @View(title = "封装要求"),
            edit = @Edit(title = "封装要求")
    )
    private String packageRequire;

    @FabosJsonField(
            views = @View(title = "实际取样流水号"),
            edit = @Edit(title = "实际取样流水号")
    )
    private String actualSerialNumber;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", show = false, type = EditType.CHOICE,
                    defaultVal = "BE_SAMPLING",
                    choiceType = @ChoiceType(fetchHandler = SampleStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时间", show = false, type = ViewType.DATE),
            edit = @Edit(title = "取样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date getSampleDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "送样时间", show = false, type = ViewType.DATE),
            edit = @Edit(title = "送样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date sendSampleDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "收样时间", show = false, type = ViewType.DATE),
            edit = @Edit(title = "收样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date ReceiveSampleDate;
}
