package cec.jiutian.bc.basicData.domain.processSampleWarningRule.handler;

import cec.jiutian.bc.basicData.domain.processSampleWarningRule.model.SampleWarningRule;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SampleWarningRuleInvalidOperationHandler implements OperationHandler<SampleWarningRule, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<SampleWarningRule> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            SampleWarningRule entity = data.get(0);
            entity.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
