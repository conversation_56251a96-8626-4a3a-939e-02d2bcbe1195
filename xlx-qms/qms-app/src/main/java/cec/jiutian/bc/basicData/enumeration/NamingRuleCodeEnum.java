package cec.jiutian.bc.basicData.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description TODO
 */
@AllArgsConstructor
@Getter
public enum NamingRuleCodeEnum {
    InspectionMethodCode("检验方法"),
    InspectionInstrument("检验仪器"),
    SamplingPlan("抽样方案"),
    PatrolInspectionItem("巡检项"),
    InspectionItem("检验项目"),
    QuantityPreparationPlan("质量准备计划"),
    InspectionItemGroup("检验项目组"),
    InspectionTask("来料检验任务"),
    SamplingTask("取样任务"),
    InspectionStandard("质检标准"),
    ProcessInspectionPlan("过程检验计划"),
    ProcessInspectionTask("过程检验任务"),
    OnSiteInspectionTask("现场巡检任务"),
    ProcessSampleTask("过程取样任务"),
    InspectionRequest("来料检验检验通知"),
    ProductReturnInspectionRequest("成品退货检验通知"),
    InventoryInspectionRequest("库存检验通知"),
    CustomerReturnProductHandle("顾客退货产品处置单"),
    ABNORMAL_FEEDBACK_HANDLING("异常反馈处理"),
    CORRECT_PREVENT_MEASURE("纠正预防措施"),
    AbnormalStopProduction("异常停产单"),
    SYSTEM_AUDIT_REPORT("审核报告"),
    SYSTEM_AUDIT_LIST_OF_ISSUE("问题清单"),
    ExamineItem("供应商审核项"),
    SupplierInventory("供应商基础台账"),
    HSFEnvironmentSubstanceInventory("HSF环境物质台账"),
    SupplierAuditTemplate("供应商审核模板"),
    SupplierAnnualAuditPlan("供应商年度审核计划"),
    SupplierAuditImplementPlan("供应商审核实施计划"),
    PROCESS_AUDIT_REPORT("过程审核报告"),
    PROCESS_AUDIT_LIST_OF_ISSUE("过程审核问题清单"),
    CLIENT_COMPLAINT_MECHANISM("客诉机制"),
    CLIENT_COMPLAINT_FEEDBACK("客诉反馈"),
    PROBLEM_IMPROVEMENT("问题改善"),
    REVIEW_PROBLEM_IMPROVEMENT("客诉评审问题改善"),
    ManagementReviewPlan("管理评审计划"),
    CustomerReviewTask("客户评审任务"),

    ManagementReviewTask("管理评审任务"),
    MrImprovementImplementationPlan("管理评审改进项目实施计划"),
    SAMPLE_MANAGEMENT("还样管理"),
    QuestionPaper("问题一页纸"),
    ACCIDENT_REPORT_TASK("事故报告任务"),

    MaterialWarningRule("来料预警规则"),
    ProductQualityAlert("制程成品良品率预警"),
    PRODUCT_QUALITY_TRACK("产品品质跟踪"),
    SampleWarningRule("取样超时预警规则"),
    ;

    private final String value;
}
