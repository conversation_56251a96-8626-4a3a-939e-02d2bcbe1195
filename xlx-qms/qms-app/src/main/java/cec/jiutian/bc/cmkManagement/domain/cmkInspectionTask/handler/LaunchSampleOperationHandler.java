package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTaskDetail;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto.LaunchSampleOprMTO;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Component
public class LaunchSampleOperationHandler implements OperationHandler<CMKInspectionTask,LaunchSampleOprMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKInspectionTask> data, LaunchSampleOprMTO modelObject, String[] param) {
        if (modelObject.getCount() != null) {
            CMKInspectionTask cmkInspectionTask = data.get(0);
            CMKSampleTask cmkSampleTask = new CMKSampleTask();
            BeanUtils.copyProperties(cmkInspectionTask, cmkSampleTask);
            cmkSampleTask.setId(null);
            cmkSampleTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.CMKSampleTask.name(), 1, null).get(0));
            cmkSampleTask.setCmkInspectionTaskCode(cmkInspectionTask.getGeneralCode());
            cmkSampleTask.setBusinessState(SampleStateEnum.Enum.BE_SAMPLING.name());
            cmkSampleTask.setSampleFrequency(modelObject.getCount() + "/" + cmkInspectionTask.getSampleCount());
            fabosJsonDao.mergeAndFlush(cmkSampleTask);

            cmkInspectionTask.setSampleTaskCount(Integer.parseInt(modelObject.getCount()) + 1);
            List<CMKInspectionTaskDetail> detailList = new ArrayList<>();
            CMKInspectionTaskDetail condition = new CMKInspectionTaskDetail();
            condition.setCmkInspectionTask(cmkInspectionTask);
            List<CMKInspectionTaskDetail> detailDataList = fabosJsonDao.select(condition);
            if (CollectionUtils.isNotEmpty(detailDataList)) {
                detailList = detailDataList;
            }
            CMKInspectionTaskDetail cmkInspectionTaskDetail = new CMKInspectionTaskDetail();
            cmkInspectionTaskDetail.setBusinessState(SampleStateEnum.Enum.BE_SAMPLING.name());
            cmkInspectionTaskDetail.setSampleTaskCode(cmkSampleTask.getGeneralCode());
            detailList.add(cmkInspectionTaskDetail);
            cmkInspectionTask.setDetailList(detailList);
            fabosJsonDao.mergeAndFlush(cmkInspectionTask);
        }else {
            throw new FabosJsonApiErrorTip("当前CMK检验任务已创建完所有取样任务");
        }
        return "alert('操作成功')";
    }

    @Override
    public LaunchSampleOprMTO fabosJsonFormValue(List<CMKInspectionTask> data, LaunchSampleOprMTO fabosJsonForm, String[] param) {
        CMKInspectionTask cmkInspectionTask = data.get(0);
        int taskCount = cmkInspectionTask.getSampleTaskCount() == null ? 1 : cmkInspectionTask.getSampleTaskCount();
        if (cmkInspectionTask.getSampleCount() >= taskCount) {
            fabosJsonForm.setCount(String.valueOf(taskCount));
        }else {
            throw new FabosJsonApiErrorTip("当前CMK检验任务已创建完所有取样任务");
        }
        return fabosJsonForm;
    }
}
