package cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlan;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlanEquipmentDetail;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto.CMKInspectionTaskMTO;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto.GenerateTaskMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description TODO
 */
@Component
public class CMKInspectionTaskMTOReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<GenerateTaskMTO, CMKPlanEquipmentDetail> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(GenerateTaskMTO generateTaskMTO, List<CMKPlanEquipmentDetail> cmkPlanEquipmentDetails) {
        Map<String, Object> result = new HashMap<>();
        List<CMKInspectionTaskMTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cmkPlanEquipmentDetails)) {
            CMKPlanEquipmentDetail cmkPlanEquipmentDetail = cmkPlanEquipmentDetails.get(0);
            CMKPlan cmkPlan = fabosJsonDao.findById(CMKPlan.class, cmkPlanEquipmentDetail.getCmkPlan().getId());
            CMKInspectionTaskMTO cmkInspectionTaskMTO = new CMKInspectionTaskMTO();

            BeanUtils.copyProperties(cmkPlanEquipmentDetail,cmkInspectionTaskMTO);
            BeanUtils.copyProperties(cmkPlan,cmkInspectionTaskMTO);
            cmkInspectionTaskMTO.setId(null);
            cmkInspectionTaskMTO.setCmkPlanCode(cmkPlan.getGeneralCode());
            cmkInspectionTaskMTO.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.CMKInspectionTask.name(), 1, null).get(0));
            cmkInspectionTaskMTO.setMaterialName(cmkPlan.getMaterial().getName());
            cmkInspectionTaskMTO.setBusinessState(OrderCurrentStateEnum.Enum.EXECUTE.name());
            cmkInspectionTaskMTO.setPlanDetailId(cmkPlanEquipmentDetail.getId());
            list.add(cmkInspectionTaskMTO);
            result.put("taskMTOList",list);
        }
        return result;
    }
}
