package cec.jiutian.bc.basicData.domain.patrolInspectionItem.handler;

import cec.jiutian.bc.basicData.domain.patrolInspectionItem.model.PatrolInspectionItem;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PIItemInValidOperationHandler implements OperationHandler<PatrolInspectionItem, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<PatrolInspectionItem> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            PatrolInspectionItem item = data.get(0);
            item.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(item);
        }
        return "msg.success('操作成功')";
    }
}
