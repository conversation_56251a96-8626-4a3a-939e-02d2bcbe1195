package cec.jiutian.bc.basicData.remote.feign;

import cec.jiutian.bc.basicData.dto.MsSipResultDTO;
import cec.jiutian.bc.basicData.service.BasicDataCreateFromSIPService;
import cec.jiutian.bc.basicData.service.BasicDataService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description TODO
 */
@Slf4j
@RestController
@RequestMapping("/fabos-qms-app" + FabosJsonRestPath.FABOS_REMOTE_API)
public class BasicDataFeign {

    @Resource
    private BasicDataService basicDataService;

    @Resource
    private BasicDataCreateFromSIPService basicDataCreateFromSIPService;

    @PostMapping("/receiveSIPData")
    public void receiveSIPData(@RequestBody Map<String, Object> params) {
        log.info("接收参数：{}", params);
        basicDataService.receiveBasicData(params);
    }

    @PostMapping("/createBasicDataFromSIP")
    public boolean createBasicData(@RequestBody MsSipResultDTO msSipResultDTO) {
        log.info("接收参数：{}", msSipResultDTO);
        return basicDataCreateFromSIPService.createBasicData(msSipResultDTO);
    }

}
