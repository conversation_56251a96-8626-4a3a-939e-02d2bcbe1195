package cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlan;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlanEquipmentDetail;
import cec.jiutian.bc.mto.EquipmentArchiveMTO;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
public class CMKDetailReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<CMKPlan, EquipmentArchiveMTO> {
    @Override
    public Map<String, Object> handle(CMKPlan cmkPlan, List<EquipmentArchiveMTO> equipmentArchiveMTOS) {
        Map<String, Object> result = new HashMap<>();
        List<CMKPlanEquipmentDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(equipmentArchiveMTOS)) {
            equipmentArchiveMTOS.forEach(equipment -> {
                CMKPlanEquipmentDetail cmkPlanEquipmentDetail = new CMKPlanEquipmentDetail();
                cmkPlanEquipmentDetail.setEquipmentArchiveId(equipment.getId());
                cmkPlanEquipmentDetail.setEquipmentArchiveCode(equipment.getGeneralCode());
                cmkPlanEquipmentDetail.setName(equipment.getName());
                list.add(cmkPlanEquipmentDetail);
            });
        }
        result.put("detailList",list);
        return result;
    }
}
