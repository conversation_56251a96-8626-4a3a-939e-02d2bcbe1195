package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum GroupType {

    IPQC("IPQC"),
    IQC("IQC"),
    OQC("OQC"),
    SAMPLE("SAMPLE"),
    OTHER("其他");

    private final String value;

    GroupType(String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(GroupType.values()).map(groupType ->
                    new VLModel(groupType.name(), groupType.getValue())).collect(Collectors.toList());
        }
    }
}