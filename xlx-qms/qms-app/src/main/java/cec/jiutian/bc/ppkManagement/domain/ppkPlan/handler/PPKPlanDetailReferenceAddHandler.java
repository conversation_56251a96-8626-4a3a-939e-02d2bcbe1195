package cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler;

import cec.jiutian.bc.mto.EquipmentArchiveMTO;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlanEquipmentDetail;
import cec.jiutian.view.ReferenceAddType;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PPKPlanDetailReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<PPKPlan, EquipmentArchiveMTO> {
    @Override
    public Map<String, Object> handle(PPKPlan entity, List<EquipmentArchiveMTO> equipmentArchiveMTOS) {
        Map<String, Object> result = new HashMap<>();
        List<PPKPlanEquipmentDetail> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(equipmentArchiveMTOS)) {
            equipmentArchiveMTOS.forEach(equipment -> {
                PPKPlanEquipmentDetail detail = new PPKPlanEquipmentDetail();
                detail.setEquipmentArchiveId(equipment.getId());
                detail.setEquipmentArchiveCode(equipment.getCode());
                detail.setAbbreviation(equipment.getAbbreviation());
                list.add(detail);
            });
        }
        result.put("details", list);
        return result;
    }
}
