package cec.jiutian.bc.mrb.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
public class UnqualifiedAuditTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        QUALITY("待品质检验分析和/或评审"),
        WORKMANSHIP ("待工艺技术分析和/或评审"),
        SQE("待供应商或客户分析和/或评审"),

        QUALITY_DEPART("待质量管理部评审处置方案"),
        RESPONSE_LEADER("待分管领导评审处置方案"),
        GENERAL_MANAGER ("待总经理评审处置方案"),
        ;

        private final String value;

    }
}
