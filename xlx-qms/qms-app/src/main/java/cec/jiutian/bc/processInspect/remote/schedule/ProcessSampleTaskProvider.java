package cec.jiutian.bc.processInspect.remote.schedule;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.processInspect.domain.processSampleTask.model.ProcessSampleTask;
import cec.jiutian.bc.processInspect.service.ProcessInspectService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 过程检取样任务定时任务
 */
@FabosCustomizedService(value = ProcessSampleTask.class)
@Component
@Transactional
@Slf4j
public class ProcessSampleTaskProvider implements IJobProvider {
    @Resource
    private ProcessInspectService processInspectService;

    // 后续添加到任务配置中@Scheduled(cron = "0 */5 * * * ?")
    @Override
    @FabosJob(comment = "过程检取样任务-取样超时报警")
    public String exec(String code, String param) {
        log.info("过程检取样任务取样超时报警定时任务开始执行");
        processInspectService.sampleTaskSampleOverTime();
        return null;
    }

}
