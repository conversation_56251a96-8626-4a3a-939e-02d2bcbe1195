package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto;

import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.enumration.MRBAnalysisMethodEnum;
import cec.jiutian.bc.modeler.enumration.MRBDisposalOpinionEnum;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Getter
@Setter
@Entity
@Table(name = "qms_mrb_unqualified_review_task")
@FabosJson(
        name = "审核",
        orderBy = "UnqualifiedReviewDisposalAudit.createTime desc",
        power = @Power(add = false,delete = false,edit = false)
)
public class UnqualifiedReviewDisposalAudit extends MetaModel {
    //不合格评审单带下来的
    @FabosJsonField(
            views = @View(title = "处置意见"),
            edit = @Edit(title = "处置意见", readonly = @Readonly(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBDisposalOpinionEnum.class))
    )
    private String handlingSuggestion;

    @FabosJsonField(
            views = @View(title = "分析方法"),
            edit = @Edit(title = "分析方法", readonly = @Readonly(), type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MRBAnalysisMethodEnum.class))
    )
    private String analysisMethod;

    @FabosJsonField(
            views = @View(title = "作业指导书"),
            edit = @Edit(title = "作业指导书",
                    type = EditType.TEXTAREA, readonly = @Readonly(), inputType = @InputType(length = 400),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "handlingSuggestion != 'REWORK'", enableOrDisable = "handlingSuggestion != 'REWORK'")
            )
    )
    private String workingInstruction;
    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件",
                    type = EditType.ATTACHMENT, readonly = @Readonly(),
                    attachmentType = @AttachmentType(size = 5120, maxLimit = 10, type = AttachmentType.Type.BASE)
            )
    )
    private String disposalAttachment;

    //评审
    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedReviewTaskCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    search = @Search(vague = true),
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;
}
