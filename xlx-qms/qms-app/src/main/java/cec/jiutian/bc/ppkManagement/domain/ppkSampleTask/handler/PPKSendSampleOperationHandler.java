package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler;

import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.model.PPKInspectionTaskDetail;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.model.PPKSampleTask;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskSendMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PPKSendSampleOperationHandler implements OperationHandler<PPKSampleTask, PPKSampleTaskSendMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<PPKSampleTask> data, PPKSampleTaskSendMTO modelObject, String[] param) {
        if (modelObject != null) {
            PPKSampleTask condition = new PPKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            PPKSampleTask sampleTask = fabosJsonDao.selectOne(condition);

            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(sampleTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            fabosJsonDao.mergeAndFlush(sampleTask);

            PPKInspectionTaskDetail inspectionTaskDetail = fabosJsonDao.findById(PPKInspectionTaskDetail.class, sampleTask.getPpkInspectionTaskDetailId());
            inspectionTaskDetail.setCurrentState(sampleTask.getBusinessState());
            fabosJsonDao.mergeAndFlush(inspectionTaskDetail);
        }
        return "alert(操作成功)";
    }
}
