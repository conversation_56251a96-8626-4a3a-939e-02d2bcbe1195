package cec.jiutian.bc.utils;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.data.factory.SpringBeanUtils;

public class MsgUtil {

    private volatile static EcsMessageProvider ecsMessageProvider;

    private static EcsMessageProvider getEcsMessageProvider() {
        if (ecsMessageProvider == null) {
            synchronized (MsgUtil.class) {
                if (ecsMessageProvider == null) {
                    ecsMessageProvider = SpringBeanUtils.getBean(EcsMessageProvider.class);
                    if (ecsMessageProvider == null) {
                        throw new ServiceException("未接入消息中心");
                    }
                }
            }
        }
        return ecsMessageProvider;
    }

    public static void sendGroupMessage(SendMsgGroupDTO sendMsgGroupDTO) {
        getEcsMessageProvider().sendGroupMessage(sendMsgGroupDTO);
    }

    public static void sendPersonMessage(SendMsgToPersonDTO sendMsgGroupDTO) {
        getEcsMessageProvider().sendPersonMessage(sendMsgGroupDTO);
    }

}
