package cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler;

import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PPKPlanPublishOperationHandler implements OperationHandler<PPKPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<PPKPlan> data, Void modelObject, String[] param) {
        PPKPlan entity = data.get(0);
        if (CollectionUtils.isEmpty(entity.getDetails())) {
            throw new FabosJsonApiErrorTip("计划设备信息不能为空");
        }
        entity.setCurrentState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        fabosJsonDao.mergeAndFlush(entity);

        return "alert('操作成功')";
    }
}
