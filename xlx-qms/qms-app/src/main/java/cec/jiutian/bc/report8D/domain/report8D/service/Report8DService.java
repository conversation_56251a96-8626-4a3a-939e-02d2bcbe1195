package cec.jiutian.bc.report8D.domain.report8D.service;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.vo.Report8DAddVO;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DAdd;
import cec.jiutian.bc.report8D.domain.report8D.proxy.Report8DAddProxy;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class Report8DService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private Report8DAddProxy report8DAddProxy;

    public String createReport8D(Report8DAddVO report8DAddVO) {
        Report8DAdd report8DAdd = new Report8DAdd();
        BeanUtils.copyNotEmptyProperties(report8DAddVO,report8DAdd);
        String code = namingRuleService.getNameCode(NamingRuleCodeEnum.Report8D.name(), 1, null).get(0);
        report8DAdd.setGeneralCode(code);
        report8DAddProxy.beforeAdd(report8DAdd);
        fabosJsonDao.persistAndFlush(report8DAdd);
        return code;
    }
}
