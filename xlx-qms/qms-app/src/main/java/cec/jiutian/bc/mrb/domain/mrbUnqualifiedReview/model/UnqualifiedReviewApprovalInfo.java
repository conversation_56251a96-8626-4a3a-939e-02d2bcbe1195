package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model;

import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedAuditTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@FabosJson(
        name = "审核信息"
)
@Table(name = "qms_mrb_unqualified_review_approval")
@Entity
@Data
public class UnqualifiedReviewApprovalInfo extends MetaModel {
    @FabosJsonField(
            views = @View(title = "评审人ID", show = false),
            edit = @Edit(title = "评审人ID", show = false)
    )
    private String reviewerId;

    @FabosJsonField(
            views = @View(title = "评审人"),
            edit = @Edit(title = "评审人", show = false)
    )
    private String reviewerName;

    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见", notNull = true, type = EditType.CHOICE,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedReviewTaskCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    search = @Search(vague = true),
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "不合格审核类型"),
            edit = @Edit(title = "不合格审核类型",
                    show = false,
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedAuditTypeEnum.class)
            )
    )
    private String unqualifiedAuditType;

    @FabosJsonField(
            views = @View(title = "审核完成时间"),
            edit = @Edit(title = "审核完成时间", dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date time;
}
