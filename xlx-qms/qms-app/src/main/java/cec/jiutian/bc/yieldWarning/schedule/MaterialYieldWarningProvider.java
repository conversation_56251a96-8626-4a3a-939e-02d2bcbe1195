package cec.jiutian.bc.yieldWarning.schedule;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.model.MaterialWarningRule;
import cec.jiutian.bc.yieldWarning.service.YieldWarningService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@FabosCustomizedService(value = MaterialWarningRule.class)
@Component
@Transactional
@Slf4j
public class MaterialYieldWarningProvider implements IJobProvider {
    @Resource
    private YieldWarningService yieldWarningService;

    // 任务配置中@Scheduled(cron = "0 0 7 ? * 1")
    @Override
    @FabosJob(comment = "来料合格率预警通知")
    public String exec(String code, String param) {
        log.info("每周一早7点统计上周来料合格率定时任务开始执行");
        yieldWarningService.materialYieldWarning();
        return null;
    }

}
