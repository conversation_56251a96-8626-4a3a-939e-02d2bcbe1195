package cec.jiutian.bc.report8D.domain.report8D.proxy;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;

@Component
public class Report8DProxy implements DataProxy<Report8D> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {

        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(d -> {
                Report8D d8 = fabosJsonDao.getById(Report8D.class, d.get("id").toString());
                if(d8.getStatus().endsWith("_AUDIT")) {
                    d.put("rowOperateFlag", "1");
                }
            });
        }
    }
}
