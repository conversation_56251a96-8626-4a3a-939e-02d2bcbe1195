package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler;

import cec.jiutian.bc.deliveryInspection.enumeration.InspectionResultEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.model.PPKInspectionTaskDetail;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.model.PPKSampleTask;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto.PPKSampleTaskReceiveMTO;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PPKReceiveSampleOperationHandler implements OperationHandler<PPKSampleTask, PPKSampleTaskReceiveMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<PPKSampleTask> data, PPKSampleTaskReceiveMTO modelObject, String[] param) {
        if (modelObject != null) {
            PPKSampleTask condition = new PPKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            PPKSampleTask sampleTask = fabosJsonDao.selectOne(condition);

            if (!TaskBusinessStateEnum.Enum.SEND_SAMPLE.name().equals(sampleTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            if (InspectionResultEnum.Enum.QUALIFIED.name().equals(modelObject.getAppearanceInspect())) {
                sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.RECEIVED_SAMPLE.name());
            } else {
                if (UnqualifiedHandleWayEnum.Enum.rePackage.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
                } else if (UnqualifiedHandleWayEnum.Enum.reSampling.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.EXCEPTION_STOP.name());
                    PPKSampleTask newSamplingTask = new PPKSampleTask();
                    BeanUtil.copyProperties(sampleTask, newSamplingTask);
                    newSamplingTask.setBusinessState(SampleStateEnum.Enum.BE_SAMPLING.name());
                    newSamplingTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.PPKSampleTask.name(), 1, null).get(0));
                    newSamplingTask.setId(null);
                    fabosJsonDao.mergeAndFlush(newSamplingTask);
                } else if (UnqualifiedHandleWayEnum.Enum.exception.name().equals(modelObject.getUnqualifiedHandle())) {
                    sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.BE_SAMPLING.name());
//                    if (StringUtils.isNotEmpty(modelObject.getAbnormalDescription())) {
//                        AbnormalFeedbackHandling abnormalFeedbackHandling = new AbnormalFeedbackHandling();
//                        abnormalFeedbackHandling.setAbnormalFeedbackHandlingFormNumber(namingRuleService.getNameCode(NamingRuleCodeEnum.ABNORMAL_FEEDBACK_HANDLING.name(), 1, null).get(0));
//                        abnormalFeedbackHandling.setAnalyzeAssociatedDocumentNumber(modelObject.getGeneralCode());
//                        abnormalFeedbackHandling.setAbnormalDescription(modelObject.getAbnormalDescription());
//                        abnormalFeedbackHandling.setSubmissionTime(modelObject.getSubmissionTime());
//                        abnormalFeedbackHandling.setDiscoveredPersonId(modelObject.getDiscoveredPersonId());
//                        UserForInsTaskMTO userForInsTaskMTO = fabosJsonDao.getById(UserForInsTaskMTO.class, modelObject.getDiscoveredPersonId());
//                        abnormalFeedbackHandling.setDiscoveredPersonName(userForInsTaskMTO == null ? null : userForInsTaskMTO.getName());
//                        abnormalFeedbackHandling.setAbnormalAttachments(modelObject.getAbnormalAttachments());
//                        fabosJsonDao.mergeAndFlush(abnormalFeedbackHandling);
//                    }
                }
            }
            fabosJsonDao.mergeAndFlush(sampleTask);

            PPKInspectionTaskDetail inspectionTaskDetail = fabosJsonDao.findById(PPKInspectionTaskDetail.class, sampleTask.getPpkInspectionTaskDetailId());
            inspectionTaskDetail.setCurrentState(sampleTask.getBusinessState());
            fabosJsonDao.mergeAndFlush(inspectionTaskDetail);
        }
        return "alert(操作成功)";
    }
}
