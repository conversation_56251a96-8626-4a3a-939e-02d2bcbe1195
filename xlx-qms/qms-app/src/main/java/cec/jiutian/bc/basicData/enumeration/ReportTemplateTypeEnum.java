package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum ReportTemplateTypeEnum {

    //IQC/IPQC/COA/OTHER
    IQC("IQC"),
    IPQC("IPQC"),
    COA("COA"),
    OTHER("其他");

    private final String value;

    ReportTemplateTypeEnum(final String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(ReportTemplateTypeEnum.values()).map(templateTypeEnum ->
                    new VLModel(templateTypeEnum.name(), templateTypeEnum.getValue())).collect(Collectors.toList());
        }

    }
}
