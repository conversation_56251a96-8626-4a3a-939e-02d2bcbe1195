package cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlan;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlanEquipmentDetail;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto.CMKInspectionTaskMTO;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto.GenerateTaskMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class CMKPlanGenerateTaskOperationHandler implements OperationHandler<CMKPlan, GenerateTaskMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<CMKPlan> data, GenerateTaskMTO modelObject, String[] param) {
        if (modelObject != null) {
            CMKInspectionTask cmkInspectionTask = new CMKInspectionTask();
            if (CollectionUtils.isNotEmpty(modelObject.getTaskMTOList())) {
                CMKInspectionTaskMTO cmkInspectionTaskMTO = modelObject.getTaskMTOList().get(0);
                switch (cmkInspectionTaskMTO.getToleranceRequire()) {
                    case  "Bilateral":
                        if (judgeNull(cmkInspectionTaskMTO.getSpecificationUpperLimit())
                                || judgeNull(cmkInspectionTaskMTO.getSpecificationLowerLimit())) {
                            throw new FabosJsonApiErrorTip("请填写上下限值");
                        }
                        break;
                    case "MinUnilateral":
                        if (judgeNull(cmkInspectionTaskMTO.getSpecificationLowerLimit())) {
                            throw new FabosJsonApiErrorTip("请填写下限值");
                        }
                        break;
                    case "MaxUnilateral":
                        if (judgeNull(cmkInspectionTaskMTO.getSpecificationUpperLimit())) {
                            throw new FabosJsonApiErrorTip("请填写上限值");
                        }
                        break;
                }

                BeanUtils.copyProperties(cmkInspectionTaskMTO,cmkInspectionTask);
                fabosJsonDao.mergeAndFlush(cmkInspectionTask);
                CMKPlanEquipmentDetail cmkPlanEquipmentDetail = fabosJsonDao.findById(CMKPlanEquipmentDetail.class,cmkInspectionTaskMTO.getPlanDetailId());
                cmkPlanEquipmentDetail.setIsGenerateTask(true);
                fabosJsonDao.mergeAndFlush(cmkPlanEquipmentDetail);
            }else {
                throw new FabosJsonApiErrorTip("请先选择计划详情");
            }
        }
        return "alert('操作成功')";
    }

    private boolean judgeNull(String str){
        if (StringUtils.isBlank(str)) {
            return true;
        }
        return str.equals("/") || str.equalsIgnoreCase("null");
    }

    @Override
    public GenerateTaskMTO fabosJsonFormValue(List<CMKPlan> data, GenerateTaskMTO fabosJsonForm, String[] param) {
        CMKPlan cmkPlan = data.get(0);
        List<CMKPlanEquipmentDetail> detailList = cmkPlan.getDetailList().stream().filter(detail -> !detail.getIsGenerateTask()).toList();
        if (CollectionUtils.isEmpty(detailList)) {
            throw new FabosJsonApiErrorTip("当前计划所有设备都已创建检验任务");
        } else {
            fabosJsonForm.setGeneralCode(cmkPlan.getGeneralCode());
        }
        return fabosJsonForm;
    }
}
