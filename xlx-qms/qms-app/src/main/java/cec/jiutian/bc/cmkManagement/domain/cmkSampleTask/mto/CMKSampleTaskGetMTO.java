package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.NumberType;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@FabosJson(
        name = "取样任务自定义按钮模型"
)
@Table(name = "qms_cmk_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class CMKSampleTaskGetMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号",readonly = @Readonly)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "cmk检验任务单号"),
            edit = @Edit(title = "cmk检验任务单号",readonly = @Readonly)
    )
    private String cmkInspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序",readonly = @Readonly)
    )
    private String processName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时间", type = ViewType.DATE),
            edit = @Edit(title = "取样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date getSampleDate;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量",readonly = @Readonly,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "实际取样流水号"),
            edit = @Edit(title = "实际取样流水号")
    )
    private String actualSerialNumber;
}
