package cec.jiutian.bc.report8D.domain.report8D.dto;

import cec.jiutian.bc.report8D.domain.report8D.model.LongMeasure;
import cec.jiutian.bc.report8D.domain.report8D.model.TempMeasure;
import cec.jiutian.bc.report8D.enums.VerifyProgressEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

@Data
@AllArgsConstructor
public class VerifyResultDto {
    private String verifyResult;
    private String verifyPersonName;
    private Date verifyDate;

    public static VerifyResultDto buildInstance(TempMeasure d) {
        VerifyResultDto resultDto = new VerifyResultDto(
                d.getTempMeasure() + "：" + VerifyProgressEnum.getValueByKey(d.getVerifyResult()),
                d.getVerifyPersonName(),
                d.getVerifyDate());
        return resultDto;
    }

    public static VerifyResultDto buildInstance(LongMeasure d) {
        VerifyResultDto resultDto = new VerifyResultDto(
                d.getLongMeasure() + "：" + VerifyProgressEnum.getValueByKey(d.getVerifyResult()),
                d.getVerifyPersonName(),
                d.getVerifyDate());
        return resultDto;
    }
}
