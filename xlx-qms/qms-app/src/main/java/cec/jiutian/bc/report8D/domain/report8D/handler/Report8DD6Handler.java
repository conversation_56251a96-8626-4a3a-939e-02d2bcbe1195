package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.*;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.service.spi.ServiceException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class Report8DD6Handler implements OperationHandler<Report8D, Report8DD6> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD6 modelObject, String[] param) {
        //保存验证人信息
        if (CollectionUtils.isNotEmpty(data)) {
            modelObject.getVerifyTempMeasureList().forEach(d->{
                TempMeasure tempMeasure = fabosJsonDao.findById(TempMeasure.class, d.getId());
                tempMeasure.setVerifyResult(d.getVerifyResult());
                tempMeasure.setVerifyDate(d.getVerifyDate());
                tempMeasure.setVerifyEvidence(d.getVerifyEvidence());

                tempMeasure.setVerifyPersonId(UserContext.getUserId());
                tempMeasure.setVerifyPersonName(UserContext.getUserName());
                fabosJsonDao.mergeAndFlush(tempMeasure);
            });

            modelObject.getVerifyLongMeasureList().forEach(d->{
                LongMeasure longMeasure = fabosJsonDao.findById(LongMeasure.class, d.getId());
                longMeasure.setVerifyResult(d.getVerifyResult());
                longMeasure.setVerifyDate(d.getVerifyDate());
                longMeasure.setVerifyEvidence(d.getVerifyEvidence());

                longMeasure.setVerifyPersonId(UserContext.getUserId());
                longMeasure.setVerifyPersonName(UserContext.getUserName());
                fabosJsonDao.mergeAndFlush(longMeasure);
            });

            // 修改主表状态
            Report8D report8D = data.get(0);
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D6_AUDIT.name() : report8D.getStatus());
            fabosJsonDao.mergeAndFlush(report8D);
            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    @Override
    public Report8DD6 fabosJsonFormValue(List<Report8D> data, Report8DD6 fabosJsonForm, String[] param) {
        // data为空 直接返回
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }
        //验证临时措施 长期措施 是否都完成
        Report8D report8D = data.get(0);
        if (Objects.equals(report8D.getTempStatus(), MeasureCompleteStatusEnum.Enum.NOT_FINISH.name())) {
            throw new ServiceException("请先完成临时措施任务后，再验证！");
        }
        if (Objects.equals(report8D.getLongStatus(), MeasureCompleteStatusEnum.Enum.NOT_FINISH.name())) {
            throw new ServiceException("请先完成长期措施任务后，再验证！");
        }
        //BeanUtil.copyProperties(report8D, fabosJsonForm);

        if (CollectionUtils.isNotEmpty(report8D.getTempMeasureList())) {
            List<VerifyTempMeasure> verifyTempList = new ArrayList<>();
            report8D.getTempMeasureList().forEach(d->{
                VerifyTempMeasure verifyTemp = new VerifyTempMeasure();
                BeanUtil.copyProperties(d, verifyTemp);
                verifyTempList.add(verifyTemp);
            });
            fabosJsonForm.setVerifyTempMeasureList(verifyTempList);
        }

        if (CollectionUtils.isNotEmpty(report8D.getLongMeasureList())) {
            List<VerifyLongMeasure> verifyLongList = new ArrayList<>();
            report8D.getLongMeasureList().forEach(d->{
                VerifyLongMeasure verifyLong = new VerifyLongMeasure();
                BeanUtil.copyProperties(d, verifyLong);
                verifyLongList.add(verifyLong);
            });
            fabosJsonForm.setVerifyLongMeasureList(verifyLongList);
        }

        return fabosJsonForm;
    }
}
