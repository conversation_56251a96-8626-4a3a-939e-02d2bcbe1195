package cec.jiutian.bc.report8D.remote.controller;

import cec.jiutian.bc.report8D.service.D8ReprotService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/8d/report")
public class D8ReportController {

    @Resource
    private D8ReprotService d8ReprotService;
    @PostMapping("/generate8DReport")
    public void generateCMKReport(@RequestParam String d8ReportId, HttpServletResponse response) {
        try {
            Workbook workbook = d8ReprotService.generateReport(d8ReportId);
            setResponseHeaders(response, workbook, "8D报告_"+ LocalDateTime.now());
            workbook.write(response.getOutputStream());
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void setResponseHeaders(HttpServletResponse response, Workbook workbook,String fileName) {
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        String contentType = workbook instanceof HSSFWorkbook ?
                "application/vnd.ms-excel" :
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String fileExtension = workbook instanceof HSSFWorkbook ? "xls" : "xlsx";
        response.setContentType(contentType);
        response.setHeader("Content-Disposition",
                "attachment; filename="+encodedFileName+"." + fileExtension);
        response.setCharacterEncoding("UTF-8");
    }
}
