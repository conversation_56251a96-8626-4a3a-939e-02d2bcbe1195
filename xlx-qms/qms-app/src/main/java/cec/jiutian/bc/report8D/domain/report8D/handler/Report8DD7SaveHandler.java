package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD7;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD7SaveHandler implements OperationHandler<Report8D, Report8DD7> {

    @Resource
    private Report8DD7Handler report8DD7Handler;

    @Override
    public Report8DD7 fabosJsonFormValue(List<Report8D> data, Report8DD7 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD7Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
