package cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.mto;

import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.handler.PPKInspectionTaskResultReferenceAddHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import lombok.Data;

import java.util.List;

@Entity
@Data
@FabosJson(
        name = "PPK检验任务录入结果MTO"
)
@TemplateType(type = "multiTable")
public class PPKEnterResultOprMTO extends BaseModel {

    @FabosJsonField(
            edit = @Edit(title = "明细", type = EditType.TAB_REFER_ADD, readonly = @Readonly(edit = false)),
            referenceAddType = @ReferenceAddType(referenceClass = "PPKInspectionTaskDetail",
                    filter = "inspectValue is null  and ppkInspectionTask.id = '${id}' and PPKInspectionTaskDetail.currentState = 'RECEIVED_SAMPLE'",
                    editable = {"inspectValue"},
                    referenceAddHandler = PPKInspectionTaskResultReferenceAddHandler.class
            ),
            views = @View(title = "明细", type = ViewType.TABLE_VIEW, extraPK = "id", column = "sampleTaskCode")
    )
    @JoinColumn(name = "lab_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<PPKEnterResultOprDetailMTO> detailMTOList;
}
