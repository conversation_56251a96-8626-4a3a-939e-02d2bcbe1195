package cec.jiutian.bc.mto;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.modeler.enumration.ReportResponseCodeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/7
 * @description
 */
@Setter
@Getter
@Component
@NoArgsConstructor
public class ReportResponseResult<T> implements Serializable {

    /**
     * 响应状态码，0：调用成功,500：调用失败
     */
    private String status;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    public static <T> ReportResponseResult<T> success(T data) {
        ReportResponseResult<T> result = new ReportResponseResult<>();
        result.setStatus(ReportResponseCodeEnum.SUCCESS.getCode());
        result.setMsg(ReportResponseCodeEnum.SUCCESS.getDescription());
        result.setData(data);
        return result;
    }

    public static <T> ReportResponseResult<T> error(String code, String message) {
        ReportResponseResult<T> result = new ReportResponseResult<>();
        result.setStatus(code);
        result.setStatus(message);
        return result;
    }
}
