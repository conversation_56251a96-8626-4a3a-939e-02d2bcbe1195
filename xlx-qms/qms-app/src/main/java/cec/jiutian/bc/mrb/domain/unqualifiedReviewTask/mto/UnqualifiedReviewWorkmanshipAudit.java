package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto;

import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Getter
@Setter
@Entity
@Table(name = "qms_mrb_unqualified_review_task")
@FabosJson(
        name = "审核",
        orderBy = "UnqualifiedReviewWorkmanshipAudit.createTime desc",
        power = @Power(add = false,delete = false,edit = false)
)
public class UnqualifiedReviewWorkmanshipAudit extends MetaModel {
    //临时措施
    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedReviewTaskCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    search = @Search(vague = true),
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;
}
