package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTaskDetail;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto.EnterResultOprDetailMTO;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto.EnterResultOprMTO;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Component
public class EnterCMKInspectResultOperationHandler implements OperationHandler<CMKInspectionTask,EnterResultOprMTO> {

    @Resource
    private NamingRuleService namingRuleService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKInspectionTask> data, EnterResultOprMTO modelObject, String[] param) {
        CMKInspectionTask cmkInspectionTask = data.get(0);
        modelObject.getDetailMTOList().forEach(detail -> {
            Optional<CMKInspectionTaskDetail> optional = cmkInspectionTask.getDetailList().stream().filter(inspectionDetail -> inspectionDetail.getSampleTaskCode().equals(detail.getSampleTaskCode())).findFirst();
            if (optional.isPresent()) {
                CMKInspectionTaskDetail cmkInspectionTaskDetail = optional.get();
                cmkInspectionTaskDetail.setInspectValue(detail.getInspectValue());
            }
        });
        fabosJsonDao.mergeAndFlush(cmkInspectionTask);
        return "alert('操作成功')";
    }

    @Override
    public EnterResultOprMTO fabosJsonFormValue(List<CMKInspectionTask> data, EnterResultOprMTO fabosJsonForm, String[] param) {
        CMKInspectionTask cmkInspectionTask = data.get(0);
        if (CollectionUtils.isNotEmpty(cmkInspectionTask.getDetailList())) {
            List<EnterResultOprDetailMTO> detailMTOList = new ArrayList<>();
            cmkInspectionTask.getDetailList().forEach(detail -> {
                EnterResultOprDetailMTO enterResultOprDetailMTO = new EnterResultOprDetailMTO();
                enterResultOprDetailMTO.setSampleTaskCode(detail.getSampleTaskCode());
                enterResultOprDetailMTO.setInspectValue(detail.getInspectValue());
                detailMTOList.add(enterResultOprDetailMTO);
            });
            fabosJsonForm.setDetailMTOList(detailMTOList);
        }else {
            throw new FabosJsonApiErrorTip("暂未创建取样任务");
        }
        return fabosJsonForm;
    }
}
