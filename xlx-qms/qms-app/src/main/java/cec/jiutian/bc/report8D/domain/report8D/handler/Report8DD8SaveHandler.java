package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD8;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD8SaveHandler implements OperationHandler<Report8D, Report8DD8> {

    @Resource
    private Report8DD8Handler report8DD8Handler;

    @Override
    public Report8DD8 fabosJsonFormValue(List<Report8D> data, Report8DD8 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD8Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
