package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.bc.report8D.enums.VerifyProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
@Getter
@Setter
@Entity
@Table(name = "qms_8d_report_long_measure")
@FabosJson(
        name = "长期措施",
        orderBy = "LongMeasure.createTime desc"
)
public class LongMeasure extends MetaModel {
    // 长期措施
    @FabosJsonField(
            views = @View(title = "长期措施"),
            edit = @Edit(
                    title = "长期措施",
                    inputType = @InputType(length = 50),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(length = 50)
    private String longMeasure;

    // 责任人
    @Transient
    @FabosJsonField(
            views = @View(title = "责任人", column = "name", show = false),
            edit = @Edit(
                    title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO responsibleUserMTO;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(
                    title = "责任人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleUserMTO", beFilledBy = "name"))
    )
    @Column(length = 20)
    private String responsiblePersonName;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "责任人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleUserMTO", beFilledBy = "id"))
    )
    @Column(length = 50)
    private String responsiblePersonId;

    // 纳期
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "纳期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "due_time", nullable = false)
    private Date dueTime;

    //改善进度
    @FabosJsonField(
            views = @View(title = "改善进度"),
            edit = @Edit(title = "改善进度",
                    inputType = @InputType(length = 20),
                    defaultVal = "WAIT_EXECUTE",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MeasureProgressEnum.class))
    )
    @Column(length = 20)
    private String progress;
    //完成日期
    @FabosJsonField(
            views = @View(title = "完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "完成日期",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date completionDate;
    //完成佐证材料
    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String completionEvidence;
    //验证人
    @Transient
    @FabosJsonField(
            views = @View(title = "验证人", column = "name", show = false),
            edit = @Edit(
                    title = "验证人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO verifyUserMTO;

    @FabosJsonField(
            views = @View(title = "验证人"),
            edit = @Edit(
                    title = "验证人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifyUserMTO", beFilledBy = "name"))
    )
    @Column(length = 20)
    private String verifyPersonName;

    @FabosJsonField(
            views = @View(title = "验证人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "验证人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "verifyUserMTO", beFilledBy = "id"))
    )
    @Column(length = 50)
    private String verifyPersonId;
    //验证结果
    @FabosJsonField(
            views = @View(title = "验证结果"),
            edit = @Edit(title = "验证结果",
                    inputType = @InputType(length = 20),
                    defaultVal = "FAILED",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = VerifyProgressEnum.class))
    )
    @Column(length = 20)
    private String verifyResult;

    //验证时间
    @FabosJsonField(
            views = @View(title = "验证时间", type = ViewType.DATE_TIME, show = false),
            edit = @Edit(
                    title = "验证时间",
                    show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date verifyDate;

    //验证佐证材料
    @FabosJsonField(
            views = @View(title = "验证佐证材料"),
            edit = @Edit(title = "验证佐证材料",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String verifyEvidence;
}
