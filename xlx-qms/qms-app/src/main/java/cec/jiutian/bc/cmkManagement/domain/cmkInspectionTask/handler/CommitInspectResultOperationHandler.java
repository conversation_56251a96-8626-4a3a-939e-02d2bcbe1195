package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTaskDetail;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @description TODO
 */
@Component
public class CommitInspectResultOperationHandler implements OperationHandler<CMKInspectionTask, CMKInspectionTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKInspectionTask> data, CMKInspectionTask modelObject, String[] param) {
        CMKInspectionTask cmkInspectionTask = data.get(0);
        if (CollectionUtils.isNotEmpty(cmkInspectionTask.getDetailList())) {
            if (cmkInspectionTask.getSampleCount() == cmkInspectionTask.getDetailList().size()) {
                List<CMKInspectionTaskDetail> taskDetailList = cmkInspectionTask.getDetailList().stream().filter(detail -> StringUtils.isEmpty(detail.getInspectValue())).toList();
                if (CollectionUtils.isEmpty(taskDetailList)) {
                    cmkInspectionTask.setBusinessState(OrderCurrentStateEnum.Enum.COMPLETE.name());
                    fabosJsonDao.mergeAndFlush(cmkInspectionTask);
                }else {
                    throw new FabosJsonApiErrorTip("取样任务实测值未填写完全，不可提交");
                }
            }else {
                throw new FabosJsonApiErrorTip("取样任务未全部创建，不可提交");
            }
        }else {
            throw new FabosJsonApiErrorTip("请先发起取样");
        }

        return "alert('操作成功')";
    }
}
