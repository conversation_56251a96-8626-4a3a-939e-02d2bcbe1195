package cec.jiutian.bc.basicData.domain.reportTemplate.proxy;

import cec.jiutian.bc.basicData.domain.reportTemplate.model.ReportTemplate;
import cec.jiutian.bc.basicData.enumeration.ReportTemplateStatus;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


@Component
public class ReportTemplateDataProxy implements DataProxy<ReportTemplate> {

    @Override
    public void beforeAdd(ReportTemplate reportTemplate) {
        String userName = UserContext.getUserName();
        if (StringUtils.isBlank(userName)) {
            throw new ServiceException("用户未登录");
        }
        reportTemplate.setCreatedBy(userName);
        reportTemplate.setStatus(ReportTemplateStatus.CREATED.name());
    }

    @Override
    public void beforeUpdate(ReportTemplate reportTemplate) {
        String userName = UserContext.getUserName();
        if (StringUtils.isBlank(userName)) {
            throw new ServiceException("用户未登录");
        }
        reportTemplate.setStatus(ReportTemplateStatus.EDIT.name());
        reportTemplate.setUpdateBy(userName);
    }
}
