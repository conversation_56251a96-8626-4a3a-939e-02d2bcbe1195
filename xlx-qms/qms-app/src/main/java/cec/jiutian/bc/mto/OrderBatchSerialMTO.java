package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description TODO
 */
@Entity
@Table(name = "batch_serial")
@Getter
@FabosJson(
        name = "工单批次信息",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class OrderBatchSerialMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "工单号"),
            edit = @Edit(title = "工单号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String orderNumber;

    @FabosJsonField(
            views = @View(title = "工序流水号/批次号"),
            edit = @Edit(title = "工序流水号/批次号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String serialNumber;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    private String materialName;

//    @FabosJsonField(
//            views = @View(title = "规格"),
//            edit = @Edit(title = "规格",
//                    notNull = true
//            )
//    )
//    private String materialDetail;

    @FabosJsonField(
            views = @View(title = "是否检验合格"),
            edit = @Edit(title = "是否检验合格",
                    notNull = true
            )
    )
    private String qualifiedFlag;


    @FabosJsonField(
            views = @View(title = "产出时间"),
            edit = @Edit(title = "产出时间"
            )
    )
    private Date outputTime;

    @FabosJsonField(
            views = @View(title = "产出数量"),
            edit = @Edit(title = "产出数量"
            )
    )
    private Double outputQuantity;

    @Column(name = "create_ts")
    private Date createTime;
}
