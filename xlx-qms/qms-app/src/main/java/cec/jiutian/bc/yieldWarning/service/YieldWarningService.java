package cec.jiutian.bc.yieldWarning.service;

import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.materialInspect.domain.inspectionTask.model.IncomingInspectionTask;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.urm.domain.role.service.RoleService;
import cec.jiutian.bc.yieldWarning.domain.materialWarningRule.model.MaterialWarningRule;
import cec.jiutian.bc.yieldWarning.enums.MaterialWarningStatisticalScopeEnum;
import cec.jiutian.bc.yieldWarning.enums.MaterialWarningStatisticalTypeEnum;
import cec.jiutian.common.util.BigDecimalUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class YieldWarningService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EcsMessageProvider ecsMessageProvider;

    @Resource
    private RoleService roleService;

    /**
     * 每周一早七点 根据预警规则判断 上周供应商来料的合格率，并发出预警
     */
    public void materialYieldWarning() {
        // 获取来料预警规则
        MaterialWarningRule condition = new MaterialWarningRule();
        condition.setStatus(StatusEnum.Enum.Effective.name());

        condition.setStatisticalScope(MaterialWarningStatisticalScopeEnum.Enum.All.name());
        List<MaterialWarningRule> allScopeRules = fabosJsonDao.select(condition);

        condition.setStatisticalScope(MaterialWarningStatisticalScopeEnum.Enum.Single.name());
        List<MaterialWarningRule> singleScopeRules = fabosJsonDao.select(condition);

        // 获取上周来料检验任务，计算合格率a
        LocalDateTime startTime = LocalDateTime.now().minusWeeks(1);
        List<IncomingInspectionTask> inspectionTaskList = fabosJsonDao.queryEntityList(IncomingInspectionTask.class,
                "where currentState = 'COMPLETE' and createTime >= :startTime and createTime <= :endTime",
                new HashMap<>(1) {{
                    this.put("startTime", startTime);
                    this.put("endTime", LocalDateTime.now());
                }});

        if (CollectionUtils.isNotEmpty(inspectionTaskList)) {
            List<String> roleIds = new ArrayList<>();
            String msg = "上周来料合格率预警：";

            if (CollectionUtils.isNotEmpty(allScopeRules)) {
                long qualified = inspectionTaskList.stream()
                        .filter(x -> x.getInspectionResult() != null && x.getInspectionResult().equals(InspectionResultEnum.Enum.QUALIFIED.name()))
                        .count();
                Double rate = BigDecimalUtils.div((double) qualified * 100, (double) inspectionTaskList.size(), 2);

                for (MaterialWarningRule rule : allScopeRules) {
                    MaterialWarningStatisticalTypeEnum.Enum type = MaterialWarningStatisticalTypeEnum.Enum.valueOf(rule.getStatisticalType());
                    switch (type) {
                        case Range:
                            if (rate < rule.getUpperLimit() && rate > rule.getLowerLimit()) {
                                roleIds.add(rule.getRoleId());
                            }
                            break;
                        case LowerLimit:
                            if (rate > rule.getLowerLimit()) {
                                roleIds.add(rule.getRoleId());
                            }
                            break;
                        case UpperLimit:
                            if (rate < rule.getUpperLimit()) {
                                roleIds.add(rule.getRoleId());
                            }
                            break;
                    }
                }

                msg = msg + "【所有供应商来料合格率" + rate + "】";
            }

            if (CollectionUtils.isNotEmpty(singleScopeRules)) {
                List<Double> rateList = new ArrayList<>();
                Map<String, List<IncomingInspectionTask>> map = inspectionTaskList.stream().filter(x -> x.getSupplierName() != null)
                        .collect(Collectors.groupingBy(IncomingInspectionTask::getSupplierName));
                map.forEach((k, v) -> {
                    long qualified = v.stream()
                            .filter(x -> x.getInspectionResult() != null && x.getInspectionResult().equals(InspectionResultEnum.Enum.QUALIFIED.name()))
                            .count();
                    Double rate = BigDecimalUtils.div((double) qualified * 100, (double) v.size(), 2);
                    rateList.add(rate);
                });

                for (MaterialWarningRule rule : allScopeRules) {
                    MaterialWarningStatisticalTypeEnum.Enum type = MaterialWarningStatisticalTypeEnum.Enum.valueOf(rule.getStatisticalType());
                    switch (type) {
                        case Range:
                            rateList.stream().filter(x -> x < rule.getUpperLimit() && x > rule.getLowerLimit())
                                    .findFirst().ifPresent(p -> roleIds.add(rule.getRoleId()));
                            break;
                        case LowerLimit:
                            rateList.stream().filter(x -> x > rule.getLowerLimit())
                                    .findFirst().ifPresent(p -> roleIds.add(rule.getRoleId()));
                            break;
                        case UpperLimit:
                            rateList.stream().filter(x -> x < rule.getUpperLimit())
                                    .findFirst().ifPresent(p -> roleIds.add(rule.getRoleId()));
                            break;
                    }
                }

                msg = msg + "【单个供应商来料合格率" + rateList + "】";
            }

            // 发出预警到指定角色/人员
            sendWarningMsg(roleIds, msg);
        }

    }

    private void sendWarningMsg(List<String> roleIds, String msg) {
        // 构造SendMsgToPersonDTO，通过传入的角色List,调用roleService.getUserPhonesByRoles后，设置发送对象，通过sendPersonMessage发送
        SendMsgToPersonDTO sendMsgToPersonDTO = new SendMsgToPersonDTO();
        sendMsgToPersonDTO.setTitle("来料预警");
        sendMsgToPersonDTO.setContent(msg);
        sendMsgToPersonDTO.setSendBy("定时任务");
        sendMsgToPersonDTO.setWay(MessageWayEnum.App);
        sendMsgToPersonDTO.setReceivers(roleService.getUserPhonesByRoles(roleIds));
        try {
            ecsMessageProvider.sendPersonMessage(sendMsgToPersonDTO);
        } catch (Exception e) {
            log.error("发送消息失败：{}", e.getMessage());
        }
    }


}
