package cec.jiutian.bc.cmkManagement.remote.controller;

import cec.jiutian.bc.cmkManagement.service.CMKService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description TODO
 */
@RestController
@RequestMapping("/cmk/report")
public class CMKReportController {

    @Resource
    private CMKService cmkService;

    @PostMapping("/generateCMKReport")
    public void generateCMKReport(@RequestParam String cmkTaskId, HttpServletResponse response) {
        try {
            Workbook workbook = cmkService.generateReport(cmkTaskId);
            setResponseHeaders(response, workbook, "CMK报告_"+ LocalDateTime.now());
            workbook.write(response.getOutputStream());
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void setResponseHeaders(HttpServletResponse response, Workbook workbook,String fileName) {
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        String contentType = workbook instanceof HSSFWorkbook ?
                "application/vnd.ms-excel" :
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String fileExtension = workbook instanceof HSSFWorkbook ? "xls" : "xlsx";
        response.setContentType(contentType);
        response.setHeader("Content-Disposition",
                "attachment; filename="+encodedFileName+"." + fileExtension);
        response.setCharacterEncoding("UTF-8");
    }
}
