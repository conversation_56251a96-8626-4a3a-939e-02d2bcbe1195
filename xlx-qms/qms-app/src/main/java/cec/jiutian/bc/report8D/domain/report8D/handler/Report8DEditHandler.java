package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DAdd;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DEditHandler implements OperationHandler<Report8D, Report8DAdd> {

    @Override
    public Report8DAdd fabosJsonFormValue(List<Report8D> data, Report8DAdd fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            BeanUtil.copyProperties(report8D, fabosJsonForm);
        }
        return fabosJsonForm;
    }
}
