package cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class InsGroupDynamicHandler implements DependFiled.DynamicHandler<InspectionItemGroup> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public Map<String, Object> handle(InspectionItemGroup inspectionItemGroup) {
        Map<String, Object> map = new HashMap<>();
        map.put("groupCode", String.valueOf(namingRuleService.getNameCode(
                        NamingRuleCodeEnum.InspectionItemGroup.name(), 1, null).get(0)));
        return map;
    }

}
