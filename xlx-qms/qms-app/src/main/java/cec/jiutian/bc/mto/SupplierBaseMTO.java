package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description TODO
 */
@FabosJson(
        name = "供应商MTO",
        orderBy = "createTime desc"
)
@Entity
@Data
@Table(name = "mo_splr")
public class SupplierBaseMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "供应商编码"),
            edit = @Edit(title = "供应商编码")
    )
    @Column(name = "splr_cd", length = 8)
    private String supplierCode;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称")
    )
    @Column(name = "splr_nm", length = 8)
    private String supplierName;

    @FabosJsonField(
            views = @View(title = "生效标记"),
            edit = @Edit(title = "生效标记",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "vld_flg", length = 8)
    private String validateFlag;

    @Column(name = "crte_tm")
    private Date createTime;
}
