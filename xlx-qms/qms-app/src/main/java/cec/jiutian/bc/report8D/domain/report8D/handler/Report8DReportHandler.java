package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.service.D8ReprotService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.OperationHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Component
public class Report8DReportHandler implements OperationHandler<Report8D, Report8D> {

    @Resource
    private D8ReprotService d8ReprotService;

    @Override
    public DownloadableFile fileOperator(List<Report8D> selectedData, String[] param) {
        Workbook workbook = d8ReprotService.D8Report(selectedData.get(0));

        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            workbook.close();

            String fileExtension = workbook instanceof HSSFWorkbook ? ".xls" : ".xlsx";
            Path outFilePath = Paths.get("/file", String.valueOf(new StringBuffer("8D报告_")
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
                    .append(fileExtension)));

            return new DownloadableFile(outputStream, outFilePath.toString(), workbook instanceof HSSFWorkbook ?
                    "application/vnd.ms-excel" :
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        } catch (IOException e) {
            throw new FabosJsonApiErrorTip("下载8D报告出错");
        }
    }
}
