package cec.jiutian.bc.basicData.port;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
public class BatchRange {
    private int minBatch;
    private int maxBatch;
    private String sampleLetter;

    public BatchRange(int min, int max,String letter) {
        this.minBatch = min;
        this.maxBatch = max;
        this.sampleLetter = letter;
    }

    public int getMinBatch() { return minBatch; }
    public int getMaxBatch() { return maxBatch; }
    public String getSampleLetter() { return sampleLetter; }
}
