package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewCreateMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description
 */
@Component
public class MRBUnqualifiedReviewModifyHandler implements OperationHandler<MRBUnqualifiedReview, MRBUnqualifiedReviewCreateMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewCreateMTO modelObject, String[] param) {
        if (modelObject != null) {
            MRBUnqualifiedReview model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public MRBUnqualifiedReviewCreateMTO fabosJsonFormValue(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewCreateMTO fabosJsonForm, String[] param) {
        MRBUnqualifiedReview model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
