package cec.jiutian.bc.report8D.domain.myPreventMeasureTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "我的预防措施",
        orderBy = "MyPreventMeasureTaskExec.createTime desc",
        power = @Power(add = false, edit = false)
)
public class MyPreventMeasureTaskExec extends MetaModel {
    //预防措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "预防措施", column = "preventMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "预防措施",
                    type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"progress", "completionDate", "completionEvidence"})
    )
    private List<MyPreventMeasures> myPreventMeasuresList;
}
