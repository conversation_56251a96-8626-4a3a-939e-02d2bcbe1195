package cec.jiutian.bc.mrb.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
public class UnqualifiedReviewTaskPresentEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        FINISH("完成"),
        NOT_FINISH("未完成");

        private final String value;

    }
}

