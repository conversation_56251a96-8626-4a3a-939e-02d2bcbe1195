package cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.model.CMKPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class CMKPlanPublishOperationHandler implements OperationHandler<CMKPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKPlan> data, Void modelObject, String[] param) {
        CMKPlan cmkPlan = data.get(0);
        if (CollectionUtils.isEmpty(cmkPlan.getDetailList())) {
            throw new FabosJsonApiErrorTip("计划设备信息不能为空");
        }
        cmkPlan.setBusinessState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        fabosJsonDao.mergeAndFlush(cmkPlan);

        return "alert('操作成功')";
    }
}
