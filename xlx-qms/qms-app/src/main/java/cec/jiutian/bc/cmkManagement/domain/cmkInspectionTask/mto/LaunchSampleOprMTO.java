package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "CMK检验任务发起取样MTO"
)
public class LaunchSampleOprMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "取样次数"),
            edit = @Edit(title = "取样次数",inputGroup = @InputGroup(prefix = "点击确认即创建第", postfix = "次取样任务"))
    )
    private String count;
}
