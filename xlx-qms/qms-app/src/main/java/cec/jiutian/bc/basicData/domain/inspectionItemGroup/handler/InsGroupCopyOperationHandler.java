package cec.jiutian.bc.basicData.domain.inspectionItemGroup.handler;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.enumeration.InsGroupStatusEnum;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class InsGroupCopyOperationHandler implements OperationHandler<InspectionItemGroup, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    public String exec(List<InspectionItemGroup> data, Void modelObject, String[] param) {
        String userName = UserContext.getUserName();
        if (StringUtils.isBlank(userName)) {
            throw new ServiceException("用户未登录");
        }
        String code = namingRuleService.getNameCode(NamingRuleCodeEnum.InspectionItemGroup.name(), 1, null).get(0);
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("生成组取编号失败");
        }
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionItemGroup inspectionItem = data.get(0);
            InspectionItemGroup copyGroup = new InspectionItemGroup();
            List<InspectionItem> items = inspectionItem.getInsItems();
            ArrayList<InspectionItem> newItems = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(items)) {
                for (InspectionItem item : items) {
                    InspectionItem insItem = fabosJsonDao.findById(InspectionItem.class, item.getId());
                    newItems.add(insItem);
                }
            }
            BeanUtils.copyProperties(inspectionItem, copyGroup, "id");
            copyGroup.setInsItems(newItems);
            copyGroup.setStatus(InsGroupStatusEnum.CREATED.name());
            copyGroup.setUpdatedAt(null);
            copyGroup.setCreatedAt(null);
            copyGroup.setCreatedBy(userName);
            copyGroup.setGroupCode(code);
            fabosJsonDao.persistAndFlush(copyGroup);
        }
        return "msg.success('操作成功')";
    }
}
