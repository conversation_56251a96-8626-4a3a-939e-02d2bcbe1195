package cec.jiutian.bc.cmkManagement.service;

import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTask;
import cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model.CMKInspectionTaskDetail;
import cec.jiutian.bc.cmkManagement.enumeration.SpecificationCustomerRequireEnum;
import cec.jiutian.bc.cmkManagement.util.ExcelUtil;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import org.apache.poi.ss.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description TODO
 */
@Service
public class CMKService {

    @jakarta.annotation.Resource
    private FabosJsonDao fabosJsonDao;

    private static final List<String> columnList = Arrays.asList("1","3","5","7","9");

    public Workbook generateReport(String cmkTaskId) {
        if (StringUtils.isNotEmpty(cmkTaskId)) {
            CMKInspectionTask cmkInspectionTask = fabosJsonDao.findById(CMKInspectionTask.class, cmkTaskId);
            if (cmkInspectionTask != null) {
                try {
                    Resource resource = new ClassPathResource("file/template/cmk_report_template.xlsx");
                    InputStream inputStream = resource.getInputStream();
                    Workbook workbook = WorkbookFactory.create(inputStream);
                    Sheet sheet = workbook.getSheetAt(1);

                    setTaskInfo(sheet, cmkInspectionTask);
                    setCellValues(sheet, cmkInspectionTask);

                    ExcelUtil.forceFormulaRecalculation(workbook);
                    return workbook;
                }catch (IOException e){
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private void setTaskInfo(Sheet sheet, CMKInspectionTask cmkInspectionTask) {
        Row firstRow = sheet.getRow(3);
        Cell factoryLineCell = firstRow.getCell(2);
        factoryLineCell.setCellValue(cmkInspectionTask.getFactoryLineName());
        Cell deviceCell = firstRow.getCell(6);
        deviceCell.setCellValue(cmkInspectionTask.getEquipmentArchiveCode());

        Row row9 = sheet.getRow(9);
        Cell cell1 = row9.getCell(3);
        cell1.setCellValue(Double.parseDouble(SpecificationCustomerRequireEnum.Enum.valueOf(cmkInspectionTask.getSpecificationCustomerRequire()).getValue()));
    }

    private void setCellValues(Sheet sheet, CMKInspectionTask cmkInspectionTask) {
        int detailIndex = 0;
        List<CMKInspectionTaskDetail> taskDetailList = cmkInspectionTask.getDetailList();
        for (String colNum : columnList) {
            for (int rowNum=15; rowNum<=24; rowNum++) {
                Row row = sheet.getRow(rowNum);
                Cell cell = row.getCell(Integer.parseInt(colNum));
                // 设置单元格值
                if (detailIndex < taskDetailList.size()) {
                    CMKInspectionTaskDetail cmkInspectionTaskDetail = taskDetailList.get(detailIndex);
                    if (cmkInspectionTaskDetail != null) {
                        if (cell == null) {
                            Cell cell1 = row.createCell(Integer.parseInt(colNum));
                            cell1.setCellValue(Double.parseDouble(cmkInspectionTaskDetail.getInspectValue()));
                        }else {
                            cell.setCellValue(Double.parseDouble(cmkInspectionTaskDetail.getInspectValue()));
                        }
                    }
                }
                detailIndex++;
            }
        }
    }
}
