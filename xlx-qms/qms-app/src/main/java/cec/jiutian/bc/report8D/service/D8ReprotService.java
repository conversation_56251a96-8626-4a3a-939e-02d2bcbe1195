package cec.jiutian.bc.report8D.service;

import cec.jiutian.bc.report8D.domain.report8D.dto.VerifyResultDto;
import cec.jiutian.bc.report8D.domain.report8D.model.*;
import cec.jiutian.bc.report8D.enums.PositionTypeEnum;
import cec.jiutian.bc.report8D.enums.VerifyProgressEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class D8ReprotService {

    private static final int INIT_ROW_INDEX = -1;

    @Resource
    private FabosJsonDao fabosJsonDao;

    public Workbook D8Report(Report8D report8D) {
        //return buildWorkbook(report8D, "8d_report_template.xlsx");
        return build8DReportWorkbook(report8D);
    }

    public Workbook generateReport(String d8ReportId) {
        Report8D report8D = fabosJsonDao.findById(Report8D.class, d8ReportId);
        //return buildWorkbook(report8D, "8d_report_template.xlsx");
        return build8DReportWorkbook(report8D);
    }

    public Workbook build8DReportWorkbook(Report8D report8D) {
        Workbook base = buildWorkbook(report8D, "8d_report_template_base.xlsx");
        Workbook d2d3 = buildWorkbook(report8D, "8d_report_template_d2d3.xlsx");
        Workbook d4d5 = buildWorkbook(report8D, "8d_report_template_d4d5.xlsx");
        Workbook d6 = buildWorkbook(report8D, "8d_report_template_d6.xlsx");
        Workbook d7 = buildWorkbook(report8D, "8d_report_template_d7.xlsx");
        Workbook d8 = buildWorkbook(report8D, "8d_report_template_d8.xlsx");
        mergeSheetsWithFormat(base.getSheetAt(0),  d2d3.getSheetAt(0));
        mergeSheetsWithFormat(base.getSheetAt(0),  d4d5.getSheetAt(0));
        mergeSheetsWithFormat(base.getSheetAt(0),  d6.getSheetAt(0));
        mergeSheetsWithFormat(base.getSheetAt(0),  d7.getSheetAt(0));
        mergeSheetsWithFormat(base.getSheetAt(0),  d8.getSheetAt(0));
        return base;
    }

    // 合并工作表并保留所有格式和合并单元格
    private static void mergeSheetsWithFormat(Sheet destSheet, Sheet srcSheet) {
        int startRowNum = destSheet.getLastRowNum() + 1; // 起始行号

        // 1. 复制所有合并区域
        copyMergedRegions(destSheet, srcSheet, startRowNum);

        // 2. 复制所有行（包括空行）
        for (int srcRowNum = 0; srcRowNum <= srcSheet.getLastRowNum(); srcRowNum++) {
            Row srcRow = srcSheet.getRow(srcRowNum);
            Row destRow = destSheet.createRow(startRowNum + srcRowNum);

            if (srcRow != null) {
                // 复制行高
                destRow.setHeight(srcRow.getHeight());

                // 复制所有单元格
                for (int cellNum = 0; cellNum < srcRow.getLastCellNum(); cellNum++) {
                    Cell srcCell = srcRow.getCell(cellNum);
                    if (srcCell != null) {
                        Cell destCell = destRow.createCell(cellNum);
                        copyCellWithFormat(srcCell, destCell);
                    }
                }
            }
        }

        // 3. 复制列宽
        for (int i = 0; i < srcSheet.getRow(0).getLastCellNum(); i++) {
            destSheet.setColumnWidth(i, srcSheet.getColumnWidth(i));
        }
    }

    // 关键修复：复制合并单元格区域
    private static void copyMergedRegions(Sheet destSheet, Sheet srcSheet, int startRow) {
        // 获取源sheet中的所有合并区域
        int numMergedRegions = srcSheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress mergedRegion = srcSheet.getMergedRegion(i);

            // 创建新的合并区域（行号偏移）
            CellRangeAddress newMergedRegion = new CellRangeAddress(
                    mergedRegion.getFirstRow() + startRow,
                    mergedRegion.getLastRow() + startRow,
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            );

            // 添加到目标sheet
            destSheet.addMergedRegion(newMergedRegion);
        }
    }
    // 复制单元格并完全保留格式
    private static void copyCellWithFormat(Cell srcCell, Cell destCell) {
        // 1. 复制单元格样式
        Workbook workbook = destCell.getSheet().getWorkbook();
        CellStyle newStyle = workbook.createCellStyle();
        newStyle.cloneStyleFrom(srcCell.getCellStyle());
        destCell.setCellStyle(newStyle);

        // 2. 复制单元格值
        switch (srcCell.getCellType()) {
            case STRING:
                destCell.setCellValue(srcCell.getStringCellValue());
                break;
            case NUMERIC:
                destCell.setCellValue(srcCell.getNumericCellValue());
                break;
            case BOOLEAN:
                destCell.setCellValue(srcCell.getBooleanCellValue());
                break;
            case FORMULA:
                destCell.setCellFormula(srcCell.getCellFormula());
                break;
            case BLANK:
                destCell.setBlank();
                break;
            default:
                destCell.setCellValue("");
        }
    }

    private Workbook buildWorkbook(Report8D report8D, String fileName) {
        Path templatePath = Paths.get("file", "template", fileName);
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(templatePath.toString())) {

            if (Objects.isNull(inputStream)) {
                throw new FabosJsonApiErrorTip("8D报告生成失败，原因是获取模板文件失败");
            }

            Workbook wb = WorkbookFactory.create(inputStream);
            Sheet sheet = wb.getSheetAt(0);

            // 正则匹配占位符，如 ${aaa}
            Pattern pattern = Pattern.compile("\\$\\{(\\w+)(?:\\[(\\d+)\\])?(?:\\.(\\w+))?\\}");

            int memberRowIndex = INIT_ROW_INDEX;
            int tempRowIndex = INIT_ROW_INDEX;
            int longRowIndex = INIT_ROW_INDEX;
            int preventRowIndex = INIT_ROW_INDEX;
            int verifyRowIndex = INIT_ROW_INDEX;
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if (cell.getCellType() == CellType.STRING) {
                        String cellValue = cell.getStringCellValue();
                        if (cellValue.contains("memberInfoList[0]")) {
                            memberRowIndex = row.getRowNum();
                        }
                        if (cellValue.contains("tempMeasureList[0]")) {
                            tempRowIndex = row.getRowNum();
                        }
                        if (cellValue.contains("longMeasureList[0]")) {
                            longRowIndex = row.getRowNum();
                        }
                        if (cellValue.contains("preventMeasuresList[0]")) {
                            preventRowIndex = row.getRowNum();
                        }
                        if (cellValue.contains("verify[0]")) {
                            verifyRowIndex =  row.getRowNum();
                        }
                        Matcher matcher = pattern.matcher(cellValue);
                        if (matcher.find()) {
                            cell.setCellValue(getInstanceValue(matcher, report8D));
                        }
                    }
                }
            }

            fillListData(sheet, report8D, memberRowIndex, tempRowIndex, longRowIndex, preventRowIndex, verifyRowIndex);
            return wb;
        } catch (IOException | InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            log.error("8D报告生成失败，原因是获取模板文件失败", e);
            throw new FabosJsonApiErrorTip("8D报告生成失败");
        }
    }

    private static CellStyle[] saveRowStyles(Row row) {
        int cellCount = row.getLastCellNum();
        CellStyle[] styles = new CellStyle[cellCount];

        for (int i = 0; i < cellCount; i++) {
            Cell cell = row.getCell(i);
            if (cell != null) {
                styles[i] = cell.getCellStyle();
            }
        }

        return styles;
    }

    // 复制样式到新行
    private static void copyStylesToRow(Row newRow, CellStyle[] styles) {
        for (int i = 0; i < styles.length; i++) {
            if (styles[i] != null) {
                Cell cell = newRow.createCell(i);
                cell.setCellStyle(styles[i]);
            }
        }
    }

    private static List<CellRangeAddress> getRowMergedRegions(Sheet sheet, int rowIndex) {
        return sheet.getMergedRegions().stream()
                .filter(region ->
                        region.getFirstRow() == rowIndex &&
                                region.getLastRow() == rowIndex
                )
                .toList();
    }

    private static void createMergedRegionsForNewRow(Sheet sheet,
                                                     List<CellRangeAddress> templateRegions,
                                                     int newRowIndex) {
        for (CellRangeAddress region : templateRegions) {
            CellRangeAddress newRegion = new CellRangeAddress(
                    newRowIndex,
                    newRowIndex,
                    region.getFirstColumn(),
                    region.getLastColumn()
            );
            sheet.addMergedRegion(newRegion);
        }
    }

    private void fillListData(Sheet sheet, Report8D report8D, int memberRowIndex, int tempRowIndex, int longRowIndex,
                              int preventRowIndex, int verifyRowIndex) {
        if ((memberRowIndex != INIT_ROW_INDEX) && CollectionUtils.isNotEmpty(report8D.getMemberInfoList())) {
            List<MemberInfo> memberInfoList = report8D.getMemberInfoList();
            Row memberRow = sheet.getRow(memberRowIndex);
            if (Objects.isNull(memberRow)) {
                log.error("小组成员模板行不存在");
                return;
            }
            CellStyle[] cellStyles = saveRowStyles(memberRow);
            List<CellRangeAddress> rowMergedRegions = getRowMergedRegions(sheet, memberRowIndex);
            for (int i = 0; i < memberInfoList.size(); i++) {
                Row row = sheet.getRow(memberRowIndex + i);
                if (Objects.isNull(row)) {
                    row = sheet.createRow(memberRowIndex + i);
                    copyStylesToRow(row, cellStyles);
                    row.setHeight(memberRow.getHeight());
                    createMergedRegionsForNewRow(sheet, rowMergedRegions, memberRowIndex + i);
                }
                fillMemberRow(row, memberInfoList.get(i));
            }
        }

        if ((tempRowIndex != INIT_ROW_INDEX) && CollectionUtils.isNotEmpty(report8D.getTempMeasureList())) {
            List<TempMeasure> tempMeasureList = report8D.getTempMeasureList();
            Row tempRow = sheet.getRow(tempRowIndex);
            if (Objects.isNull(tempRow)) {
                log.error("临时措施模板行不存在");
                return;
            }
            CellStyle[] cellTempStyles = saveRowStyles(tempRow);
            List<CellRangeAddress> rowTempMergedRegions = getRowMergedRegions(sheet, tempRowIndex);
            for (int i = 0; i < tempMeasureList.size(); i++) {
                Row row = sheet.getRow(tempRowIndex + i);
                if (Objects.isNull(row)) {
                    row = sheet.createRow(tempRowIndex + i);
                    copyStylesToRow(row, cellTempStyles);
                    row.setHeight(tempRow.getHeight());
                    createMergedRegionsForNewRow(sheet, rowTempMergedRegions, tempRowIndex + i);
                }
                fillTempRow(row, tempMeasureList.get(i));
            }
        }

        if ((longRowIndex != INIT_ROW_INDEX) && CollectionUtils.isNotEmpty(report8D.getLongMeasureList())) {
            List<LongMeasure> longMeasureList = report8D.getLongMeasureList();
            Row longRow = sheet.getRow(longRowIndex);
            if (Objects.isNull(longRow)) {
                log.error("长期措施模板行不存在");
                return;
            }
            CellStyle[] cellLongStyles = saveRowStyles(longRow);
            List<CellRangeAddress> rowLongMergedRegions = getRowMergedRegions(sheet, longRowIndex);
            for (int i = 0; i < longMeasureList.size(); i++) {
                Row row = sheet.getRow(longRowIndex + i);
                if (Objects.isNull(row)) {
                    row = sheet.createRow(longRowIndex + i);
                    copyStylesToRow(row, cellLongStyles);
                    row.setHeight(longRow.getHeight());
                    createMergedRegionsForNewRow(sheet, rowLongMergedRegions, longRowIndex + i);
                }
                fillLongRow(row, longMeasureList.get(i));
            }
        }

        if ((preventRowIndex != INIT_ROW_INDEX) && CollectionUtils.isNotEmpty(report8D.getPreventMeasuresList())) {
            List<PreventMeasures> preventMeasuresList = report8D.getPreventMeasuresList();
            Row preventRow = sheet.getRow(preventRowIndex);
            if (Objects.isNull(preventRow)) {
                log.error("预防措施模板行不存在");
                return;
            }
            CellStyle[] cellPreventStyles = saveRowStyles(preventRow);
            List<CellRangeAddress> rowPreventMergedRegions = getRowMergedRegions(sheet, preventRowIndex);
            for (int i = 0; i < preventMeasuresList.size(); i++) {
                Row row = sheet.getRow(preventRowIndex + i);
                if (Objects.isNull(row)) {
                    row = sheet.createRow(preventRowIndex + i);
                    copyStylesToRow(row, cellPreventStyles);
                    row.setHeight(preventRow.getHeight());
                    createMergedRegionsForNewRow(sheet, rowPreventMergedRegions, preventRowIndex + i);
                }
                fillPreventRow(row, preventMeasuresList.get(i));
            }
        }

        if ((verifyRowIndex != INIT_ROW_INDEX)) {
            List<VerifyResultDto> verifyResultDtoList = buildVerifyResultDto(report8D);
            Row verifyRow = sheet.getRow(verifyRowIndex);
            if (Objects.isNull(verifyRow)) {
                log.error("验证模板行不存在");
                return;
            }
            CellStyle[] cellVerifyStyles = saveRowStyles(verifyRow);
            List<CellRangeAddress> rowPreventMergedRegions = getRowMergedRegions(sheet, verifyRowIndex);
            for (int i = 0; i < verifyResultDtoList.size(); i++) {
                Row row = sheet.getRow(verifyRowIndex + i);
                if (Objects.isNull(row)) {
                    row = sheet.createRow(verifyRowIndex + i);
                    copyStylesToRow(row, cellVerifyStyles);
                    row.setHeight(verifyRow.getHeight());
                    createMergedRegionsForNewRow(sheet, rowPreventMergedRegions, verifyRowIndex + i);
                }
                fillVerifyRow(row, verifyResultDtoList.get(i));
            }
        }
    }

    private void fillVerifyRow(Row row, VerifyResultDto verifyResultDto) {
        Cell nameCell = row.getCell(0);
        nameCell.setCellValue(Objects.isNull(verifyResultDto.getVerifyResult()) ? ""
                : verifyResultDto.getVerifyResult());

        Cell positionCell = row.getCell(6);
        positionCell.setCellValue(Objects.isNull(verifyResultDto.getVerifyPersonName()) ? ""
                : verifyResultDto.getVerifyPersonName());

        Cell phoneCell = row.getCell(7);
        phoneCell.setCellValue(Objects.isNull(verifyResultDto.getVerifyDate()) ? ""
                : new SimpleDateFormat("yyyy-MM-dd").format(verifyResultDto.getVerifyDate()));
    }

    private List<VerifyResultDto> buildVerifyResultDto(Report8D report8D) {
        List<VerifyResultDto> verifyResultDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(report8D.getTempMeasureList())) {
            report8D.getTempMeasureList().forEach(d -> {
                verifyResultDtoList.add(VerifyResultDto.buildInstance(d));
            });
        }
        if (CollectionUtils.isNotEmpty(report8D.getLongMeasureList())) {
            report8D.getLongMeasureList().forEach(d -> {
                verifyResultDtoList.add(VerifyResultDto.buildInstance(d));
            });
        }
        return verifyResultDtoList;
    }

    private void fillPreventRow(Row row, PreventMeasures preventMeasures) {
        Cell nameCell = row.getCell(0);
        nameCell.setCellValue(Objects.isNull(preventMeasures.getPreventMeasure()) ? ""
                : preventMeasures.getPreventMeasure());

        Cell positionCell = row.getCell(6);
        positionCell.setCellValue(Objects.isNull(preventMeasures.getResponsiblePersonName()) ? ""
                : preventMeasures.getResponsiblePersonName());

        Cell phoneCell = row.getCell(7);
        phoneCell.setCellValue(Objects.isNull(preventMeasures.getCompletionDate()) ? ""
                : new SimpleDateFormat("yyyy-MM-dd").format(preventMeasures.getCompletionDate()));
    }

    private void fillLongRow(Row row, LongMeasure longMeasure) {
        Cell nameCell = row.getCell(0);
        nameCell.setCellValue(Objects.isNull(longMeasure.getLongMeasure()) ? ""
                : longMeasure.getLongMeasure());

        Cell positionCell = row.getCell(6);
        positionCell.setCellValue(Objects.isNull(longMeasure.getResponsiblePersonName()) ? ""
                : longMeasure.getResponsiblePersonName());

        Cell phoneCell = row.getCell(7);
        phoneCell.setCellValue(Objects.isNull(longMeasure.getCompletionDate()) ? ""
                : new SimpleDateFormat("yyyy-MM-dd").format(longMeasure.getCompletionDate()));
    }

    private void fillTempRow(Row row, TempMeasure tempMeasure) {
        Cell nameCell = row.getCell(0);
        nameCell.setCellValue(Objects.isNull(tempMeasure.getTempMeasure()) ? ""
                : tempMeasure.getTempMeasure());

        Cell positionCell = row.getCell(6);
        positionCell.setCellValue(Objects.isNull(tempMeasure.getResponsiblePersonName()) ? ""
                : tempMeasure.getResponsiblePersonName());

        Cell phoneCell = row.getCell(7);
        phoneCell.setCellValue(Objects.isNull(tempMeasure.getCompletionDate()) ? ""
                : new SimpleDateFormat("yyyy-MM-dd").format(tempMeasure.getCompletionDate()));
    }

    private void fillMemberRow(Row row, MemberInfo member) {
        // A列：成员姓名
        Cell nameCell = row.getCell(0);
        nameCell.setCellValue(member.getMemberName());

        // D列：职务
        Cell positionCell = row.getCell(3);
        positionCell.setCellValue(PositionTypeEnum.getValueByKey(member.getPosition()));

        // G列：电话号码
        Cell phoneCell = row.getCell(6);
        phoneCell.setCellValue(member.getPhoneNumber());
    }

    private String getInstanceValue(Matcher matcher, Report8D report8D)
            throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        if (Objects.nonNull(matcher.group(2)) || Objects.nonNull(matcher.group(3))) {
            return "";
        }
        String field = matcher.group(1);
        Object value = PropertyUtils.getProperty(report8D, field);
        if (Objects.isNull(value)) {
            return "";
        } else if (value instanceof Date) {
            return new SimpleDateFormat("yyyy-MM-dd").format(value);
        } else {
            return value.toString();
        }

    }
}
