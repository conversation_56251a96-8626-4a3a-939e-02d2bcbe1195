package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler.CMKSampleReceiveDynamicHandler;
import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.processInspect.domain.publicInspectionTask.mto.ProcessUserForInsTaskMTO;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@FabosJson(
        name = "收样自定义按钮模型"
)
@Table(name = "qms_cmk_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class CMKSampleTaskReceiveMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "条码号（取样任务单号）"),
            edit = @Edit(title = "条码号（取样任务单号）")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "cmk检验任务单号"),
            edit = @Edit(title = "cmk检验任务单号",readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "generalCode", dynamicHandler = CMKSampleReceiveDynamicHandler.class))
    )
    private String cmkInspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序",readonly = @Readonly)
    )
    private String processName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时间", type = ViewType.DATE),
            edit = @Edit(title = "取样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date samplePlanDate;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量",readonly = @Readonly,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "外观检验"),
            edit = @Edit(title = "外观检验",notNull = true, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String appearanceInspect;

    @FabosJsonField(
            views = @View(title = "复核数量"),
            edit = @Edit(title = "复核数量",notNull = true,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0,max = 100,precision = 2))
    )
    private Double reviewQuantity;

    @FabosJsonField(
            views = @View(title = "不合格处理"),
            edit = @Edit(title = "不合格处理",notNull = true, type = EditType.CHOICE,search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedHandleWayEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'"))
    )
    private String unqualifiedHandle;

    @FabosJsonField(
            views = @View(title = "异常描述"),
            edit = @Edit(title = "异常描述",
                    notNull = true,
                    search = @Search(),
                    type = EditType.TEXTAREA,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "unqualifiedHandle == 'exception'"))
    )
    private String abnormalDescription;

    @FabosJsonField(
            views = @View(title = "发现时间"),
            edit = @Edit(title = "发现时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date submissionTime;

    @Transient
    @FabosJsonField(
            views = @View(title = "发现人",show = false, column = "name"),
            edit = @Edit(title = "发现人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")
            )
    )
    private ProcessUserForInsTaskMTO discoveredPerson;

    @FabosJsonField(
            views = @View(title = "发现人ID", show = false),
            edit = @Edit(title = "发现人ID", show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "id"))
    )
    private String discoveredPersonId;

    @FabosJsonField(
            views = @View(title = "发现人"),
            edit = @Edit(title = "发现人",
                    notNull = true,
                    readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'")),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "discoveredPerson", beFilledBy = "name"))
    )
    private String discoveredPersonName;

    @FabosJsonField(
            views = @View(title = "异常附件"),
            edit = @Edit(title = "异常附件", type = EditType.ATTACHMENT,
                    notNull = true,
                    search = @Search(),
                    attachmentType = @AttachmentType,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "unqualifiedHandle == 'exception'"))
    )
    private String abnormalAttachments;
}
