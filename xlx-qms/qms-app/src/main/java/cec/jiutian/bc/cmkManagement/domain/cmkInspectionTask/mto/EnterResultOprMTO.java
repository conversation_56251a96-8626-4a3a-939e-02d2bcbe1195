package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "CMK检验任务录入结果MTO"
)
@TemplateType(type = "multiTable")
public class EnterResultOprMTO extends BaseModel {

    @FabosJsonField(
            edit = @Edit(title = "明细", type = EditType.TAB_REFER_ADD,readonly = @Readonly(edit = false)),
            referenceAddType = @ReferenceAddType(referenceClass = "CMKInspectionTaskDetail",
                    editable = {"inspectValue"}),
            views = @View(title = "明细", type= ViewType.TABLE_VIEW,extraPK = "personId")
    )
    @JoinColumn(name = "lab_id")
    @OneToMany(cascade = CascadeType.ALL)
    private List<EnterResultOprDetailMTO> detailMTOList;
}
