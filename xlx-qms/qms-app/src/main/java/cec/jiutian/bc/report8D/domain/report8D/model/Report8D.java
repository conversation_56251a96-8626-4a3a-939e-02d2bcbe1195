package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.mto.InventoryMTO;
import cec.jiutian.bc.report8D.domain.report8D.handler.*;
import cec.jiutian.bc.report8D.domain.report8D.proxy.Report8DProxy;
import cec.jiutian.bc.report8D.enums.AuditResultEnum;
import cec.jiutian.bc.report8D.enums.MeasureCompleteStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.FormOperation;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "8D报告",
        orderBy = "Report8D.createTime desc",
        power = @Power(add = false, edit = false, export = false, importable = false),
        dataProxy = Report8DProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "status !='WAIT_SUBMIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "add",
                        ifExpr = "1 =='1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "创建",
                        code = "Report8D@AddButton",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = Report8DAdd.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@AddButton"
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "Report8D@EDIT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        operationHandler = Report8DEditHandler.class,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = Report8DAdd.class,
                        ifExpr = "selectedItems[0].status!='WAIT_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@EDIT"
                        )
                ),
                @RowOperation(
                        title = "提交",
                        code = "Report8D@SUBMIT",
                        mode = RowOperation.Mode.HEADER,
                        operationHandler = Report8DSubmitHandler.class,
                        callHint = "请确认是否提交？",
                        ifExpr = "selectedItems[0].status != 'WAIT_SUBMIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@SUBMIT"
                        )
                ),
                @RowOperation(
                        title = "D1",
                        code = "Report8D@D1",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD1Handler.class,
                        fabosJsonClass = Report8DD1.class,
                        ifExpr = "selectedItems[0].status != 'D1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D1"
                        )
                ),
                @RowOperation(
                        title = "D2",
                        code = "Report8D@D2",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD2Handler.class,
                        fabosJsonClass = Report8DD2.class,
                        ifExpr = "selectedItems[0].status != 'D2'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D2"
                        )
                ),
                @RowOperation(
                        title = "D3",
                        code = "Report8D@D3",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD3Handler.class,
                        fabosJsonClass = Report8DD3.class,
                        ifExpr = "selectedItems[0].status != 'D3'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D3"
                        )
                ),
                @RowOperation(
                        title = "D4",
                        code = "Report8D@D4",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD4Handler.class,
                        fabosJsonClass = Report8DD4.class,
                        ifExpr = "selectedItems[0].status != 'D4'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D4"
                        )
                ),
                @RowOperation(
                        title = "D5",
                        code = "Report8D@D5",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD5Handler.class,
                        fabosJsonClass = Report8DD5.class,
                        ifExpr = "selectedItems[0].status != 'D5'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D5"
                        )
                ),
                @RowOperation(
                        title = "D6",
                        code = "Report8D@D6",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD6Handler.class,
                        fabosJsonClass = Report8DD6.class,
                        ifExpr = "selectedItems[0].status != 'D6'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D6"
                        )
                ),
                @RowOperation(
                        title = "D7",
                        code = "Report8D@D7",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD7Handler.class,
                        fabosJsonClass = Report8DD7.class,
                        ifExpr = "selectedItems[0].status != 'D7'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D7"
                        )
                ),
                @RowOperation(
                        title = "D8",
                        code = "Report8D@D8",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        operationHandler = Report8DD8Handler.class,
                        fabosJsonClass = Report8DD8.class,
                        ifExpr = "selectedItems[0].status != 'D8'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@D8"
                        )
                ),
                @RowOperation(
                        title = "8D报告生成",
                        code = "Report8D@Report",
                        mode = RowOperation.Mode.HEADER,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = Report8DReportHandler.class,
                        type = RowOperation.Type.FILE,
                        callHint = "请确认是否生成8D报告？",
                        ifExpr = "selectedItems[0].status != 'CLOSED'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Report8D@Report"
                        )
                ),
        },
        formOperation = {
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D2Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD2SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D2"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D3Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD3SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D3"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D4Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD4SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D4"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D5Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD5SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D5"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D6Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD6SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D6"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D7Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD7SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D7"},
                        closeFormAfterClick = true
                ),
                @FormOperation(
                        name = "确认并提交",
                        code = "Report8D@D8Submit",
                        position = FormOperation.Position.RIGHT,
                        operationHandler = Report8DD8SaveHandler.class,
                        affectMode = FormOperation.AffectMode.rowOperation,
                        affectRowOperations = {"Report8D@D8"},
                        closeFormAfterClick = true
                )
        }
)
@TemplateType(type = "multiTable")
public class Report8D extends MetaModel {

    // 8D编号
    @FabosJsonField(
            views = @View(title = "8D编号"),
            edit = @Edit(title = "8D编号",
                    notNull = true,
                    search = @Search
            )
    )
    private String generalCode;

    // 题目
    @FabosJsonField(
            views = @View(title = "题目"),
            edit = @Edit(
                    title = "题目",
                    inputType = @InputType(length = 255),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "title", length = 255)
    private String title;

    // 投诉类型
    @FabosJsonField(
            views = @View(title = "投诉类型"),
            edit = @Edit(
                    title = "投诉类型",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = {DictChoiceFetchHandler.class},
                            fetchHandlerParams = {"ComplaintType"}
                    )
            )
    )
    @Column(name = "complaint_type", length = 20)
    private String complaintType;

    // 发生时间
    @FabosJsonField(
            views = @View(title = "发生时间", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "发生时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "occurrence_time", nullable = false)
    private Date occurrenceTime;

    // 产品批号
    @Transient
    @FabosJsonField(
            views = @View(title = "选择产品",
                    type = ViewType.TABLE_VIEW,
                    column = "materialName",
                    show = false
            ),
            edit = @Edit(title = "选择产品",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    filter = @Filter(value = "stockType = 'PRODUCT'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "materialName")
            )
    )
    private InventoryMTO productMTO;

    @FabosJsonField(
            views = @View(title = "产品批号"),
            edit = @Edit(
                    title = "产品批号",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "lotSerialId"))
    )
    @Column(name = "product_code", length = 20)
    private String productCode;


    // 产品名称
    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(
                    title = "产品名称",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "productMTO", beFilledBy = "materialName"))
    )
    @Column(name = "product_name", length = 20)
    private String productName;

    // 客户
    @FabosJsonField(
            views = @View(title = "客户"),
            edit = @Edit(
                    title = "客户",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "customer", length = 20)
    private String customer;

    // 总批量数
    @FabosJsonField(
            views = @View(title = "总批量数"),
            edit = @Edit(
                    title = "总批量数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "total_batch_quantity", length = 20)
    private Integer totalBatchQuantity;

    // 提出日期
    @FabosJsonField(
            views = @View(title = "提出日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "提出日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "raised_Date", nullable = false)
    private Date raisedDate;

    // 供应商
    @FabosJsonField(
            views = @View(title = "供应商"),
            edit = @Edit(
                    title = "供应商",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "supplier", length = 20)
    private String supplier;

    // 抽样数
    @FabosJsonField(
            views = @View(title = "抽样数"),
            edit = @Edit(
                    title = "抽样数",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "sample_quantity", length = 20)
    private Integer sampleQuantity;

    // 提出人
    @Transient
    @FabosJsonField(
            views = @View(title = "选择提出人", column = "name", show = false),
            edit = @Edit(
                    title = "选择提出人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO reporterUserMTO;

    @FabosJsonField(
            views = @View(title = "提出人"),
            edit = @Edit(
                    title = "提出人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "name"))
    )
    @Column(name = "reporter", length = 20)
    private String reporter;

    @FabosJsonField(
            views = @View(title = "提出人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "提出人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "reporterUserMTO", beFilledBy = "id"))
    )
    @Column(name = "reporter_id", length = 50)
    private String reporterId;

    // 联络人
    @Transient
    @FabosJsonField(
            views = @View(title = "选择联络人", column = "name", show = false),
            edit = @Edit(
                    title = "选择联络人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO contactUserMTO;

    @FabosJsonField(
            views = @View(title = "联络人"),
            edit = @Edit(
                    title = "联络人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "name"))
    )
    @Column(name = "contact_person", length = 20)
    private String contactPerson;

    @FabosJsonField(
            views = @View(title = "联络人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "联络人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "contactUserMTO", beFilledBy = "id"))
    )
    @Column(name = "contact_person_id", length = 50)
    private String contactPersonId;

    // 不良数量
    @FabosJsonField(
            views = @View(title = "不良数量"),
            edit = @Edit(
                    title = "不良数量",
                    numberType = @NumberType(min = 0),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "defective_count", length = 20)
    private Integer defectiveCount;

    // 要求完成日期
    @FabosJsonField(
            views = @View(title = "要求完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "要求完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "required_completion_date")
    private Date requiredCompletionDate;

    // 发生地点
    @FabosJsonField(
            views = @View(title = "发生地点"),
            edit = @Edit(
                    title = "发生地点",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "occurrence_location", length = 20)
    private String occurrenceLocation;

    @FabosJsonField(
            views = @View(title = "8D附件"),
            edit = @Edit(title = "8D附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String d8Attachment;

    // 状态
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    search = @Search,
                    readonly = @Readonly(add = true, edit = true),
                    defaultVal = "WAIT_SUBMIT",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = Report8DStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(name = "status", length = 20)
    private String status;

    // 问题描述
    @FabosJsonField(
            views = @View(title = "问题描述"),
            edit = @Edit(
                    title = "问题描述",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "problem_description", length = 100)
    private String problemDescription;

    @FabosJsonField(
            views = @View(title = "问题描述附件"),
            edit = @Edit(title = "问题描述附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String problemAttachment;

    // 根本原因
    @FabosJsonField(
            views = @View(title = "根本原因"),
            edit = @Edit(
                    title = "根本原因",
                    type = EditType.TEXTAREA,
                    readonly = @Readonly,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "root_cause", length = 100)
    private String rootCause;

    @FabosJsonField(
            views = @View(title = "根本原因附件"),
            edit = @Edit(title = "根本原因附件",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String causeAttachment;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "新增成员", column = "memberName", type = ViewType.TABLE_VIEW, show = false),
            edit = @Edit(
                    title = "新增成员",
                    type = EditType.TAB_TABLE_ADD,
                    allowAddMultipleRows = true,
                    show = false,
                    referenceTableType = @ReferenceTableType(label = "memberName")
            )
    )
    private List<MemberInfo> memberInfoList;

    @FabosJsonField(
            views = @View(title = "审核人ID", show = false),
            edit = @Edit(title = "审核人ID", show = false)
    )
    @Column(name = "audit_person_id", length = 50)
    private String auditPersonId;

    @FabosJsonField(
            views = @View(title = "审核人", show = false),
            edit = @Edit(title = "审核人", show = false)
    )
    @Column(name = "audit_person_name", length = 50)
    private String auditPersonName;
    //临时措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "临时措施", column = "tempMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "临时措施",
                    type = EditType.TAB_TABLE_ADD,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(label = "tempMeasure")
            )
    )
    private List<TempMeasure> tempMeasureList;

    // 临时措施状态
    @FabosJsonField(
            views = @View(title = "临时措施状态", show = false),
            edit = @Edit(
                    title = "临时措施状态",
                    inputType = @InputType(length = 20),
                    show = false,
                    defaultVal = "NOT_FINISH",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MeasureCompleteStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(length = 20)
    private String tempStatus;

    @FabosJsonField(
            views = @View(title = "临时措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "临时措施下所有责任人的id集合",show = false)
    )
    private String tempUserIds;

    //长期措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "长期措施", column = "longMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "长期措施",
                    type = EditType.TAB_TABLE_ADD,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(label = "longMeasure")
            )
    )
    private List<LongMeasure> longMeasureList;

    @FabosJsonField(
            views = @View(title = "流出对策", show = false),
            edit = @Edit(
                    title = "流出对策",
                    type = EditType.TEXTAREA,
                    show = false,
                    inputType = @InputType(length = 100)
            )
    )
    @Column(name = "outflow_measure", length = 100)
    private String outflowMeasure;

    // 长期措施状态
    @FabosJsonField(
            views = @View(title = "长期措施状态", show = false),
            edit = @Edit(
                    title = "长期措施状态",
                    inputType = @InputType(length = 20),
                    show = false,
                    defaultVal = "NOT_FINISH",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MeasureCompleteStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(length = 20)
    private String longStatus;

    @FabosJsonField(
            views = @View(title = "长期措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "长期措施下所有责任人的id集合",show = false)
    )
    private String longUserIds;

    //预防措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "预防措施", column = "preventMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "预防措施",
                    type = EditType.TAB_TABLE_ADD,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(label = "preventMeasure")
            )
    )
    private List<PreventMeasures> preventMeasuresList;

    // 预防措施状态
    @FabosJsonField(
            views = @View(title = "预防措施状态", show = false),
            edit = @Edit(
                    title = "预防措施状态",
                    inputType = @InputType(length = 20),
                    show = false,
                    defaultVal = "NOT_FINISH",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = MeasureCompleteStatusEnum.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    @Column(length = 20)
    private String preventStatus;

    @FabosJsonField(
            views = @View(title = "预防措施下所有责任人的id集合", show = false),
            edit = @Edit(title = "预防措施下所有责任人的id集合",show = false)
    )
    private String preventUserIds;


    //导出8D报告需要
    @FabosJsonField(
            views = @View(title = "D2审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D2审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date d2AuditTime;

    @FabosJsonField(
            views = @View(title = "D3审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D3审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date d3AuditTime;
    @FabosJsonField(
            views = @View(title = "D4审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D4审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date d4AuditTime;
    @FabosJsonField(
            views = @View(title = "D5审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D5审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date d5AuditTime;
    @FabosJsonField(
            views = @View(title = "D6审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D6审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date d6AuditTime;
    @FabosJsonField(
            views = @View(title = "D7审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D7审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date d7AuditTime;
    @FabosJsonField(
            views = @View(title = "D8审核时间", show = false, type = ViewType.DATE_TIME),
            edit = @Edit(title = "D8审核时间", show = false, dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    private Date d8AuditTime;
    @FabosJsonField(
            views = @View(title = "审核意见", show = false),
            edit = @Edit(title = "审核意见", show = false)
    )
    @Column(length = 10)
    private String d8Result;

    @FabosJsonField(
            views = @View(title = "d2创建者", show = false),
            edit = @Edit(title = "d2创建者", show = false)
    )
    @Column(length = 20)
    private String d2CreateBy;
    @FabosJsonField(
            views = @View(title = "d3创建者", show = false),
            edit = @Edit(title = "d3创建者", show = false)
    )
    @Column(length = 20)
    private String d3CreateBy;
    @FabosJsonField(
            views = @View(title = "d4创建者", show = false),
            edit = @Edit(title = "d4创建者", show = false)
    )
    @Column(length = 20)
    private String d4CreateBy;

}
