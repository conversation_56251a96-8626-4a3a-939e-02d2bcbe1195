package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.AuditTask;
import cec.jiutian.bc.report8D.domain.report8D.model.AuditTaskAudit;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.AuditResultEnum;
import cec.jiutian.bc.report8D.enums.AuditTaskStatusEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.List;

@Component
public class AuditTaskAuditHandler implements OperationHandler<AuditTask, AuditTaskAudit> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<AuditTask> data, AuditTaskAudit modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            AuditTask auditTask = data.get(0);
            auditTask.setResult(modelObject.getResult());
            auditTask.setExplain(modelObject.getExplain());
            auditTask.setAttachment(modelObject.getAttachment());
            auditTask.setAuditTime(Date.from(Instant.now()));
            auditTask.setStatus(AuditTaskStatusEnum.Enum.COMPLETE.name());
            fabosJsonDao.mergeAndFlush(auditTask);

            // 审核不通过驳回上一个状态    通过则到下一个状态
            Report8D report8D = fabosJsonDao.findById(Report8D.class, auditTask.getReport8dId());
            report8D.setStatus(AuditResultEnum.Enum.REJECT.name().equals(auditTask.getResult())
                    ? Report8DStatusEnum.getPreStatus(auditTask.getReport8dStatus())
                    : Report8DStatusEnum.getNextStatus(auditTask.getReport8dStatus()));
            report8DSetAuditTime(report8D,auditTask);
            fabosJsonDao.mergeAndFlush(report8D);
        }
        return "msg.success('操作成功')";
    }

    private void report8DSetAuditTime(Report8D report8D, AuditTask auditTask) {
        if (AuditResultEnum.Enum.REJECT.name().equals(auditTask.getResult())) {
            return;
        }
        switch (auditTask.getReport8dStatus()) {
            case "D2_AUDIT":
                report8D.setD2AuditTime(Date.from(Instant.now()));
                break;
            case "D3_AUDIT":
                report8D.setD3AuditTime(Date.from(Instant.now()));
                break;
            case "D4_AUDIT":
                report8D.setD4AuditTime(Date.from(Instant.now()));
                break;
            case "D5_AUDIT":
                report8D.setD5AuditTime(Date.from(Instant.now()));
                break;
            case "D6_AUDIT":
                report8D.setD6AuditTime(Date.from(Instant.now()));
                break;
            case "D7_AUDIT":
                report8D.setD7AuditTime(Date.from(Instant.now()));
                break;
            case "D8_AUDIT":
                report8D.setD8AuditTime(Date.from(Instant.now()));
                report8D.setD8Result(AuditResultEnum.getValueByKey(auditTask.getResult()));
                break;
        }

    }

    @Override
    public AuditTaskAudit fabosJsonFormValue(List<AuditTask> data, AuditTaskAudit fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            AuditTask task = data.get(0);
            fabosJsonForm.setResult(task.getResult());
            fabosJsonForm.setExplain(task.getExplain());
            fabosJsonForm.setAttachment(task.getAttachment());
        }
        return fabosJsonForm;
    }
}
