package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description TODO
 */
@Entity
@Table(name = "mf_prcs_oprtn_param")
@Getter
@Setter
@FabosJson(
        name = "工序信息",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class ProcessOperationMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "process_operation_code", length = 40)
    private String processOperationCode;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "process_operation_name", length = 40)
    private String processOperationName;

    @FabosJsonField(
            views = @View(title = "工艺流程id",show = false),
            edit = @Edit(title = "工艺流程id",
                    show = false
            )
    )
    @Column(name = "process_flow_id", length = 40)
    private Long processFlowId;

    @FabosJsonField(
            views = @View(title = "工艺id",show = false),
            edit = @Edit(title = "工艺id",show = false
            )
    )
    @Column(name = "process_id", length = 40)
    private Long processId;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "process_code", length = 40)
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "processName", length = 40)
    private String code;

    @Column(name = "crte_tm")
    private Date createTime;
}
