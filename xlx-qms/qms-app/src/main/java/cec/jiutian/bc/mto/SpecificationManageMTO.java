package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Formula;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @description TODO
 */
@Entity
@Table(name = "ms_spcfcn")
@Getter
@Setter
@FabosJson(
        name = "物资编码",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class SpecificationManageMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcfcn_cd", length = 40)
    private String code;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcfcn_nm", length = 40)
    private String name;

    @FabosJsonField(
            views = @View(title = "规格型号"),
            edit = @Edit(title = "规格型号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcfcn_dtl", length = 40)
    private String type;

    @FabosJsonField(
            views = @View(title = "分类编码"),
            edit = @Edit(title = "分类编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcdct_cd", length = 40)
    private String specificationCode;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称",readonly = @Readonly)

    )
    @Formula("(select t1.spcdct_nm from ms_spcdct t1 where t1.spcdct_cd = spcdct_cd)")
    private String specificationName;

    @Column(name = "crte_tm")
    private Date createTime;

    @FabosJsonField(
            views = @View(title = "生效标记"),
            edit = @Edit(title = "生效标记",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "vld_flg", length = 8)
    private String validateFlag;
}
