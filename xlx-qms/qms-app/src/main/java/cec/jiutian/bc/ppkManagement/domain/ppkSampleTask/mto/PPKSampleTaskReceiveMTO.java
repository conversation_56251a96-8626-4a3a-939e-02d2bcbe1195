package cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.mto;

import cec.jiutian.bc.materialInspect.enumeration.InspectionResultEnum;
import cec.jiutian.bc.modeler.enumration.UnqualifiedHandleWayEnum;
import cec.jiutian.bc.ppkManagement.domain.ppkSampleTask.handler.PPKSampleReceiveDynamicHandler;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(
        name = "收样自定义按钮模型"
)
@Table(name = "qms_ppk_sample_task",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
public class PPKSampleTaskReceiveMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "条码号（取样任务单号）"),
            edit = @Edit(title = "条码号（取样任务单号）")
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "PPK检验任务单号"),
            edit = @Edit(title = "PPK检验任务单号", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "generalCode", dynamicHandler = PPKSampleReceiveDynamicHandler.class))
    )
    private String ppkInspectionTaskCode;

    @FabosJsonField(
            views = @View(title = "工序"),
            edit = @Edit(title = "工序", readonly = @Readonly)
    )
    private String processName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时间", type = ViewType.DATE),
            edit = @Edit(title = "取样时间", show = false,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date samplePlanDate;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量", readonly = @Readonly,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0, precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "外观检验"),
            edit = @Edit(title = "外观检验", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = InspectionResultEnum.class))
    )
    private String appearanceInspect;

    @FabosJsonField(
            views = @View(title = "复核数量"),
            edit = @Edit(title = "复核数量", notNull = true,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0, max = 100, precision = 2))
    )
    private Double reviewQuantity;

    @FabosJsonField(
            views = @View(title = "不合格处理"),
            edit = @Edit(title = "不合格处理", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedHandleWayEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "appearanceInspect == 'UNQUALIFIED'"))
    )
    private String unqualifiedHandle;
}
