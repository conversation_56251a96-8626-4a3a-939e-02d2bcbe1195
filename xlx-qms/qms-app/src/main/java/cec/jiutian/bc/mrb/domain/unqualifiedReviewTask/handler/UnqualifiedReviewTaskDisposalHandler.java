package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler;

import cec.jiutian.bc.modeler.enumration.UnqualifiedLevelEnum;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.model.UnqualifiedReviewTask;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewDisposalAudit;
import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedAuditTypeEnum;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedReviewTaskPresentEnum;
import cec.jiutian.bc.mrb.service.MRBUnqualifiedReviewService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Component
@Slf4j
public class UnqualifiedReviewTaskDisposalHandler implements OperationHandler<UnqualifiedReviewTask, UnqualifiedReviewDisposalAudit> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private MRBUnqualifiedReviewService reviewService;

    @Override
    @Transactional
    public String exec(List<UnqualifiedReviewTask> data, UnqualifiedReviewDisposalAudit modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            UnqualifiedReviewTask model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(UnqualifiedReviewTaskPresentEnum.Enum.FINISH.name());
            model.setTime(new Date());
            reviewService.createUnqualifiedReviewApprovalInfo(model);
            fabosJsonDao.mergeAndFlush(model);
            //判断：一般和验证，到分管领导；严重到总经理
            MRBUnqualifiedReview unqualifiedReview = model.getUnqualifiedReview();
            String unqualifiedAuditType = model.getUnqualifiedAuditType();
            if (model.getReviewComment().equals(UnqualifiedReviewTaskCommentEnum.Enum.PASS.name())) {
                createDisposalAuditOrChangeStatus(unqualifiedReview, unqualifiedAuditType);
            } else {
                unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.TO_BE_SUBMITTED.name());
            }
            fabosJsonDao.mergeAndFlush(unqualifiedReview);
        }
        return "msg.success('操作成功')";
    }

    @Override
    public UnqualifiedReviewDisposalAudit fabosJsonFormValue(List<UnqualifiedReviewTask> data, UnqualifiedReviewDisposalAudit fabosJsonForm, String[] param) {
        UnqualifiedReviewTask model = data.get(0);
        MRBUnqualifiedReview unqualifiedReview = model.getUnqualifiedReview();
        BeanUtil.copyProperties(unqualifiedReview, fabosJsonForm);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }

    private void createDisposalAuditOrChangeStatus(MRBUnqualifiedReview unqualifiedReview, String unqualifiedAuditType) {
        String unqualifiedLevel = unqualifiedReview.getUnqualifiedLevel();
        log.info("The audit type is {}, the unqualified level is {}", unqualifiedLevel, unqualifiedLevel);
        if (unqualifiedLevel.equals(UnqualifiedLevelEnum.Enum.Slight.name())) {
            unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.COMPLETED.name());
        } else if (unqualifiedLevel.equals(UnqualifiedLevelEnum.Enum.Normal.name())) {
            if (unqualifiedAuditType.equals(UnqualifiedAuditTypeEnum.Enum.QUALITY_DEPART.name())) {
                //建立分管领导审批
                reviewService.createUnqualifiedReviewTaskByDisposal(unqualifiedReview, UnqualifiedAuditTypeEnum.Enum.RESPONSE_LEADER.name(),
                        unqualifiedReview.getResponseLeader().getId(), unqualifiedReview.getResponseLeader().getName());
            } else if (unqualifiedAuditType.equals(UnqualifiedAuditTypeEnum.Enum.RESPONSE_LEADER.name())) {
                unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.COMPLETED.name());
            }
        } else if (unqualifiedLevel.equals(UnqualifiedLevelEnum.Enum.Serious.name())) {
            if (unqualifiedAuditType.equals(UnqualifiedAuditTypeEnum.Enum.QUALITY_DEPART.name())) {
                //建立分管领导审批
                reviewService.createUnqualifiedReviewTaskByDisposal(unqualifiedReview, UnqualifiedAuditTypeEnum.Enum.RESPONSE_LEADER.name(),
                        unqualifiedReview.getResponseLeader().getId(), unqualifiedReview.getResponseLeader().getName());
            } else if (unqualifiedAuditType.equals(UnqualifiedAuditTypeEnum.Enum.RESPONSE_LEADER.name())) {
                //建立总经理审批
                reviewService.createUnqualifiedReviewTaskByDisposal(unqualifiedReview, UnqualifiedAuditTypeEnum.Enum.GENERAL_MANAGER.name(),
                        unqualifiedReview.getGeneralManager().getId(), unqualifiedReview.getGeneralManager().getName());
            } else if (unqualifiedAuditType.equals(UnqualifiedAuditTypeEnum.Enum.GENERAL_MANAGER.name())) {
                unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.COMPLETED.name());
            }
        }
    }
}
