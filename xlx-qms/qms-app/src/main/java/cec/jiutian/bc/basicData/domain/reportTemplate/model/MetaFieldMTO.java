package cec.jiutian.bc.basicData.domain.reportTemplate.model;


import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "metadata_model_field")
@Getter
@Setter
@NoArgsConstructor
@FabosJson(
        name = "模型元数据字段",
        orderBy = "id asc"
)
public class MetaFieldMTO {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "id"),
            views = @View(title = "id", show = false)
    )
    private String id;

    // 字段简短名
    @FabosJsonField(
            views = @View(title = "字段名"),
            edit = @Edit(title = "字段名", search = @Search(vague = true))
    )
    private String name;

    // 显示名称
    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", search = @Search(vague = true))
    )
    private String displayName;


    @ManyToOne
    @JsonIgnoreProperties({"fields"})
    @FabosJsonField(
            views = @View(title = "模型名称",
                    show = false,
                    column = "displayName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "模型名称", type = EditType.REFERENCE_TABLE,
                    show = false
            )
    )
    private MetadataModel referenceModel;

}
