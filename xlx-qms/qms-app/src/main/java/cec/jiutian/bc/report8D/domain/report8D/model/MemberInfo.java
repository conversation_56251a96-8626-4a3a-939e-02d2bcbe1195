package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.enums.PositionTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report_member_info")
@FabosJson(
        name = "小组成员",
        orderBy = "MemberInfo.createTime desc"
)
public class MemberInfo extends MetaModel {

    // 小组成员
    @Transient
    @FabosJsonField(
            views = @View(title = "小组成员", column = "name", show = false),
            edit = @Edit(
                    title = "小组成员",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO memberUserMTO;

    @FabosJsonField(
            views = @View(title = "小组成员"),
            edit = @Edit(
                    title = "小组成员",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    show = false,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "memberUserMTO", beFilledBy = "name"))
    )
    @Column(name = "member_name", length = 20)
    private String memberName;

    @FabosJsonField(
            views = @View(title = "小组成员ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "小组成员ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "memberUserMTO", beFilledBy = "id"))
    )
    @Column(name = "member_id", length = 50)
    private String memberId;

    // 职务
    @FabosJsonField(
            views = @View(title = "职务"),
            edit = @Edit(
                    title = "职务",
                    inputType = @InputType(length = 20),
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PositionTypeEnum.class)
            )
    )
    @Column(name = "position", length = 20)
    private String position;

    // 电话号码
    @FabosJsonField(
            views = @View(title = "电话号码"),
            edit = @Edit(
                    title = "电话号码",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly(add = true, edit = true),
                    notNull = true,
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "memberUserMTO", beFilledBy = "phoneNumber"))
    )
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
}
