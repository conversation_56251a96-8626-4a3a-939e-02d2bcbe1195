package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD6;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD6SaveHandler implements OperationHandler<Report8D, Report8DD6> {

    @Resource
    private Report8DD6Handler report8DD6Handler;

    @Override
    public Report8DD6 fabosJsonFormValue(List<Report8D> data, Report8DD6 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD6Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}