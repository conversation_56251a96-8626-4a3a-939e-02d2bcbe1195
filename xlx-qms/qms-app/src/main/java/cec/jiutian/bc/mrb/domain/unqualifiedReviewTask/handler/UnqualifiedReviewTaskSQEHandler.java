package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler;

import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.model.UnqualifiedReviewTask;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewSQEAudit;
import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedReviewTaskPresentEnum;
import cec.jiutian.bc.mrb.service.MRBUnqualifiedReviewService;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Component
public class UnqualifiedReviewTaskSQEHandler implements OperationHandler<UnqualifiedReviewTask, UnqualifiedReviewSQEAudit> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private MRBUnqualifiedReviewService reviewService;

    @Override
    @Transactional
    public String exec(List<UnqualifiedReviewTask> data, UnqualifiedReviewSQEAudit modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            UnqualifiedReviewTask model = data.get(0);
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setReviewerId(UserContext.getUserId());
            model.setReviewerName(UserContext.getUserName());
            model.setBusinessStatus(UnqualifiedReviewTaskPresentEnum.Enum.FINISH.name());
            model.setTime(new Date());
            reviewService.createUnqualifiedReviewApprovalInfo(model);
            fabosJsonDao.mergeAndFlush(model);
            //工艺完成后创建SQE
            MRBUnqualifiedReview unqualifiedReview = model.getUnqualifiedReview();
            if (model.getReviewComment().equals(UnqualifiedReviewTaskCommentEnum.Enum.PASS.name())) {
                unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.PENDING.name());
            } else {
                unqualifiedReview.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.TO_BE_RELEASED.name());
            }
            fabosJsonDao.mergeAndFlush(unqualifiedReview);
        }
        return "msg.success('操作成功')";
    }

    @Override
    public UnqualifiedReviewSQEAudit fabosJsonFormValue(List<UnqualifiedReviewTask> data, UnqualifiedReviewSQEAudit fabosJsonForm, String[] param) {
        BeanUtil.copyProperties(data.get(0), fabosJsonForm);
        return fabosJsonForm;
    }
}
