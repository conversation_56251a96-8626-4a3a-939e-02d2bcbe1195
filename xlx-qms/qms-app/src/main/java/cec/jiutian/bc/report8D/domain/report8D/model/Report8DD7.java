package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ReferenceTableType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "8D报告D7",
        orderBy = "Report8DD5.createTime desc"
)
public class Report8DD7 extends MetaModel {
    //预防措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "预防措施", column = "preventMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "预防措施",
                    type = EditType.TAB_TABLE_ADD,
                    referenceTableType = @ReferenceTableType(label = "preventMeasure")
            )
    )
    private List<D7PreventMeasures> d7PreventMeasuresList;
}
