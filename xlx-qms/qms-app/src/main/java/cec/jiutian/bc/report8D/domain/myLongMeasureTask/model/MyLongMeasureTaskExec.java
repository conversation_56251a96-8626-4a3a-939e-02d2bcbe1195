package cec.jiutian.bc.report8D.domain.myLongMeasureTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "我的临时措施",
        orderBy = "MyLongMeasureTaskExec.createTime desc",
        power = @Power(add = false,delete = false,edit = false)
)
public class MyLongMeasureTaskExec extends MetaModel {
    //长期措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "长期措施", column = "longMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "长期措施",
                    type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"progress", "completionDate", "completionEvidence"})
    )
    private List<MyLongMeasure> myLongMeasureList;
}
