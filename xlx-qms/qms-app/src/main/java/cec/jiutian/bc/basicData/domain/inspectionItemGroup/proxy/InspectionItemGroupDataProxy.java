package cec.jiutian.bc.basicData.domain.inspectionItemGroup.proxy;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.enumeration.InsGroupStatusEnum;
import cec.jiutian.bc.modeler.domain.dto.MesUnit;
import cec.jiutian.bc.modeler.domain.inspectionItem.model.InspectionItem;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.bc.mto.TechnologyFLowMTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class InspectionItemGroupDataProxy implements DataProxy<InspectionItemGroup> {


    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(InspectionItemGroup inspectionItemGroup) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException("当前用户未登录");
        }
        String userName = UserContext.getUserName();
        inspectionItemGroup.setCreatedBy(userName);
        inspectionItemGroup.setCreatedById(userId);
        inspectionItemGroup.setStatus(InsGroupStatusEnum.CREATED.name());
    }

    @Override
    public void afterAdd(InspectionItemGroup inspectionItemGroup) {
        List<InspectionItem> items = inspectionItemGroup.getInsItems();
        if (CollectionUtils.isNotEmpty(items)) {
            for (InspectionItem item : items) {
                item.setGroupId(InspectionItem.addGroupId(item.getGroupId(), inspectionItemGroup.getId()));
                fabosJsonDao.mergeAndFlush(item);
            }
        }
    }



    @Override
    public void beforeUpdate(InspectionItemGroup inspectionItemGroup) {
        List<InspectionItem> items = inspectionItemGroup.getInsItems();
        if (CollectionUtils.isNotEmpty(items)) {
            for (InspectionItem item : items) {
                item.setGroupId(InspectionItem.addGroupId(item.getGroupId(),inspectionItemGroup.getId()));
                fabosJsonDao.mergeAndFlush(item);
            }
        }
        inspectionItemGroup.setStatus(InsGroupStatusEnum.EDIT.name());
    }

    @Override
    public void afterUpdate(InspectionItemGroup inspectionItemGroup) {
        updateInspectionItem(inspectionItemGroup);
    }

    @Override
    public void afterDelete(InspectionItemGroup inspectionItemGroup) {
        updateInspectionItem(inspectionItemGroup);
    }

    private void updateInspectionItem(InspectionItemGroup inspectionItemGroup) {
        String hql = "from InspectionItem item where item.groupId like :groupId";
        TypedQuery<InspectionItem> query = fabosJsonDao.getEntityManager().createQuery(hql, InspectionItem.class);
        query.setParameter("groupId","%"+inspectionItemGroup.getId()+"%");
        List<InspectionItem> itemList = query.getResultList();

        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList.forEach(item -> {
                List<String> groupId = new java.util.ArrayList<>(List.of(item.getGroupId().split(",")));
                groupId.remove(inspectionItemGroup.getId());
                item.setGroupId(StringUtils.join(groupId,","));
            });
            fabosJsonDao.updateBatchById(itemList);
        }

        InspectionItemGroup groupData = fabosJsonDao.findById(InspectionItemGroup.class,inspectionItemGroup.getId());
        List<InspectionItem> groupItemList = groupData.getInsItems();
        if (CollectionUtils.isNotEmpty(groupItemList)) {
            groupItemList.forEach(item -> {
                List<String> groupId = new java.util.ArrayList<>(List.of(item.getGroupId().split(",")));
                groupId.add(groupData.getId());
                item.setGroupId(StringUtils.join(groupId,","));
            });
        }
        fabosJsonDao.updateBatchById(groupItemList);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String userId = UserContext.getUserId();
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException("当前用户未登录");
        }
//        //获取当前用户部门信息
//        UserMTO userMTO = fabosJsonDao.findById(UserMTO.class, userId);
//        String id = userMTO.getOrg().getId();



        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void afterSingleFetch(Map<String, Object> map) {
        if (map.get("usualUnitId") != null) {
            MesUnit mesUnit = fabosJsonDao.findById(MesUnit.class, map.get("usualUnitId"));
            if (mesUnit != null) {
                map.put("mesUnit", mesUnit);
                map.put("mesUnit_name", mesUnit.getName());
            } else {
                throw new FabosJsonApiErrorTip("未查到单位：" + String.valueOf(map.get("usualUnit")) + "源数据，请确认");
            }
        }
    }

}
