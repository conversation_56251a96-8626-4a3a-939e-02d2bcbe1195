package cec.jiutian.bc.cmkManagement.util;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description TODO
 */
public class ExcelUtil {

    private static final String CRITICAL_DEVICE = "关键设备";
    private static final String NON_CRITICAL_DEVICE = "非关键设备";
    private static final String LINKED_CELL_CRITICAL = "$M$6";
    private static final String LINKED_CELL_NON_CRITICAL = "$N$6";

    private static final String TOLERANCE_REQUIRE = "";

    /*
       强制重新计算公式
     */
    public static void forceFormulaRecalculation(Workbook workbook) {
        workbook.setForceFormulaRecalculation(true);
    }

    public static void setCheckBox(Sheet sheet) {
//        Drawing<?> drawing = sheet.getDrawingPatriarch();
//        if (drawing == null) {
//            System.out.println("没有找到任何绘图对象");
//            return;
//        }
//
//        for (Shape shape : drawing) {
//            if (shape instanceof XSSFSimpleShape &&
//                    "Check Box".equals(shape.getShapeName())) {
//                XSSFSimpleShape checkbox = (XSSFSimpleShape) shape;
//                String linkedCellRef = getCheckboxLinkedCell(checkbox);
//
//                if (linkedCellRef != null) {
//                    // 5. 设置单元格值控制复选框状态
//                    setCheckboxState(workbook, sheet, linkedCellRef, true);
//                }
//            }
//        }
    }
}
