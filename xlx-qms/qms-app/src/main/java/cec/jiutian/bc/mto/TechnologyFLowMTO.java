package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description TODO
 */
@Entity
@Table(name = "mf_prcs_flow")
@Getter
@Setter
@FabosJson(
        name = "工艺流程",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class TechnologyFLowMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            views = @View(title = "物资编码"),
            edit = @Edit(title = "物资编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcfcn_cd", length = 40)
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "spcfcn_nm", length = 40)
    private String materialName;

    @FabosJsonField(
            views = @View(title = "车间id",show = false),
            edit = @Edit(title = "车间id",
                    show = false
            )
    )
    @Column(name = "workshop_id", length = 40)
    private String workshopId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "workshop_name", length = 40)
    private String workshopName;

    @FabosJsonField(
            views = @View(title = "产线id",show = false),
            edit = @Edit(title = "产线id",show = false
            )
    )
    @Column(name = "production_line_id", length = 40)
    private String productionLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "production_line_name", length = 40)
    private String productionLineName;

    @FabosJsonField(
            views = @View(title = "工艺流程名称"),
            edit = @Edit(title = "工艺流程名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "flow_name", length = 40)
    private String flowName;

    @FabosJsonField(
            views = @View(title = "工艺流程版本"),
            edit = @Edit(title = "工艺流程版本",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "flow_vrsn", length = 40)
    private String flowVersion;

    @FabosJsonField(
            views = @View(title = "工艺id",show = false),
            edit = @Edit(title = "工艺id",show = false
            )
    )
    @Column(name = "process_id", length = 40)
    private String processId;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "process_code", length = 40)
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "processName", length = 40)
    private String code;

    @Column(name = "crte_tm")
    private Date createTime;
}
