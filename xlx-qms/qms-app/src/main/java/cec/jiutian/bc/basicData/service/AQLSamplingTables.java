package cec.jiutian.bc.basicData.service;

import cec.jiutian.bc.basicData.port.BatchRange;
import cec.jiutian.bc.urm.domain.dictionary.entity.Dictionary;
import cec.jiutian.bc.urm.domain.dictionary.entity.DictionaryItem;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description TODO
 */
@Component
public class AQLSamplingTables {
    @Resource
    private FabosJsonDao fabosJsonDao;

    private List<BatchRange> lotSize = getLotSize();

    public Integer getAQlSampleQuantity(Double lotQuantity, String levelValue) {
        Integer result = 0;
        List<BatchRange> rangeList = getLevelRanges(levelValue);
        Optional<BatchRange> batchRange = rangeList.stream().filter(d -> lotQuantity <= d.getMaxBatch() && lotQuantity >= d.getMinBatch()).findFirst();
        if (batchRange.isPresent()) {
            String sampleLetter = batchRange.get().getSampleLetter();
            Dictionary condition = new Dictionary();
            condition.setCode("AQL_SAMPLE_QUANTITY");
            Dictionary dictionary = fabosJsonDao.selectOne(condition);
            if (dictionary != null && CollectionUtils.isNotEmpty(dictionary.getDictionaryItems())) {
                Optional<DictionaryItem> dictionaryItem = dictionary.getDictionaryItems().stream().filter(d -> d.getCode().equals(sampleLetter)).findFirst();
                if (dictionaryItem.isPresent()) {
                    result = Integer.getInteger(dictionaryItem.get().getName());
                }
            }
        }
        return result;
    }

    private List<BatchRange> getLevelRanges(String levelValue) {
        List<BatchRange> ranges = new ArrayList<>();
        ranges.add(new BatchRange(2, 8, getSampleLetter(levelValue,"2")));
        ranges.add(new BatchRange(9, 15, getSampleLetter(levelValue,"9")));
        ranges.add(new BatchRange(16, 25, getSampleLetter(levelValue,"16")));
        ranges.add(new BatchRange(26, 50, getSampleLetter(levelValue,"26")));
        ranges.add(new BatchRange(51, 150, getSampleLetter(levelValue,"51")));
        ranges.add(new BatchRange(151, 280, getSampleLetter(levelValue,"151")));
        ranges.add(new BatchRange(281, 500, getSampleLetter(levelValue,"281")));
        ranges.add(new BatchRange(501, 1200, getSampleLetter(levelValue,"501")));
        ranges.add(new BatchRange(1201, 3200, getSampleLetter(levelValue,"1201")));
        ranges.add(new BatchRange(3201, 10000, getSampleLetter(levelValue,"3201")));
        ranges.add(new BatchRange(10001, 35000, getSampleLetter(levelValue,"10001")));
        ranges.add(new BatchRange(35001, 150000, getSampleLetter(levelValue,"35001")));
        ranges.add(new BatchRange(150001, 500000, getSampleLetter(levelValue,"150001")));
        ranges.add(new BatchRange(500001, 99999999, getSampleLetter(levelValue,"500001")));
        return ranges;
    }

    private String getSampleLetter(String levelValue, String itemCode) {
        String result = "";
        Dictionary dictionary = fabosJsonDao.queryEntity(Dictionary.class, "where code = :code",
                new HashMap<>(1){{
                    this.put("code", levelValue);
                }});
        if (dictionary != null && CollectionUtils.isNotEmpty(dictionary.getDictionaryItems())) {
            Optional<DictionaryItem> dictionaryItem = dictionary.getDictionaryItems().stream().filter(d -> d.getCode().equals(itemCode)).findFirst();
            if (dictionaryItem.isPresent()) {
                result = dictionaryItem.get().getName();
            }
        }
        return result;
    }

    public String getSampleSizeCode(String lotSize, String levelValue) {
        Dictionary condition = new Dictionary();
        condition.setCode(levelValue);
        Dictionary dictionary = fabosJsonDao.selectOne(condition);
        if (dictionary != null && CollectionUtils.isNotEmpty(dictionary.getDictionaryItems())) {
            Optional<DictionaryItem> dictionaryItem = dictionary.getDictionaryItems().stream().filter(d -> d.getCode().equals(lotSize)).findFirst();
            if (dictionaryItem.isPresent()) {
                return dictionaryItem.get().getName();
            }
        }
        return "";
    }

    public String getLotSize(Double sampleQuantity) {
        Optional<BatchRange> batchRange = lotSize.stream().filter(d -> sampleQuantity <= d.getMaxBatch() && sampleQuantity >= d.getMinBatch()).findFirst();
        if (batchRange.isPresent()) {
            return batchRange.get().getSampleLetter();
        }
        return "";
    }

    private List<BatchRange> getLotSize() {
        List<BatchRange> ranges = new ArrayList<>();
        ranges.add(new BatchRange(2, 8, "2"));
        ranges.add(new BatchRange(9, 15, "9"));
        ranges.add(new BatchRange(16, 25,"16"));
        ranges.add(new BatchRange(26, 50, "26"));
        ranges.add(new BatchRange(51, 90, "51"));
        ranges.add(new BatchRange(91, 150, "91"));
        ranges.add(new BatchRange(151, 280, "151"));
        ranges.add(new BatchRange(281, 500, "281"));
        ranges.add(new BatchRange(501, 1200, "501"));
        ranges.add(new BatchRange(1201, 3200, "1201"));
        ranges.add(new BatchRange(3201, 10000, "3201"));
        ranges.add(new BatchRange(10001, 35000, "10001"));
        ranges.add(new BatchRange(35001, 150000, "35001"));
        ranges.add(new BatchRange(150001, 500000, "150001"));
        ranges.add(new BatchRange(500001, 99999999, "500001"));
        return ranges;
    }


}
