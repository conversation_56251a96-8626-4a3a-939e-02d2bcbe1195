package cec.jiutian.bc.report8D.domain.report8D.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceGenerateType;
import cec.jiutian.view.field.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report")
@FabosJson(
        name = "8D报告D6",
        orderBy = "Report8DD6.createTime desc",
        formColumnElements = 2,
        subTableDisplayType = FabosJson.SubTableDisplayTypeEnum.INDEX
)
public class Report8DD6 extends MetaModel {
    //临时措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "临时措施", column = "tempMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "临时措施",
                    type = EditType.TAB_REFERENCE_GENERATE),
            referenceGenerateType = @ReferenceGenerateType(editable = {"verifyResult", "verifyDate", "verifyEvidence"})
    )
    private List<VerifyTempMeasure> verifyTempMeasureList;

    //长期措施
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "report_8d_id")
    @FabosJsonField(
            views = @View(title = "长期措施", column = "longMeasure", type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "长期措施",
                    type = EditType.TAB_REFERENCE_GENERATE),
                    referenceGenerateType = @ReferenceGenerateType(editable = {"verifyResult", "verifyDate", "verifyEvidence"})
    )
    private List<VerifyLongMeasure> verifyLongMeasureList;
}
