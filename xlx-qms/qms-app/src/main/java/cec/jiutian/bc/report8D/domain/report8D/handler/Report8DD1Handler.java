package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.domain.report8D.model.MemberInfo;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD1;
import cec.jiutian.bc.report8D.enums.PositionTypeEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class Report8DD1Handler implements OperationHandler<Report8D, Report8DD1> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD1 modelObject, String[] param) {
        if (CollectionUtils.isEmpty(modelObject.getMemberInfoList())) {
            return "msg.success('填写的数据为空')";
        }
        //至少且只有一个组长
        int leaderCount = 0;
        for (MemberInfo member : modelObject.getMemberInfoList()) {
            if (!PositionTypeEnum.Enum.LEADER.name().equals(member.getPosition())) {
                continue;
            }
            leaderCount++;
            if (leaderCount > 1) {
                throw new FabosJsonApiErrorTip("只能有一个组长");
            }
        }
        if (leaderCount == 0) {
            throw new FabosJsonApiErrorTip("至少有一个组长");
        }
        // 存DB
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setMemberInfoList(modelObject.getMemberInfoList());
            report8D.setStatus(Report8DStatusEnum.Enum.D2.name());
            MemberInfo leader = findLeader(modelObject.getMemberInfoList());
            report8D.setAuditPersonId(leader.getMemberId());
            report8D.setAuditPersonName(leader.getMemberName());
            fabosJsonDao.mergeAndFlush(report8D);
        }
        return "msg.success('操作成功')";
    }

    private MemberInfo findLeader(List<MemberInfo> memberInfoList) {
        for (MemberInfo memberInfo : memberInfoList) {
            if (PositionTypeEnum.Enum.LEADER.name().equals(memberInfo.getPosition())) {
                return memberInfo;
            }
        }
        return null;
    }

    @Override
    public Report8DD1 fabosJsonFormValue(List<Report8D> data, Report8DD1 fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            if (CollectionUtils.isNotEmpty(report8D.getMemberInfoList())) {
                List<MemberInfo> memberInfoList = new ArrayList<>();
                report8D.getMemberInfoList().forEach(memberInfo -> {
                    MemberInfo info = new MemberInfo();
                    BeanUtil.copyProperties(memberInfo, info);
                    UserForInsTaskMTO mto = fabosJsonDao.getById(UserForInsTaskMTO.class, memberInfo.getMemberId());
                    info.setMemberUserMTO(mto);
                    memberInfoList.add(info);
                });
                fabosJsonForm.setMemberInfoList(memberInfoList);
            }
        }
        return fabosJsonForm;
    }
}
