package cec.jiutian.bc.ppkManagement.domain.ppkPlan.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@FabosJson(
        name = "PPK计划设备详情"
)
@Table(name = "qms_ppk_plan_detail")
@Entity
@Data
public class PPKPlanEquipmentDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "PPK计划", column = "generalCode", show = false)
            },
            edit = @Edit(title = "PPK计划", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("details")
    private PPKPlan ppkPlan;

    @FabosJsonField(
            views = @View(title = "设备档案主键id", show = false),
            edit = @Edit(title = "设备档案主键id", show = false)
    )
    private String equipmentArchiveId;

    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号", readonly = @Readonly)
    )
    private String equipmentArchiveCode;

    @FabosJsonField(
            views = @View(title = "设备简称"),
            edit = @Edit(title = "设备简称", readonly = @Readonly)
    )
    private String abbreviation;

    @FabosJsonField(
            views = @View(title = "取样点"),
            edit = @Edit(title = "取样点")
    )
    private String getSamplePoint;

    @FabosJsonField(
            views = @View(title = "送样点"),
            edit = @Edit(title = "送样点")
    )
    private String sendSamplePoint;

    @FabosJsonField(
            views = @View(title = "评判项目"),
            edit = @Edit(title = "评判项目")
    )
    private String judgeItem;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量",
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0, precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "样品份数"),
            edit = @Edit(title = "样品份数",
                    inputGroup = @InputGroup(postfix = "份"),
                    numberType = @NumberType(min = 0, max = 100))
    )
    private Integer sampleCount;

    @FabosJsonField(
            views = @View(title = "取样方式"),
            edit = @Edit(title = "取样方式")
    )
    private String getSampleWay;

    @FabosJsonField(
            views = @View(title = "封装要求"),
            edit = @Edit(title = "封装要求")
    )
    private String packageRequire;

    @FabosJsonField(
            views = @View(title = "是否已生成任务", show = false),
            edit = @Edit(title = "是否已生成任务",
                    show = false,
                    defaultVal = "false",
                    type = EditType.BOOLEAN
            )
    )
    private Boolean isGenerateTask;
}
