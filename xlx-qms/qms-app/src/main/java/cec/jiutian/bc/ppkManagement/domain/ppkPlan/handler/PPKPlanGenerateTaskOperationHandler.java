package cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler;

import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.ppkManagement.domain.ppkInspectionTask.model.PPKInspectionTask;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlanEquipmentDetail;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlanGenerateTask;
import cec.jiutian.bc.ppkManagement.domain.ppkPlan.mto.PPKInspectionTaskMTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class PPKPlanGenerateTaskOperationHandler implements OperationHandler<PPKPlan, PPKPlanGenerateTask> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private NamingRuleService namingRuleService;

    @Transactional
    @Override
    public String exec(List<PPKPlan> data, PPKPlanGenerateTask modelObject, String[] param) {
        if (modelObject != null) {
            PPKInspectionTask task = new PPKInspectionTask();
            if (CollectionUtils.isNotEmpty(modelObject.getTaskMTOList())) {
                PPKInspectionTaskMTO taskMTO = modelObject.getTaskMTOList().get(0);
                BeanUtils.copyProperties(taskMTO, task);
                task.setSampleTaskCount(0);
                fabosJsonDao.mergeAndFlush(task);
                PPKPlanEquipmentDetail planDetail = fabosJsonDao.findById(PPKPlanEquipmentDetail.class, taskMTO.getPlanDetailId());
                planDetail.setIsGenerateTask(true);
                fabosJsonDao.mergeAndFlush(planDetail);
            }

        }
        return "alert('操作成功')";
    }

//    @Override
//    public PPKPlanGenerateTask fabosJsonFormValue(List<PPKPlan> data, PPKPlanGenerateTask fabosJsonForm, String[] param) {
//        PPKPlan plan = data.get(0);
//        List<PPKPlanEquipmentDetail> detailList = plan.getDetails().stream().filter(detail -> !detail.getIsGenerateTask()).toList();
//        if (CollectionUtils.isEmpty(detailList)) {
//            throw new FabosJsonApiErrorTip("当前计划所有设备都已创建检验任务");
//        } else {
//            fabosJsonForm.setGeneralCode(plan.getGeneralCode());
//        }
//        return fabosJsonForm;
//    }
}
