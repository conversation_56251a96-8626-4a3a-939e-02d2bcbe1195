package cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.model;

import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.enumration.UnqualifiedReviewTaskCommentEnum;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler.UnqualifiedReviewTaskDisposalHandler;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler.UnqualifiedReviewTaskSQEHandler;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler.UnqualifiedReviewTaskWorkmanshipHandler;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewDisposalAudit;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewSQEAudit;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewWorkmanshipAudit;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedReviewTaskPresentEnum;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.handler.UnqualifiedReviewTaskQualityHandler;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.mto.UnqualifiedReviewPublishAudit;
import cec.jiutian.bc.mrb.domain.unqualifiedReviewTask.proxy.UnqualifiedReviewTaskProxy;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedAuditTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description TODO
 */
@Getter
@Setter
@Entity
@Table(name = "qms_mrb_unqualified_review_task")
@FabosJson(
        name = "不合格评审任务",
        orderBy = "UnqualifiedReviewTask.createTime desc",
        power = @Power(add = false, edit = false, importable = false, delete = false),
        dataProxy = UnqualifiedReviewTaskProxy.class,
        rowOperation = {
                //品质审核
                @RowOperation(
                        title = "审核",
                        code = "UnqualifiedReviewTask@QUALITYAUDIT",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        fabosJsonClass = UnqualifiedReviewPublishAudit.class,
                        operationHandler = UnqualifiedReviewTaskQualityHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "selectedItems[0].unqualifiedAuditType != 'QUALITY' || selectedItems[0].businessStatus != 'NOT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "UnqualifiedReviewTask@QUALITYAUDIT"
                        )
                ),
                //工艺技术
                @RowOperation(
                        title = "审核",
                        code = "UnqualifiedReviewTask@WORKMANSHIP",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        fabosJsonClass = UnqualifiedReviewWorkmanshipAudit.class,
                        operationHandler = UnqualifiedReviewTaskWorkmanshipHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "selectedItems[0].unqualifiedAuditType != 'WORKMANSHIP' || selectedItems[0].businessStatus != 'NOT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "UnqualifiedReviewTask@WORKMANSHIP"
                        )
                ),
                //SQE
                @RowOperation(
                        title = "审核",
                        code = "UnqualifiedReviewTask@SQE",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        fabosJsonClass = UnqualifiedReviewSQEAudit.class,
                        operationHandler = UnqualifiedReviewTaskSQEHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "selectedItems[0].unqualifiedAuditType != 'SQE' || selectedItems[0].businessStatus != 'NOT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "UnqualifiedReviewTask@SQE"
                        )
                ),
                //处置审核
                @RowOperation(
                        title = "审核",
                        code = "UnqualifiedReviewTask@DISPOSAL",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        fabosJsonClass = UnqualifiedReviewDisposalAudit.class,
                        operationHandler = UnqualifiedReviewTaskDisposalHandler.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        ifExpr = "(selectedItems[0].unqualifiedAuditType != 'QUALITY_DEPART' && selectedItems[0].unqualifiedAuditType != 'RESPONSE_LEADER' && selectedItems[0].unqualifiedAuditType != 'GENERAL_MANAGER') || selectedItems[0].businessStatus != 'NOT_FINISH'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "UnqualifiedReviewTask@DISPOSAL"
                        )
                ),
        }
)
public class UnqualifiedReviewTask extends MetaModel {
    @FabosJsonField(
            views = @View(title = "任务单号"),
            edit = @Edit(title = "任务单号",
                    search = @Search(vague = true))
    )
    private String generalCode;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "不合格评审单号", column = "generalCode"),
            edit = @Edit(title = "不合格评审单号",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"),
                    readonly = @Readonly()
            )
    )
    @JoinColumn(name = "unqualified_review_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private MRBUnqualifiedReview unqualifiedReview;

    @FabosJsonField(
            views = @View(title = "评审人ID", show = false),
            edit = @Edit(title = "评审人ID", show = false)
    )
    private String reviewerId;

    @FabosJsonField(
            views = @View(title = "评审人"),
            edit = @Edit(title = "评审人", show = false)
    )
    private String reviewerName;

    @FabosJsonField(
            views = @View(title = "评审意见"),
            edit = @Edit(title = "评审意见", notNull = true, type = EditType.CHOICE,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedReviewTaskCommentEnum.class))
    )
    private String reviewComment;

    @FabosJsonField(
            views = @View(title = "意见说明"),
            edit = @Edit(title = "意见说明",
                    search = @Search(vague = true),
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String commentDescription;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(maxLimit = 10))
    )
    private String attachment;

    @FabosJsonField(
            views = @View(title = "不合格审核类型"),
            edit = @Edit(title = "不合格审核类型",
                    show = false,
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedAuditTypeEnum.class)
            )
    )
    private String unqualifiedAuditType;

    @FabosJsonField(
            views = @View(title = "审核完成时间"),
            edit = @Edit(title = "审核完成时间", dateType = @DateType(type = DateType.Type.DATE_TIME))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date time;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    show = false,
                    notNull = true,
                    type = EditType.CHOICE,
                    defaultVal = "NOT_FINISH",
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = UnqualifiedReviewTaskPresentEnum.class)
            )
    )
    private String businessStatus;
}
