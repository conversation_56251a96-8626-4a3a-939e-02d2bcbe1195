package cec.jiutian.bc.basicData.domain.inspectionStandard.handler;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandard;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InspectionStandardInValidOperationHandler implements OperationHandler<InspectionStandard, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<InspectionStandard> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            InspectionStandard entity = data.get(0);
            entity.setStatus(StatusEnum.Enum.Invalid.name());
            fabosJsonDao.mergeAndFlush(entity);
        }
        return "msg.success('操作成功')";
    }
}
