package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.model;

import cec.jiutian.bc.modeler.enumration.SampleStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@FabosJson(
        name = "CMK检验任务详情"
)
@Table(name = "qms_cmk_inspection_task_detail")
@Entity
@Data
public class CMKInspectionTaskDetail extends MetaModel {

    @ManyToOne
    @FabosJsonField(
            views = {
                    @View(title = "CMK检验任务", column = "generalCode", show = false)
            },
            edit = @Edit(title = "CMK检验任务", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @JsonIgnoreProperties("detailList")
    private CMKInspectionTask cmkInspectionTask;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SampleStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "实测值"),
            edit = @Edit(title = "实测值")
    )
    private String inspectValue;

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号",readonly = @Readonly)
    )
    private String sampleTaskCode;

    @FabosJsonField(
            views = @View(title = "实际取样流水号"),
            edit = @Edit(title = "实际取样流水号",readonly = @Readonly)
    )
    private String actualSerialNumber;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "取样时间", type = ViewType.DATE),
            edit = @Edit(title = "取样时间", readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date getSampleDate;
}
