package cec.jiutian.bc.basicData.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/10
 */
@Data
public class MsSipResultDTO {

    private static final long serialVersionUID = 7879262570261663909L;

    @ApiModelProperty(name = "SIP GID", notes = "SIP GID")
    private String gid;

    @ApiModelProperty(name = "SIP Name", notes = "SIP名称")
    private String sipName;

    @ApiModelProperty(name = "SIP Version", notes = "SIP版本")
    private String sipVersion;

    @ApiModelProperty(name = "specification Detail", notes = "产品型号")
    private String specificationDetail;

    @ApiModelProperty(name = "Specification Code", notes = "产品编码")
    private String specificationCode;

    @ApiModelProperty(name = "Specification Name", notes = "产品名称")
    private String specificationName;

    @ApiModelProperty(name = "workshopId", notes = "生产车间ID")
    private Long workshopId;

    @ApiModelProperty(name = "workshop Name", notes = "生产车间名称")
    private String workshopName;

    @ApiModelProperty(name = "production Line Id", notes = "产线ID")
    private Long productionLineId;

    @ApiModelProperty(name = "Production Line Name", notes = "产线名称")
    private String productionLineName;

    @ApiModelProperty(name = "Process Code", notes = "工艺编码")
    private String processCode;

    @ApiModelProperty(name = "Process Name", notes = "工艺名称")
    private String processName;

    @ApiModelProperty(name = "Current State", notes = "当前状态")
    private String currentState;

    private List<MsSipDetailResultDTO> detailList;
}
