package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.domain.report8D.model.*;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class Report8DD5Handler implements OperationHandler<Report8D, Report8DD5> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD5 modelObject, String[] param) {
        if (CollectionUtils.isEmpty(modelObject.getD5LongMeasureList())) {
            return "msg.success('填写的数据为空')";
        }
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D5_AUDIT.name() : report8D.getStatus());
            report8DSetLongMeasureListAndUserIds(report8D, modelObject.getD5LongMeasureList());
            report8D.setOutflowMeasure(modelObject.getOutflowMeasure());
            fabosJsonDao.mergeAndFlush(report8D);

            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }
        return "msg.success('操作成功')";

    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    private void report8DSetLongMeasureListAndUserIds(Report8D report8D, List<D5LongMeasure> d5LongMeasureList) {
        List<LongMeasure> longMeasureList = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>();
        d5LongMeasureList.forEach(d -> {
            LongMeasure longMeasure = new LongMeasure();
            BeanUtil.copyProperties(d, longMeasure);
            if (Objects.isNull(longMeasure.getProgress())) {
                longMeasure.setProgress(MeasureProgressEnum.Enum.WAIT_EXECUTE.name());
            }
            longMeasureList.add(longMeasure);
            userIdSet.add(d.getResponsiblePersonId());
        });
        report8D.setLongMeasureList(longMeasureList);

        ArrayList<String> longUsrIds = new ArrayList<>(userIdSet);
        report8D.setLongUserIds(String.join(",", longUsrIds));
    }

    @Override
    public Report8DD5 fabosJsonFormValue(List<Report8D> data, Report8DD5 fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            if (CollectionUtils.isNotEmpty(report8D.getLongMeasureList())) {
                List<D5LongMeasure> longMeasureList = new ArrayList<>();
                report8D.getLongMeasureList().forEach(d -> {
                    D5LongMeasure d5LongMeasure = new D5LongMeasure();
                    BeanUtil.copyProperties(d, d5LongMeasure);
                    UserForInsTaskMTO mto = fabosJsonDao.getById(UserForInsTaskMTO.class, d.getResponsiblePersonId());
                    d5LongMeasure.setResponsibleUserMTO(mto);
                    longMeasureList.add(d5LongMeasure);
                });
                fabosJsonForm.setD5LongMeasureList(longMeasureList);
            }
        }
        return fabosJsonForm;
    }
}
