package cec.jiutian.bc.cmkManagement.domain.cmkInspectionTask.mto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "CMK检验任务录入结果详情MTO"
)
public class EnterResultOprDetailMTO extends BaseModel {

    @FabosJsonField(
            views = @View(title = "取样任务单号"),
            edit = @Edit(title = "取样任务单号",readonly = @Readonly)
    )
    private String sampleTaskCode;

    @FabosJsonField(
            views = @View(title = "实测值"),
            edit = @Edit(title = "实测值")
    )
    private String inspectValue;
}
