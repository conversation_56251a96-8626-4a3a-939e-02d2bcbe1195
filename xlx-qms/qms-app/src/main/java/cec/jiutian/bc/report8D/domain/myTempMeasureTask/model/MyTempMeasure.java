package cec.jiutian.bc.report8D.domain.myTempMeasureTask.model;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.enums.MeasureCompleteProgressEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "qms_8d_report_temp_measure")
@FabosJson(
        name = "临时措施",
        orderBy = "MyTempMeasure.createTime desc"
)
public class MyTempMeasure extends MetaModel {
    // 临时措施
    @FabosJsonField(
            views = @View(title = "临时措施"),
            edit = @Edit(
                    title = "临时措施",
                    inputType = @InputType(length = 50),
                    readonly = @Readonly()
            )
    )
    @Column(length = 50)
    private String tempMeasure;

    // 责任人
    @Transient
    @FabosJsonField(
            views = @View(title = "责任人", column = "name", show = false),
            edit = @Edit(
                    title = "责任人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    show = false,
                    referenceTableType = @ReferenceTableType()
            )
    )
    private UserForInsTaskMTO responsibleUserMTO;

    @FabosJsonField(
            views = @View(title = "责任人"),
            edit = @Edit(
                    title = "责任人",
                    inputType = @InputType(length = 20),
                    readonly = @Readonly()
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleUserMTO", beFilledBy = "name"))
    )
    @Column(length = 20)
    private String responsiblePersonName;

    @FabosJsonField(
            views = @View(title = "责任人ID", show = false),
            edit = @Edit(
                    show = false,
                    title = "责任人ID",
                    inputType = @InputType(length = 50)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "responsibleUserMTO", beFilledBy = "id"))
    )
    @Column(length = 50)
    private String responsiblePersonId;

    // 纳期
    @FabosJsonField(
            views = @View(title = "纳期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "纳期",
                    notNull = true,
                    readonly = @Readonly(),
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "due_time", nullable = false)
    private Date dueTime;

    //改善进度
    @FabosJsonField(
            views = @View(title = "是否完成"),
            edit = @Edit(title = "是否完成",
                    inputType = @InputType(length = 20),
                    defaultVal = "WAIT_EXECUTE",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = MeasureCompleteProgressEnum.class))
    )
    @Column(length = 20)
    private String progress;
    //完成日期
    @FabosJsonField(
            views = @View(title = "完成日期", type = ViewType.DATE_TIME),
            edit = @Edit(
                    title = "完成日期",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date completionDate;
    //完成佐证材料
    @FabosJsonField(
            views = @View(title = "完成佐证材料"),
            edit = @Edit(title = "完成佐证材料",
                    inputType = @InputType(length = 300),
                    type = EditType.ATTACHMENT,
                    tips = "最大支持100M文件",
                    attachmentType = @AttachmentType(
                            size = 100 * 1024,
                            maxLimit = 1,
                            type = AttachmentType.Type.BASE)
            )
    )
    @Column(length = 300)
    private String completionEvidence;
}
