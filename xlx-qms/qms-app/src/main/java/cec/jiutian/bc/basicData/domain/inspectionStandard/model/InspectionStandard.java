package cec.jiutian.bc.basicData.domain.inspectionStandard.model;

import cec.jiutian.bc.basicData.domain.inspectionItemGroup.model.InspectionItemGroup;
import cec.jiutian.bc.basicData.domain.inspectionStandard.handler.InspectionStandardInValidOperationHandler;
import cec.jiutian.bc.basicData.domain.inspectionStandard.handler.InspectionStandardValidOperationHandler;
import cec.jiutian.bc.basicData.domain.inspectionStandard.proxy.InspectionStandardProxy;
import cec.jiutian.bc.basicData.enumeration.IQCStandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.NamingRuleCodeEnum;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.bc.modeler.domain.dto.SendPointMTO;
import cec.jiutian.bc.modeler.enumration.SendPointTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.RowOperationReadonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "质检标准",
        orderBy = "InspectionStandard.createTime desc",
        dataProxy = InspectionStandardProxy.class,
        power = @Power(add = false, edit = false),
        rowOperation = {
                @RowOperation(
                        title = "来料质检标准",
                        code = "InspectionStandard@IQCCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardIQC.class,
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@IQCCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "InspectionStandard@IQCUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardIQC.class,
                        ifExpr = "type !='IQC' || status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@IQCUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "过程质检标准",
                        code = "InspectionStandard@IPQCCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardIPQC.class,
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@IPQCCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "InspectionStandard@IPQCUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardIPQC.class,
                        ifExpr = "type !='IPQC' || status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@IPQCUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "发货质检标准",
                        code = "InspectionStandard@OQCCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardOQC.class,
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@OQCCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "InspectionStandard@OQCUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardOQC.class,
                        ifExpr = "type !='OQC' || status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@OQCUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "其他质检标准",
                        code = "InspectionStandard@OTHERCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardOther.class,
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@OTHERCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "InspectionStandard@OTHERUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardOther.class,
                        ifExpr = "type !='Other' || status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@OTHERUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "实验室质检标准",
                        code = "InspectionStandard@LIMSCREATE",
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.ADD,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardLims.class,
                        readonly = @RowOperationReadonly(readOnlyAffectMode = RowOperationReadonly.AffectMode.add),
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@LIMSCREATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "编辑",
                        code = "InspectionStandard@LIMSUPDATE",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.UPDATE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = InspectionStandardLims.class,
                        ifExpr = "type !='LIMS' || status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@LIMSUPDATE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        title = "生效",
                        code = "InspectionStandard@EFFECTIVE",
                        operationHandler = InspectionStandardValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@EFFECTIVE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status != 'Invalid'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
                @RowOperation(
                        title = "失效",
                        code = "InspectionStandard@INVALID",
                        operationHandler = InspectionStandardInValidOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "InspectionStandard@INVALID"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "status != 'Effective'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                )
        }
)
@Table(name = "bd_inspection_standard",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class InspectionStandard extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.InspectionStandard.name();
    }

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "适用检验类型"),
            edit = @Edit(title = "适用检验类型", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StandardTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "IQC子类型"),
            edit = @Edit(title = "IQC子类型", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = IQCStandardTypeEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IQC'"))
    )
    private String IQCSubType;

//    @Transient
//    @FabosJsonField(
//            views = @View(title = "物料/产品", column = "name", show = false),
//            edit = @Edit(title = "物料/产品", show = false,
//                    type = EditType.REFERENCE_TABLE,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
//            )
//    )
//    //@ManyToOne
//    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "物料id", show = false),
            edit = @Edit(title = "物料id", show = false)
    )
    private String specificationId;

    @FabosJsonField(
            views = @View(title = "工艺流程id", show = false),
            edit = @Edit(title = "工艺流程id", show = false)
    )
    private Long technologyFlowId;

    @FabosJsonField(
            views = @View(title = "物料/产品编码"),
            edit = @Edit(title = "物料/产品编码", readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "IQCSubType != 'Usual'"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料/产品名称"),
            edit = @Edit(title = "物料/产品名称", readonly = @Readonly, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "IQCSubType != 'Usual'"))
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "工艺编码"),
            edit = @Edit(title = "工艺编码", readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IPQC'"))
    )
    private String processCode;

    @FabosJsonField(
            views = @View(title = "工艺名称"),
            edit = @Edit(title = "工艺名称", readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IPQC'"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "车间ID", show = false),
            edit = @Edit(title = "车间ID", show = false)
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称", readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IPQC'"))
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id", show = false),
            edit = @Edit(title = "产线Id", show = false)
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称", readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IPQC'"))
    )
    private String factoryLineName;

//    @FabosJsonField(
//            views = @View(title = "过程实验室Id", show = false),
//            edit = @Edit(title = "过程实验室Id", show = false)
//    )
//    private String labId;
//
//    @FabosJsonField(
//            views = @View(title = "过程实验室"),
//            edit = @Edit(title = "过程实验室", readonly = @Readonly,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'IPQC'"))
//    )
//    private String labName;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述", type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "是否留样"),
            edit = @Edit(title = "是否留样", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String keepSampleFlag;

    @FabosJsonField(
            views = @View(title = "留样量"),
            edit = @Edit(title = "留样量",
                    dependFieldDisplay = @DependFieldDisplay(
                            notNull = "keepSampleFlag == 'Y'",
                            showOrHide = "keepSampleFlag == 'Y'"))
    )
    private Double sampleQuantity;

    @FabosJsonField(
            views = @View(title = "样品单位id", show = false),
            edit = @Edit(title = "样品单位id", show = false)
    )
    private String sampleUnitId;

    @FabosJsonField(
            views = @View(title = "样品单位"),
            edit = @Edit(title = "样品单位")
    )
    private String sampleUnit;

    @FabosJsonField(
            views = @View(title = "留样取样点"),
            edit = @Edit(title = "留样取样点",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'")
            )
    )
    @Column(name = "sample_point", length = 50)
    private String samplePoint;

    @FabosJsonField(
            views = @View(title = "送检点类型"),
            edit = @Edit(title = "送检点类型",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SendPointTypeEnum.class),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'"))
    )
    private String sendPointType;

    @FabosJsonField(
            views = @View(title = "留样送检点", column = "name"),
            edit = @Edit(title = "留样送检点", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"type\":\"${sendPointType}\"}",
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "keepSampleFlag == 'Y'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "send_point_mto_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SendPointMTO sendPointMTO;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = StatusEnum.class))
    )
    private String status;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "检验项目组（实验室使用）", column = "groupName", show = false),
            edit = @Edit(title = "检验项目组",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    filter = @Filter(value = "status = 'ACTIVE'")
            )
    )
    private InspectionItemGroup inspectionItemGroup;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "inspection_standard_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "质检标准详情", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "质检标准详情", type = ViewType.TABLE_VIEW)
    )
    private List<InspectionStandardDetail> details;

}
