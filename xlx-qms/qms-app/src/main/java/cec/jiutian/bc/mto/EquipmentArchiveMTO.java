package cec.jiutian.bc.mto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@FabosJson(
        name = "EAM设备台账MTO"
)
@Entity
@Getter
@Setter
@Table(name = "eam_ea_equipment_archive_eam")
public class EquipmentArchiveMTO extends MetaModel {
    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号",search = @Search, notNull = true)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "设备编码",show = false),
            edit = @Edit(title = "设备编码", show = false,search = @Search)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称", readonly = @Readonly)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "设备简称"),
            edit = @Edit(title = "设备简称", readonly = @Readonly)
    )
    private String abbreviation;

    @FabosJsonField(
            views = @View(title = "设备状态",show = false),
            edit = @Edit(title = "设备状态",show = false, type = EditType.CHOICE,search = @Search())
    )
    private String businessState;
}
