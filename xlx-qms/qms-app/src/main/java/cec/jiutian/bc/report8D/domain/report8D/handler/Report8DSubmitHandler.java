package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class Report8DSubmitHandler implements OperationHandler<Report8D, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setStatus(Report8DStatusEnum.Enum.D1.name());
            fabosJsonDao.mergeAndFlush(report8D);
        }
        return "msg.success('操作成功')";
    }
}
