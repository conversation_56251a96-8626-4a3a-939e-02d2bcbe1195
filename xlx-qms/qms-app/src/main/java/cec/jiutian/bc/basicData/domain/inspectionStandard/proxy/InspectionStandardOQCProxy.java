package cec.jiutian.bc.basicData.domain.inspectionStandard.proxy;

import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardDetail;
import cec.jiutian.bc.basicData.domain.inspectionStandard.model.InspectionStandardOQC;
import cec.jiutian.bc.basicData.enumeration.StandardTypeEnum;
import cec.jiutian.bc.basicData.enumeration.StatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class InspectionStandardOQCProxy implements DataProxy<InspectionStandardOQC> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public void beforeAdd(InspectionStandardOQC entity) {
        // 校验名称
        InspectionStandardOQC condition = new InspectionStandardOQC();
        condition.setName(entity.getName());
        InspectionStandardOQC data = fabosJsonDao.selectOne(condition);
        if (null != data) {
            throw new FabosJsonApiErrorTip("名称不可重复，请确认");
        }
        checkNotNull(entity.getDetails());

        entity.setStatus(StatusEnum.Enum.Invalid.name());
        entity.setType(StandardTypeEnum.Enum.OQC.name());
        List<InspectionStandardDetail> details = entity.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {
            for (InspectionStandardDetail detail : details) {
                detail.setOperationCode("FH");
                detail.setOperationName("发货");
                bindInsItermAndGroup(detail);
            }
        }
    }

    private void checkNotNull(List<InspectionStandardDetail> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            for (InspectionStandardDetail detail : details) {
                if (null == detail.getInspectionItem()) {
                    throw new FabosJsonApiErrorTip("详情中检验项目必填，请确认");
                }
            }
        }
    }

    @Override
    @Transactional
    public void beforeUpdate(InspectionStandardOQC entity) {
        if (!entity.getStatus().equals(StatusEnum.Enum.Invalid.name())) {
            throw new FabosJsonApiErrorTip("失效状态允许编辑");
        }
        checkNotNull(entity.getDetails());

        List<InspectionStandardDetail> details = entity.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {
            for (InspectionStandardDetail detail : details) {
                detail.setOperationCode("FH");
                detail.setOperationName("发货");
                bindInsItermAndGroup(detail);
            }
        }

    }


    private void bindInsItermAndGroup(InspectionStandardDetail detail) {
//        if (detail == null) {
//            return;
//        }
//        InspectionItem item = fabosJsonDao.findById(InspectionItem.class, detail.getInspectionItem().getId());
//        InspectionItemGroup itemGroup = fabosJsonDao.findById(InspectionItemGroup.class, detail.getInspectionItemGroup().getId());
//        if (item == null) return;
//        if (itemGroup == null) return;
//
//        item.setGroupId(InspectionItem.addGroupId(item.getGroupId(), itemGroup.getId()));
//        itemGroup.addItem(item);
//        fabosJsonDao.merge(item);
//        fabosJsonDao.merge(itemGroup);

    }

}
