package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto.CMKSampleTaskGetMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class CMKGetSampleOperationHandler implements OperationHandler<CMKSampleTask, CMKSampleTaskGetMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKSampleTask> data, CMKSampleTaskGetMTO modelObject, String[] param) {
        if (modelObject != null) {
            CMKSampleTask condition = new CMKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            CMKSampleTask samplingTask = fabosJsonDao.selectOne(condition);
            samplingTask.setBusinessState(TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name());
            samplingTask.setGetSampleDate(new Date());
            fabosJsonDao.mergeAndFlush(samplingTask);
        }
        return "alert(操作成功)";
    }

    @Override
    public CMKSampleTaskGetMTO fabosJsonFormValue(List<CMKSampleTask> data, CMKSampleTaskGetMTO fabosJsonForm, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            CMKSampleTask samplingTask = data.get(0);
            fabosJsonForm.setGeneralCode(samplingTask.getGeneralCode());
            fabosJsonForm.setCmkInspectionTaskCode(samplingTask.getCmkInspectionTaskCode());
            fabosJsonForm.setProcessName(samplingTask.getProductProcessMTO().getName());
            fabosJsonForm.setMaterialCode(samplingTask.getMaterialCode());
            fabosJsonForm.setMaterialName(samplingTask.getMaterialName());
            fabosJsonForm.setSampleWeight(samplingTask.getSampleWeight());
        }
        return fabosJsonForm;
    }
}
