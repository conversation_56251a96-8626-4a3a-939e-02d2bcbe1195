package cec.jiutian.bc.cmkManagement.domain.cmkPlan.model;

import cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler.CMKDetailReferenceAddHandler;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler.CMKPlanCloseOperationHandler;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler.CMKPlanGenerateTaskOperationHandler;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.handler.CMKPlanPublishOperationHandler;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto.GenerateTaskMTO;
import cec.jiutian.bc.cmkManagement.domain.cmkPlan.proxy.CMKPlanProxy;
import cec.jiutian.bc.cmkManagement.enumeration.GatherTimingTypeEnum;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.modeler.enumration.NamingRuleCodeEnum;
import cec.jiutian.bc.mto.FactoryArea;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26
 * @description TODO
 */
@FabosJson(
        name = "CMK计划",
        orderBy = "CMKPlan.createTime desc",
        dataProxy = CMKPlanProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "CMKPlan@PUBLISH",
                        operationHandler = CMKPlanPublishOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "CMKPlan@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "任务生成",
                        code = "CMKPlan@TASK",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = GenerateTaskMTO.class,
                        operationHandler = CMKPlanGenerateTaskOperationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "CMKPlan@TASK"
                        ),
                        ifExpr = "selectedItems[0].businessState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "关闭",
                        code = "CMKPlan@CLOSE",
                        operationHandler = CMKPlanCloseOperationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "CMKPlan@CLOSE"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EXECUTE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.HIDE
                ),
        }
)
@Table(name = "qms_cmk_plan",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class CMKPlan extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.CMKPlan.name();
    }

    @Transient
    @FabosJsonField(
            views = @View(title = "车间", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "车间",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    filter = @Filter(value = "factoryAreaTypeCode = '02'")
            )
    )
    private FactoryArea factoryArea;

    @FabosJsonField(
            views = @View(title = "车间ID",show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "id"))
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryArea", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @Transient
    @FabosJsonField(
            views = @View(title = "产线", show = false, column = "factoryAreaName"),
            edit = @Edit(title = "产线",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "factoryAreaName"),
                    queryCondition = "{\"pid\": \"${factoryArea.id}\"}",
                    filter = @Filter(value = "factoryAreaTypeCode = '03'")
            )
    )
    private FactoryArea factoryLine;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "id"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true,edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "factoryLine", beFilledBy = "factoryAreaName"))
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "产品", column = "name"),
            edit = @Edit(title = "产品",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '02%'"),
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    @ManyToOne
    @JoinColumn(name = "specification_manage_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SpecificationManageMTO material;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code"))
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格"),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "type"))
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "实施时间"),
            edit = @Edit(title = "实施时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyTime;

    @FabosJsonField(
            views = @View(title = "采集时机"),
            edit = @Edit(title = "采集时机",type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = GatherTimingTypeEnum.class))
    )
    private String gatherTiming;

    @FabosJsonField(
            views = @View(title = "其他采集时机"),
            edit = @Edit(title = "其他采集时机",notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "gatherTiming == 'Other'"))
    )
    private String otherGatherTiming;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "EDIT",
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "cmk_plan_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "CMK计划设备详情", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "EquipmentArchiveMTO",
                    //filter = "businessState = 'normal'",
                    editable = {"getSamplePoint","sendSamplePoint","judgeItem","sampleWeight","sampleCount","getSampleWay","packageRequire"},
                    referenceAddHandler = CMKDetailReferenceAddHandler.class),
            views = @View(title = "CMK计划设备详情", type = ViewType.TABLE_VIEW, extraPK = "equipmentArchiveId")
    )
    private List<CMKPlanEquipmentDetail> detailList;
}
