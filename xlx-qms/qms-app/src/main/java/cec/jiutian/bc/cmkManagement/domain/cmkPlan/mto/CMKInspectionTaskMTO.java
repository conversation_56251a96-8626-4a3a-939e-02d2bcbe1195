package cec.jiutian.bc.cmkManagement.domain.cmkPlan.mto;

import cec.jiutian.bc.cmkManagement.enumeration.GatherTimingTypeEnum;
import cec.jiutian.bc.cmkManagement.enumeration.SpecificationCustomerRequireEnum;
import cec.jiutian.bc.cmkManagement.enumeration.ToleranceRequireEnum;
import cec.jiutian.bc.mto.ProductProcessMTO;
import cec.jiutian.bc.mto.SpecificationManageMTO;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description TODO
 */
@Entity
@Data
@FabosJson(
        name = "CMK检验任务MTO"
)
public class CMKInspectionTaskMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "cmk检验单号",show = false),
            edit = @Edit(title = "cmk检验单号",show = false)
    )
    private String generalCode;

    @FabosJsonField(
            views = @View(title = "cmk计划单号"),
            edit = @Edit(title = "cmk计划单号",readonly = @Readonly)
    )
    private String cmkPlanCode;

    @FabosJsonField(
            views = @View(title = "车间ID",show = false),
            edit = @Edit(title = "车间ID",
                    show = false
            )
    )
    private String factoryAreaId;

    @FabosJsonField(
            views = @View(title = "车间名称"),
            edit = @Edit(title = "车间名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            )
    )
    private String factoryAreaName;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            )
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true,edit = true)
            )
    )
    private String factoryLineName;

    @FabosJsonField(
            views = @View(title = "工序", column = "name"),
            edit = @Edit(title = "工序",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "product_process_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ProductProcessMTO productProcessMTO;

    @FabosJsonField(
            views = @View(title = "产品编码"),
            edit = @Edit(title = "产品编码", readonly = @Readonly)
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "产品名称"),
            edit = @Edit(title = "产品名称", readonly = @Readonly)
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格",readonly = @Readonly)
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号", readonly = @Readonly)
    )
    private String equipmentArchiveCode;

    @FabosJsonField(
            views = @View(title = "设备简称"),
            edit = @Edit(title = "设备简称", readonly = @Readonly)
    )
    private String abbreviation;

    @FabosJsonField(
            views = @View(title = "评判项目"),
            edit = @Edit(title = "评判项目",readonly = @Readonly)
    )
    private String judgeItem;

    @FabosJsonField(
            views = @View(title = "采集时机"),
            edit = @Edit(title = "采集时机",readonly = @Readonly,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = GatherTimingTypeEnum.class))
    )
    private String gatherTiming;

    @FabosJsonField(
            views = @View(title = "其他采集时机"),
            edit = @Edit(title = "其他采集时机",notNull = true,readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "gatherTiming == 'Other'"))
    )
    private String otherGatherTiming;

    @FabosJsonField(
            views = @View(title = "规格客户要求/特殊要求Cmk限"),
            edit = @Edit(title = "规格客户要求/特殊要求Cmk限",notNull = true,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = SpecificationCustomerRequireEnum.class))
    )
    private String specificationCustomerRequire;

    @FabosJsonField(
            views = @View(title = "公差要求"),
            edit = @Edit(title = "公差要求",notNull = true,type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = ToleranceRequireEnum.class))
    )
    private String toleranceRequire;

    @FabosJsonField(
            views = @View(title = "规格中心"),
            edit = @Edit(title = "规格中心",notNull = true,defaultVal = "/",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "toleranceRequire != 'Bilateral'"))
    )
    private String specificationCenter;

    @FabosJsonField(
            views = @View(title = "规格上限"),
            edit = @Edit(title = "规格上限",notNull = true,defaultVal = "/",
                    dependFieldDisplay = @DependFieldDisplay(notNull = "toleranceRequire == 'MinUnilateral'"))
    )
    private String specificationUpperLimit;

    @FabosJsonField(
            views = @View(title = "规格下限"),
            edit = @Edit(title = "规格下限",
                    defaultVal = "/",
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "toleranceRequire == 'MaxUnilateral'"))
    )
    private String specificationLowerLimit;

    @FabosJsonField(
            views = @View(title = "公差范围"),
            edit = @Edit(title = "公差范围",notNull = true,defaultVal = "/",
                    inputGroup = @InputGroup(prefix = "±"),
                    numberType = @NumberType(min = 0,max = 100),
                    dependFieldDisplay = @DependFieldDisplay(enableOrDisable = "toleranceRequire != 'Bilateral'"))
    )
    private String toleranceRange;

    @FabosJsonField(
            views = @View(title = "样品重量"),
            edit = @Edit(title = "样品重量",readonly = @Readonly,
                    inputGroup = @InputGroup(postfix = "g"),
                    numberType = @NumberType(min = 0,precision = 2))
    )
    private Double sampleWeight;

    @FabosJsonField(
            views = @View(title = "选择计量仪器", show = false, column = "code"),
            edit = @Edit(title = "选择计量仪器",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "code"),
                    filter = @Filter(value = "validateFlag = 'Y' and specificationCode like '01%'"))
    )
    @ManyToOne
    @JoinColumn(name = "specification_manage_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private SpecificationManageMTO specificationManage;

    @FabosJsonField(
            views = @View(title = "仪器编号"),
            edit = @Edit(title = "仪器编号",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "specificationManage", beFilledBy = "name"))

    )
    private String instrumentName;

    @FabosJsonField(
            views = @View(title = "量程"),
            edit = @Edit(title = "量程")
    )
    private String instrumentRange;

    @FabosJsonField(
            views = @View(title = "精度"),
            edit = @Edit(title = "精度")
    )
    private String precision;

    @FabosJsonField(
            views = @View(title = "样品份数",show = false),
            edit = @Edit(title = "样品份数",show = false,
                    inputGroup = @InputGroup(postfix = "份"),
                    numberType = @NumberType(min = 0,max = 100))
    )
    private Integer sampleCount;

    @FabosJsonField(
            views = @View(title = "已创建取样任务数",show = false),
            edit = @Edit(title = "已创建取样任务数",show = false)
    )
    private Integer sampleTaskCount;

    @FabosJsonField(
            views = @View(title = "取样点",show = false),
            edit = @Edit(title = "取样点",show = false)
    )
    private String getSamplePoint;

    @FabosJsonField(
            views = @View(title = "送样点",show = false),
            edit = @Edit(title = "送样点",show = false)
    )
    private String sendSamplePoint;

    @FabosJsonField(
            views = @View(title = "取样方式",show = false),
            edit = @Edit(title = "取样方式",show = false)
    )
    private String getSampleWay;

    @FabosJsonField(
            views = @View(title = "封装要求",show = false),
            edit = @Edit(title = "封装要求",show = false)
    )
    private String packageRequire;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "EDIT",
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @FabosJsonField(
            views = @View(title = "计划详情id",show = false),
            edit = @Edit(title = "计划详情id",show = false)
    )
    private String planDetailId;
}
