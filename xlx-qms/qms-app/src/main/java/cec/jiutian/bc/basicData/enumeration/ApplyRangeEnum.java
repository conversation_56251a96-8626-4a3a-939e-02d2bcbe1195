package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description TODO
 */
public class ApplyRangeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        processInspect("过程检验"),
        materialInspect("来料检验"),
        deliveryInspect("发货检验"),
        inventoryInspect("库存检验"),
        otherInspect("其他检验"),
        productReturnInspect("成品退货检验"),
        ProductExamineInspect("产品审核检验"),
        keepSample("留样"),
        surplusSample("余样"),
        Other("其他"),
        ;

        private final String value;

    }
}
