package cec.jiutian.bc.basicData.domain.reportTemplate.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Entity
@Table(name = "ins_report_template_show_filed")
@FabosJson(
        name = "模板显示字段",
        orderBy = "createTime desc",
        power = @Power(importable = false, print = false)
)
public class ShowField extends MetaModel {


    @FabosJsonField(
            views = @View(title = "字段名称"),
            edit = @Edit(title = "字段名称",
                    search = @Search(vague = true),
                    notNull = true
            )
    )
    private String simpleName;

    @FabosJsonField(
            views = @View(title = "字段中文名称"),
            edit = @Edit(title = "字段中文名称",
                    search = @Search(vague = true),
                    notNull = true
            )
    )
    private String filedCnName;

    @FabosJsonField(
            views = @View(title = "元数据id",show = false),
            edit = @Edit(title = "元数据id",
                    show = false
            )
    )
    private String metaFieldId;


}
