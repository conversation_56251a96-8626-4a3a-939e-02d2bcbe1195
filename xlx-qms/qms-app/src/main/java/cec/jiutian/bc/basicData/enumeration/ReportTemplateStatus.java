package cec.jiutian.bc.basicData.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum ReportTemplateStatus {
    CREATED("创建"),
    EDIT("编辑"),
    ACTIVE("生效"),
    INACTIVE("失效");

    private String value;

    ReportTemplateStatus(String value) {
        this.value = value;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(ReportTemplateStatus.values()).map(reportTemplateStatus ->
                    new VLModel(reportTemplateStatus.name(), reportTemplateStatus.getValue())).collect(Collectors.toList());
        }

    }
}