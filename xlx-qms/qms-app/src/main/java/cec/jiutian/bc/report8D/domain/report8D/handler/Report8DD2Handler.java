package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.AuditTask;
import cec.jiutian.bc.report8D.domain.report8D.model.MemberInfo;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD2;
import cec.jiutian.bc.report8D.enums.AuditTaskStatusEnum;
import cec.jiutian.bc.report8D.enums.PositionTypeEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class Report8DD2Handler implements OperationHandler<Report8D, Report8DD2> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD2 modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setProblemDescription(modelObject.getProblemDescription());
            report8D.setProblemAttachment(modelObject.getProblemAttachment());
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D2_AUDIT.name() : report8D.getStatus());
            report8D.setD2CreateBy(UserContext.getUserName());
            fabosJsonDao.mergeAndFlush(report8D);

            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }

        return "msg.success('操作成功')";
    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    @Override
    public Report8DD2 fabosJsonFormValue(List<Report8D> data, Report8DD2 fabosJsonForm, String[] param) {

        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            fabosJsonForm.setProblemDescription(report8D.getProblemDescription());
            fabosJsonForm.setProblemAttachment(report8D.getProblemAttachment());
        }
        return fabosJsonForm;
    }
}
