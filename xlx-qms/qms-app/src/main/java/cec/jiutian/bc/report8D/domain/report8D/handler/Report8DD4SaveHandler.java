package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.report8D.domain.report8D.model.Report8D;
import cec.jiutian.bc.report8D.domain.report8D.model.Report8DD4;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class Report8DD4SaveHandler implements OperationHandler<Report8D, Report8DD4> {

    @Resource
    private Report8DD4Handler report8DD4Handler;

    @Override
    public Report8DD4 fabosJsonFormValue(List<Report8D> data, Report8DD4 fabosJsonForm, String[] param) {
        String[] execParam = {"submit"};
        report8DD4Handler.exec(data, fabosJsonForm, execParam);
        return fabosJsonForm;
    }
}
