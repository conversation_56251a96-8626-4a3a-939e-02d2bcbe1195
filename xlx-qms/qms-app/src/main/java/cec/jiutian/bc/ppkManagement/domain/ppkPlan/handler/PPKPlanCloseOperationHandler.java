package cec.jiutian.bc.ppkManagement.domain.ppkPlan.handler;

import cec.jiutian.bc.ppkManagement.domain.ppkPlan.model.PPKPlan;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PPKPlanCloseOperationHandler implements OperationHandler<PPKPlan, Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<PPKPlan> data, Void modelObject, String[] param) {
        PPKPlan entity = data.get(0);
        entity.setCurrentState(OrderCurrentStateEnum.Enum.END.name());
        fabosJsonDao.mergeAndFlush(entity);
        return "alert('操作成功')";
    }
}
