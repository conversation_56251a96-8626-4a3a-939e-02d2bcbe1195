package cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.handler;

import cec.jiutian.bc.materialInspect.port.client.MaterialInspectFeignClient;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.model.MRBUnqualifiedReview;
import cec.jiutian.bc.mrb.domain.mrbUnqualifiedReview.mto.MRBUnqualifiedReviewDisposalSubMTO;
import cec.jiutian.bc.mrb.enumeration.MRBUnqualifiedBusinessStatusEnum;
import cec.jiutian.bc.mrb.enumeration.UnqualifiedAuditTypeEnum;
import cec.jiutian.bc.mrb.service.MRBUnqualifiedReviewService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description
 */
@Component
@Slf4j
public class MRBUnqualifiedDisposalSubHandler implements OperationHandler<MRBUnqualifiedReview, MRBUnqualifiedReviewDisposalSubMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private MRBUnqualifiedReviewService reviewService;

    @Resource
    private MaterialInspectFeignClient materialInspectFeignClient;

    @Override
    @Transactional
    public String exec(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewDisposalSubMTO modelObject, String[] param) {
        if (modelObject != null) {
            MRBUnqualifiedReview model = data.get(0);
            //todo 调用wms接口同步来料检验、库存检验异常处理结果   参数待产品确认
//            materialInspectFeignClient.updateInventoryLetAcceptFlagByLotSerialId()
            BeanUtil.copyProperties(modelObject, model, "createBy", "createTime");
            model.setBusinessStatus(MRBUnqualifiedBusinessStatusEnum.Enum.UNDER_DISPOSAL.name());
            //创建审批单
            reviewService.createUnqualifiedReviewTaskByDisposal(model, UnqualifiedAuditTypeEnum.Enum.QUALITY_DEPART.name(),
                    model.getQualityDepart().getId(), model.getQualityDepart().getName());
            fabosJsonDao.mergeAndFlush(model);
        }
        return "alert(操作成功)";
    }

    @Override
    public MRBUnqualifiedReviewDisposalSubMTO fabosJsonFormValue(List<MRBUnqualifiedReview> data, MRBUnqualifiedReviewDisposalSubMTO fabosJsonForm, String[] param) {
        MRBUnqualifiedReview model = data.get(0);
        BeanUtil.copyProperties(model, fabosJsonForm);
        return fabosJsonForm;
    }
}
