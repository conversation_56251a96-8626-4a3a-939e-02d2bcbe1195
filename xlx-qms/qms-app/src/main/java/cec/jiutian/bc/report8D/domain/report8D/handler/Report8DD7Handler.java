package cec.jiutian.bc.report8D.domain.report8D.handler;

import cec.jiutian.bc.materialInspect.domain.publicInspectionTask.mto.UserForInsTaskMTO;
import cec.jiutian.bc.report8D.domain.report8D.model.*;
import cec.jiutian.bc.report8D.enums.MeasureProgressEnum;
import cec.jiutian.bc.report8D.enums.Report8DStatusEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class Report8DD7Handler implements OperationHandler<Report8D, Report8DD7> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<Report8D> data, Report8DD7 modelObject, String[] param) {
        if (CollectionUtils.isEmpty(modelObject.getD7PreventMeasuresList())) {
            return "msg.success('填写的数据为空')";
        }
        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            report8D.setStatus(checkParam(param) ? Report8DStatusEnum.Enum.D7_AUDIT.name() : report8D.getStatus());
            report8DSetPreventMeasureListAndUserIds(report8D, modelObject.getD7PreventMeasuresList());
            fabosJsonDao.mergeAndFlush(report8D);

            // 生成审核任务
            if (checkParam(param)) {
                AuditTask auditTask = AuditTask.createInstance(report8D);
                fabosJsonDao.mergeAndFlush(auditTask);
            }
        }
        return "msg.success('操作成功')";
    }

    private boolean checkParam(String[] param) {
        if (param == null || param.length == 0) {
            return false;
        }
        return param[0].equals("submit");
    }

    private void report8DSetPreventMeasureListAndUserIds(Report8D report8D, List<D7PreventMeasures> d7PreventMeasuresList) {
        List<PreventMeasures> preventMeasureList = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>();
        d7PreventMeasuresList.forEach(d -> {
            PreventMeasures preventMeasures = new PreventMeasures();
            BeanUtil.copyProperties(d, preventMeasures);
            if (Objects.isNull(preventMeasures.getProgress())) {
                preventMeasures.setProgress(MeasureProgressEnum.Enum.WAIT_EXECUTE.name());
            }
            preventMeasureList.add(preventMeasures);
            userIdSet.add(d.getResponsiblePersonId());
        });
        report8D.setPreventMeasuresList(preventMeasureList);

        ArrayList<String> longUsrIds = new ArrayList<>(userIdSet);
        report8D.setPreventUserIds(String.join(",", longUsrIds));
    }

    @Override
    public Report8DD7 fabosJsonFormValue(List<Report8D> data, Report8DD7 fabosJsonForm, String[] param) {

        if (CollectionUtils.isNotEmpty(data)) {
            Report8D report8D = data.get(0);
            if (CollectionUtils.isNotEmpty(report8D.getPreventMeasuresList())) {
                List<D7PreventMeasures> prventMeasureList = new ArrayList<>();
                report8D.getPreventMeasuresList().forEach(d -> {
                    D7PreventMeasures d7PreventMeasures = new D7PreventMeasures();
                    BeanUtil.copyProperties(d, d7PreventMeasures);
                    UserForInsTaskMTO mto = fabosJsonDao.getById(UserForInsTaskMTO.class, d.getResponsiblePersonId());
                    d7PreventMeasures.setResponsibleUserMTO(mto);
                    prventMeasureList.add(d7PreventMeasures);
                });
                fabosJsonForm.setD7PreventMeasuresList(prventMeasureList);
            }
        }
        return fabosJsonForm;
    }
}
