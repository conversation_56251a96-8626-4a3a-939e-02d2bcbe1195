package cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.handler;

import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.model.CMKSampleTask;
import cec.jiutian.bc.cmkManagement.domain.cmkSampleTask.mto.CMKSampleTaskSendMTO;
import cec.jiutian.bc.modeler.enumration.TaskBusinessStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @description TODO
 */
@Component
public class CMKSendSampleOperationHandler implements OperationHandler<CMKSampleTask, CMKSampleTaskSendMTO> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<CMKSampleTask> data, CMKSampleTaskSendMTO modelObject, String[] param) {
        if (modelObject != null) {
            CMKSampleTask condition = new CMKSampleTask();
            condition.setGeneralCode(modelObject.getGeneralCode());
            CMKSampleTask sampleTask = fabosJsonDao.selectOne(condition);

            if (!TaskBusinessStateEnum.Enum.SAMPLING_FINISH.name().equals(sampleTask.getBusinessState())) {
                throw new FabosJsonApiErrorTip("取样任务单状态有误");
            }
            sampleTask.setBusinessState(TaskBusinessStateEnum.Enum.SEND_SAMPLE.name());
            fabosJsonDao.mergeAndFlush(sampleTask);
        }
        return "alert(操作成功)";
    }
}
