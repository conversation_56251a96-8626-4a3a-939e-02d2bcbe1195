package cec.jiutian.bc;

import cec.jiutian.bc.sso.proxy.FileProxy;
import cec.jiutian.core.frame.annotation.FabosJsonScan;
import cec.jiutian.meta.FabosJsonAttachmentUpload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StopWatch;

@SpringBootApplication(scanBasePackages = {
        "cec.jiutian"
})
@ComponentScan(basePackages = {
        "cec.jiutian"
})
@FabosJsonScan(value = {
        "cec.jiutian"
})
@EnableJpaRepositories(basePackages = "cec.jiutian")
@EntityScan(basePackages = {
        "cec.jiutian"
})
@EnableDiscoveryClient
@EnableFeignClients
@EnableJpaAuditing
@FabosJsonAttachmentUpload(FileProxy.class)
@Slf4j
@EnableScheduling
public class BootApplication {
    public static void main(String[] args) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        SpringApplication.run(BootApplication.class, args);
        stopWatch.stop();
        log.info("本次启动总计耗时：{}秒", stopWatch.getTotalTimeSeconds());
    }
}
