package cec.jiutian.bc.sso.domain.application.proxy;

import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.enumeration.CategoryEnum;
import cec.jiutian.bc.sso.repository.CustomRegisteredClientRepository;
import cec.jiutian.bc.sso.util.ClientUtil;
import cec.jiutian.bc.sso.util.CreateHqlUtil;
import cec.jiutian.bc.sso.util.UserUtil;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Component
public class ApplicationProxy implements DataProxy<Application> {
    @Resource
    private CustomRegisteredClientRepository customRegisteredClientRepository;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private CreateHqlUtil createHqlUtil;
    @Resource
    private UserUtil userUtil;
    @Resource
    private UrmCommandService urmCommandService;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        return createHqlUtil.createHql("app");
    }

    @Override
    public void beforeAdd(Application application) {

        if (application.getCategory() == CategoryEnum.Enum.External.name()){
            // 生成客户端ID和密钥
            application.setClientId(UUID.randomUUID().toString());
            application.setClientSecret(UUID.randomUUID().toString());
            // 如果没有设置重定向URI，使用首页地址
            if (StringUtils.isEmpty(application.getRedirectUri())) {
                application.setRedirectUri(application.getHomeUrl());
            }
        }
        application.setUbpFlag(false);
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        userUtil.setUserAccount();
    }

    @Override
    public void afterAdd(Application application) {
        if (Objects.equals(application.getCategory(), CategoryEnum.Enum.External.name())){
            RegisteredClient client = ClientUtil.getClient(application);
            customRegisteredClientRepository.save(client);
        }
    }

    @Override
    public void afterUpdate(Application application) {
        customRegisteredClientRepository.reloadClients();
        // 在系统换组，组换用户时强行清理所有的token,无论在不在线
        urmCommandService.removeAllTokens();
    }
}
