package cec.jiutian.bc.sso.service;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.api.remoteCallLog.enums.RemoteCallCodeEnum;
import cec.jiutian.bc.sso.port.client.CrsFeignClient;
import cec.jiutian.bc.sso.port.client.EamFeignClient;
import cec.jiutian.bc.sso.port.client.EhsFeignClient;
import cec.jiutian.bc.sso.port.client.EmsFeignClient;
import cec.jiutian.bc.sso.port.client.IotFeignClient;
import cec.jiutian.bc.sso.port.client.LimsFeignClient;
import cec.jiutian.bc.sso.port.client.QmsFeignClient;
import cec.jiutian.bc.sso.port.client.SpcFeignClient;
import cec.jiutian.bc.sso.port.client.WmsFeignClient;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

// 向各个业务应用发布角色菜单分配信息
@Component
@Slf4j
public class ApplicationRoleMenuDistributeService {

    private final IotFeignClient iotFeignClient;
    private final WmsFeignClient wmsFeignClient;

    private final CrsFeignClient crsFeignClient;

    private final EamFeignClient eamFeignClient;

    private final EhsFeignClient ehsFeignClient;

    private final LimsFeignClient limsFeignClient;

    private final QmsFeignClient qmsFeignClient;

    private final SpcFeignClient spcFeignClient;

    private final EmsFeignClient emsFeignClient;

    public ApplicationRoleMenuDistributeService(IotFeignClient iotFeignClient, WmsFeignClient wmsFeignClient, CrsFeignClient crsFeignClient, EamFeignClient eamFeignClient, EhsFeignClient ehsFeignClient, LimsFeignClient limsFeignClient, QmsFeignClient qmsFeignClient, SpcFeignClient spcFeignClient, EmsFeignClient emsFeignClient) {
        this.iotFeignClient = iotFeignClient;
        this.wmsFeignClient = wmsFeignClient;
        this.crsFeignClient = crsFeignClient;
        this.eamFeignClient = eamFeignClient;
        this.ehsFeignClient = ehsFeignClient;
        this.limsFeignClient = limsFeignClient;
        this.qmsFeignClient = qmsFeignClient;
        this.spcFeignClient = spcFeignClient;
        this.emsFeignClient = emsFeignClient;
    }


    // params参数格式为：{"roleId": "1", "roleExternalMenus": {"systemCode1": ["menuId1", "menuId2"], "systemCode2": ["menuId3"]}}
    public void distributeRoleMenu(Map<String, Object> params) {
        // 根据业务应用，调用不同的服务，向业务应用发布角色菜单分配信息
        Map<String, Object> roleExternalMenus = (Map<String, Object>) params.get("roleExternalMenus");
                for (Map.Entry<String, Object> entry : roleExternalMenus.entrySet()) {
                    String systemCode = entry.getKey();
                    Map<String, Object> distributeParams = Map.of(
                        "roleId", params.get("roleId"),
                        "menus", entry.getValue()
                    );
                    log.info("菜单权限下发远程调用，目标系统：" + systemCode);
                    switch (systemCode) {
                        case "iot" -> {
                            try {
                                RemoteCallResult remoteCallResult = iotFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用IotFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用IotFeignClient失败" + e);
                            }
                        }
                        case "wms" -> {
                            try {
                                RemoteCallResult remoteCallResult = wmsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用WmsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用WmsFeignClient失败" + e);
                            }
                        }
                        case "crs" -> {
                            try {
                                RemoteCallResult remoteCallResult = crsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用CrsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用CrsFeignClient失败" + e);
                            }
                        }
                        case "eam" -> {
                            try {
                                RemoteCallResult remoteCallResult = eamFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用EamFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用EamFeignClient失败" + e);
                            }
                        }
                        case "ehs" -> {
                            try {
                                RemoteCallResult remoteCallResult = ehsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用EhsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用EhsFeignClient失败" + e);
                            }
                        }
                        case "lims" -> {
                            try {
                                RemoteCallResult remoteCallResult = limsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用LimsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用LimsFeignClient失败" + e);
                            }
                        }
                        case "qms" -> {
                            try {
                                RemoteCallResult remoteCallResult = qmsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用QmsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用QmsFeignClient失败" + e);
                            }
                        }
                        case "spc" -> {
                            try {
                                RemoteCallResult remoteCallResult = spcFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用SpcFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用SpcFeignClient失败" + e);
                            }
                        }
                        case "ems" -> {
                            try {
                                RemoteCallResult remoteCallResult = emsFeignClient.roleMenuDistribute(distributeParams);

                                if (RemoteCallCodeEnum.SUCCESS.getCode().equals(remoteCallResult.getStatus())) {
                                    log.info("菜单权限下发远程调用成功，目标系统：" + systemCode);
                                } else {
                                    String errorMessage = "菜单权限下发远程调用失败，目标系统：" + systemCode + "，错误信息：" + remoteCallResult.getStatus() + "," + remoteCallResult.getMessage();
                                    log.error(errorMessage);
                                    throw new FabosJsonApiErrorTip(errorMessage);
                                }
                            } catch (Exception e) {
                                log.error("调用EmsFeignClient失败", e);
                                throw new FabosJsonApiErrorTip("调用EmsFeignClient失败" + e);
                            }
                        }
                    }

                }

    }
}
