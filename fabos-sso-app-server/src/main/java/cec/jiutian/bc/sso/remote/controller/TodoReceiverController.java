package cec.jiutian.bc.sso.remote.controller;


import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.remote.dto.TodoNotification;
import cec.jiutian.bc.sso.repository.ApplicationRepository;
import cec.jiutian.bc.sso.service.TodoService;
import cec.jiutian.core.frame.module.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PutMapping;

@RestController
@RequestMapping("/fabos-api/to-receiver")
public class TodoReceiverController {


    private TodoService todoService;

    private ApplicationRepository applicationRepository;

    public TodoReceiverController(TodoService todoService, ApplicationRepository applicationRepository) {
        this.todoService = todoService;
        this.applicationRepository = applicationRepository;
    }

    @PostMapping("/notify")
    public R<String> receiveTodoItem(
            @RequestHeader("X-Client-Id") String clientId,
            @RequestHeader("X-Client-Secret") String clientSecret,
            @RequestBody TodoNotification notification) {

        // 验证应用身份
        Application application = applicationRepository.findByClientId(clientId)
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("Application not found"));

        if (!application.getClientSecret().equals(clientSecret)) {
            throw new FabosJsonWebApiRuntimeException("Invalid client secret");
        }

        // 创建待办事项
        TodoItem todoItem = todoService.createTodoItem(
                notification.getAccount(),
                application.getId(),
                notification.toTodoItem()
        );

        return R.ok(todoItem.getBusinessKey());
    }

    @PutMapping("/complete")
    public R completeTodoItem(
            @RequestHeader("X-Client-Id") String clientId,
            @RequestHeader("X-Client-Secret") String clientSecret,
            @RequestParam String businessKey) {

        // 验证应用身份
        Application application = applicationRepository.findByClientId(clientId)
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("Application not found"));

        if (!application.getClientSecret().equals(clientSecret)) {
            throw new FabosJsonWebApiRuntimeException("Invalid client secret");
        }

        // 更新待办事项状态
        todoService.completeTodoItem(businessKey);
        return R.ok();
    }
}
