package cec.jiutian.bc.sso.service;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.sso.remote.feign.SystemRestartFeignClient;
import cec.jiutian.common.context.RequestContext;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class SystemRestartService {

    @Resource
    private DiscoveryClient discoveryClient;

//    @Autowired
//    @LoadBalanced
//    private RestTemplate restTemplate;


    private final FeignClientBuilder feignClientBuilder;

    /**
     * 构造函数注入 ApplicationContext，并用它来初始化 FeignClientBuilder.
     * FeignClientBuilder 需要 ApplicationContext 来访问 Spring 的环境和配置.
     *
     * @param context Spring应用上下文
     */
    @Autowired
    public SystemRestartService(ApplicationContext context) {
        this.feignClientBuilder = new FeignClientBuilder(context);
    }


    public String restartSystem(String systemCode) {
        try {
            // 检查服务是否注册
            List<ServiceInstance> instances = discoveryClient.getInstances(systemCode);
            if (instances.isEmpty()) {
                throw new RuntimeException("系统 " + systemCode + " 未在服务注册中心找到");
            }

            // 使用服务名调用，LoadBalancer会自动负载均衡
            String restartUrl = "http://" + systemCode + "/api/system/restart";

//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            headers.setBearerAuth(RequestContext.getHeader("Authorization"));
//            HttpEntity<String> entity = new HttpEntity<>(headers);
//
//            ResponseEntity<String> response = restTemplate.postForEntity(restartUrl, entity, String.class);

            // 动态创建Feign客户端
            SystemRestartFeignClient client = feignClientBuilder
                    .forType(SystemRestartFeignClient.class, systemCode)
                    .build();

            RemoteCallResult<?> result = client.restart();


            if (result.getStatus().equals("200")) {
                log.info("系统 {} 重启请求发送成功，可用实例数: {}", systemCode, instances.size());
                return "系统重启请求已发送，系统将在5秒后重启";
            } else {
//                throw new RuntimeException("重启请求失败");
                throw new RuntimeException("重启请求失败: " + result.getData());
            }

        } catch (Exception e) {
            log.error("重启系统 {} 失败", systemCode, e);
            throw new RuntimeException("重启失败: " + e.getMessage());
        }
    }

//    public boolean isSystemHealthy(String systemCode) {
//        try {
//            String healthUrl = "http://" + systemCode + "/actuator/health";
//            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
//            return response.getStatusCode().is2xxSuccessful();
//        } catch (Exception e) {
//            return false;
//        }
//    }
}
