package cec.jiutian.bc.sso.remote.controller;

import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.remote.dto.TodoSummary;
import cec.jiutian.bc.sso.service.TodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/todos")
@PreAuthorize("isAuthenticated()")
@Slf4j
public class TodoApiController {

    @Autowired
    private TodoService todoService;

    /**
     * 获取待办事项列表
     */
    @GetMapping
    public ResponseEntity<Page<TodoItem>> getTodos(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createTime") String sortBy,
            @RequestParam(required = false) String applicationId,
            @RequestParam(required = false) String currentStatus) {

        Page<TodoItem> todos;
        if (applicationId != null) {
            todos = todoService.getUserTodosByApplicationPaged(
                    userDetails.getUsername(),
                    applicationId,
                    page,
                    size,
                    sortBy
            );
        } else if (currentStatus != null) {
            todos = todoService.getUserTodosByStatusPaged(
                    userDetails.getUsername(),
                    currentStatus,
                    page,
                    size,
                    sortBy
            );
        } else {
            todos = todoService.getUserTodosPaged(
                    userDetails.getUsername(),
                    page,
                    size,
                    sortBy
            );
        }

        return ResponseEntity.ok(todos);
    }

    /**
     * 获取待办事项摘要
     */
    @GetMapping("/summary")
    public ResponseEntity<TodoSummary> getTodoSummary(
            @AuthenticationPrincipal UserDetails userDetails) {
        TodoSummary summary = todoService.getUserTodoSummary(userDetails.getUsername());
        return ResponseEntity.ok(summary);
    }

    /**
     * 开始处理待办事项
     */
    @PutMapping("/{id}/process")
    public ResponseEntity<TodoItem> processTodo(
            @PathVariable String id,
            @AuthenticationPrincipal UserDetails userDetails) {
        TodoItem todoItem = todoService.startProcessing(id, userDetails.getUsername());
        return ResponseEntity.ok(todoItem);
    }

    /**
     * 批量完成待办事项
     */
    @PutMapping("/complete")
    public ResponseEntity<Void> completeTodos(
            @RequestBody List<String> todoIds,
            @AuthenticationPrincipal UserDetails userDetails) {
        todoService.completeTodoItems(todoIds, userDetails.getUsername());
        return ResponseEntity.ok().build();
    }

    /**
     * 获取过期待办事项
     */
    @GetMapping("/expired")
    public ResponseEntity<List<TodoItem>> getExpiredTodos(
            @AuthenticationPrincipal UserDetails userDetails) {
        List<TodoItem> expiredTodos = todoService.getExpiredTodos(userDetails.getUsername());
        return ResponseEntity.ok(expiredTodos);
    }

    /**
     * 按应用筛选待办事项
     */
    @GetMapping("/by-application/{applicationId}")
    public ResponseEntity<List<TodoItem>> getTodosByApplication(
            @PathVariable String applicationId,
            @AuthenticationPrincipal UserDetails userDetails) {
        List<TodoItem> todos = todoService.getUserTodosByApplication(
                userDetails.getUsername(),
                applicationId
        );
        return ResponseEntity.ok(todos);
    }

    /**
     * 按日期范围查询待办事项
     */
    @GetMapping("/by-date-range")
    public ResponseEntity<List<TodoItem>> getTodosByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @AuthenticationPrincipal UserDetails userDetails) {
        List<TodoItem> todos = todoService.findByUserUsernameAndDateRange(
                userDetails.getUsername(),
                startDate,
                endDate
        );
        return ResponseEntity.ok(todos);
    }
}
