package cec.jiutian.bc.sso.remote.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ApplicationLoginDto {

    private String name;
    private String systemCode;
    private String validFlag;
    private String category;
    private String homeUrl;
    private String serviceCode;
    private String description;
    private Boolean ubpFlag;
    private Boolean mobileFlag;

    public ApplicationLoginDto(String name, String systemCode, String validFlag, String category, String homeUrl, String serviceCode, String description, Boolean ubpFlag, Boolean mobileFlag) {
        this.name = name;
        this.systemCode = systemCode;
        this.validFlag = validFlag;
        this.category = category;
        this.homeUrl = homeUrl;
        this.serviceCode = serviceCode;
        this.description = description;
        this.ubpFlag = ubpFlag;
        this.mobileFlag = mobileFlag;
    }
}
