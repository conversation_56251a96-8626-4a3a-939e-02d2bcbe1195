package cec.jiutian.bc.sso.controller;

import cec.jiutian.bc.sso.repository.UserRepository;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
public class UserInfoController {

    private final UserRepository userRepository;

    @GetMapping("/userinfo")
    public Map<String, Object> getUserInfo(@AuthenticationPrincipal OAuth2User oauth2User) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("sub", oauth2User.getName());
        
        // 获取用户详细信息
        User user = userRepository.findByName(oauth2User.getName())
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("User not found"));

        if (user != null) {
            userInfo.put("name", user.getName());
            userInfo.put("email", user.getEmailAddress());
            userInfo.put("preferred_username", user.getAccount());
            // 添加其他需要的用户信息
        }
        
        return userInfo;
    }
} 
