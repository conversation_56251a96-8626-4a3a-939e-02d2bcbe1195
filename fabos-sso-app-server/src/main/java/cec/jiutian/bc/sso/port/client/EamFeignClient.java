package cec.jiutian.bc.sso.port.client;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.urm.remote.FeignConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@Component
@FeignClient(name = "xlx-eam") // 对应目标服务的spring.application.name
public interface EamFeignClient {
    @PostMapping(FeignConstant.APPLICATION_ROLE_MENU_DISTRIBUTE)
    RemoteCallResult roleMenuDistribute(@RequestBody Map<String, Object> params);
}
