package cec.jiutian.bc.sso.repository;

import cec.jiutian.bc.sso.service.ClientDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Primary
public class CustomRegisteredClientRepository implements RegisteredClientRepository {

    private final Map<String, RegisteredClient> registeredClientMap = new ConcurrentHashMap<>();

    private final ClientDetailsService clientDetailsService;

    @Autowired
    public CustomRegisteredClientRepository(ClientDetailsService clientDetailsService) {
        this.clientDetailsService = clientDetailsService;
        reloadClients();
    }

    public void reloadClients() {
        List<RegisteredClient> clients = clientDetailsService.loadClients();
        for (RegisteredClient client : clients) {
            registeredClientMap.put(client.getClientId(), client);
        }
    }

    @Override
    public void save(RegisteredClient registeredClient) {
        registeredClientMap.put(registeredClient.getClientId(), registeredClient);
    }

    @Override
    public RegisteredClient findById(String id) {
        return registeredClientMap.values().stream()
                .filter(client -> client.getId().equals(id))
                .findFirst()
                .orElse(null);
    }



    @Override
    public RegisteredClient findByClientId(String clientId) {
        return registeredClientMap.get(clientId);
    }
}
