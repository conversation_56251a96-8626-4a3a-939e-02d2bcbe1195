package cec.jiutian.bc.sso.util;

import cec.jiutian.bc.sso.domain.application.model.Application;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;

import java.time.Duration;

public class ClientUtil {
    /**
     * 根据应用程序信息生成一个注册的客户端对象
     * 此方法配置了客户端的详细信息，包括认证方法、授权类型、重定向URI和作用域等，
     * 以便在OAuth2.0授权流程中使用这些配置
     *
     * @param application 包含客户端应用程序的相关信息，如客户端ID、客户端密钥等
     * @return 返回一个配置好的RegisteredClient对象，它代表了一个在系统中注册的OAuth2.0客户端
     */
    public static RegisteredClient getClient(Application application) {
        return RegisteredClient.withId(application.getId())
                .clientId(application.getClientId())
                .clientSecret(application.getClientSecret())
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
//                .redirectUri(application.getLogUri())
                .redirectUri(application.getRedirectUri())
                .redirectUri(application.getHomeUrl())
                .scope("all")
                .scope(OidcScopes.OPENID)
                .scope(OidcScopes.PROFILE)
                .scope(OidcScopes.PHONE)
                .scope(OidcScopes.ADDRESS)
                .scope(OidcScopes.EMAIL)
                .scope("message.read")
                .scope("message.write")
                .scope("read")
                .scope("write")
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .tokenSettings(TokenSettings.builder()
                        .accessTokenTimeToLive(Duration.ofMinutes(application.getAccessTokenTime()))  // 设置访问令牌的有效期
                        .refreshTokenTimeToLive(Duration.ofDays(application.getRefreshTokenTime()))  // 设置刷新令牌的有效期
                        .reuseRefreshTokens(application.getReuseRefreshTokens())                   // 是否重用刷新令牌
                        .build())
                .build();
    }
}
