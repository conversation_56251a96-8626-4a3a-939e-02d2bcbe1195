package cec.jiutian.bc.sso.service;

import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.remote.dto.NotificationType;
import cec.jiutian.bc.sso.remote.dto.TodoNotificationMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NotificationService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired(required = false)
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送待办事项通知
     */
    public void sendTodoNotification(String username, TodoItem todoItem) {
        try {
            // 构建通知消息
            TodoNotificationMessage notification = TodoNotificationMessage.builder()
                    .type(NotificationType.NEW_TODO)
                    .todoId(todoItem.getId())
                    .title(todoItem.getTitle())
                    .description(todoItem.getDescription())
                    .applicationName(todoItem.getApplication().getName())
                    .createdTime(todoItem.getCreateTime())
                    .build();

            // 通过WebSocket发送实时通知
            messagingTemplate.convertAndSendToUser(
                    username,
                    "/topic/todos",
                    notification
            );

            // 如果配置了消息队列，也发送到消息队列
            if (rabbitTemplate != null) {
                rabbitTemplate.convertAndSend(
                        "portal.notifications",
                        "todo.new",
                        notification
                );
            }

            log.debug("Sent todo notification to user {}: {}", username, notification);
        } catch (Exception e) {
            log.error("Failed to send todo notification", e);
        }
    }

    /**
     * 发送待办事项完成通知
     */
    public void sendTodoCompletionNotification(String username, TodoItem todoItem) {
        try {
            TodoNotificationMessage notification = TodoNotificationMessage.builder()
                    .type(NotificationType.TODO_COMPLETED)
                    .todoId(todoItem.getId())
                    .title(todoItem.getTitle())
                    .applicationName(todoItem.getApplication().getName())
                    .completedTime(todoItem.getUpdateTime())
                    .build();

            // 通过WebSocket发送实时通知
            messagingTemplate.convertAndSendToUser(
                    username,
                    "/topic/todos",
                    notification
            );

            // 如果配置了消息队列，也发送到消息队列
            if (rabbitTemplate != null) {
                rabbitTemplate.convertAndSend(
                        "portal.notifications",
                        "todo.completed",
                        notification
                );
            }

            log.debug("Sent todo completion notification to user {}: {}", username, notification);
        } catch (Exception e) {
            log.error("Failed to send todo completion notification", e);
        }
    }
}
