package cec.jiutian.bc.sso.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.endpoint.event.RefreshEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class NacosConfigRefreshListener implements ApplicationListener<RefreshEvent> {
    private static final Logger logger = LoggerFactory.getLogger(NacosConfigRefreshListener.class);

    @Override
    public void onApplicationEvent(RefreshEvent event) {
        logger.info("Nacos配置已更新，变更内容：{}", event.getEvent());
    }
}
