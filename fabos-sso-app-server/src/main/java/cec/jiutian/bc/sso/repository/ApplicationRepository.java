package cec.jiutian.bc.sso.repository;

import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.remote.dto.ApplicationLoginDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationRepository extends JpaRepository<Application, String> {
    Optional<Application> findByClientId(String clientId);
    Optional<Application> findByServiceCode(String svsCode);
    //List<Application> findByUsers_account(String account);

    @Override
    List<Application> findAll();

    @Query(value = "from Application where category = 'External' or ubpFlag = true")
    List<Application> findAsClients();

    @Query(value = "select new cec.jiutian.bc.sso.remote.dto.ApplicationLoginDto(name as name, systemCode as systemCode, validFlag as validFlag, category as category, homeUrl as homeUrl, serviceCode as serviceCode, description as description, ubpFlag as ubpFlag, mobileFlag as mobileFlag) from Application ")
    List<ApplicationLoginDto> findLoginApplications();

    @Query(nativeQuery = true, value = """
            select count(0)
            from fd_application a
                     left join fd_application_with_group ag on a.id = ag.application_id
                     left join fd_user_group ug on ug.id = ag.group_id
                     left join fd_group_with_user gu on gu.target_id = ug.id
                     left join fd_meta_user u on gu.source_id = u.id
            where a.service_code = :svsCode and (u.account = :userId or a.ubp_flag = true)""")
    Integer getCount(@Param("svsCode")String svsCode,@Param("userId") String userId);
}
