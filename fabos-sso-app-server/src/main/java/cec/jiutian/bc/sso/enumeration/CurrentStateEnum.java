package cec.jiutian.bc.sso.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用当前状态枚举
 * <AUTHOR>
 * @date 2025/1/20 16:18
 */
public class CurrentStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue(), data.getColor(), ""));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Enable("启用", "active"),
        Disable("停用", "inactive");

        private final String value;
        private final String color;

    }
}
