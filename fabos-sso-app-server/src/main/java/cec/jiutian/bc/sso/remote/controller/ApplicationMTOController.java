package cec.jiutian.bc.sso.remote.controller;

import cec.jiutian.bc.sso.remote.dto.AppMTODTO;
import cec.jiutian.bc.sso.remote.mto.ApplicationMTO;
import cec.jiutian.bc.sso.repository.ApplicationMTORepository;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/base-foun")
public class ApplicationMTOController {

    @Resource
    private ApplicationMTORepository applicationMTORepository;


    @PostMapping("/getLoginSystemComponentList")
    public ResponseEntity<List<AppMTODTO>> getLoginSystemComponentList() {
//        List<ApplicationMTO> all = applicationMTORepository.findAll().stream()
//                .filter(applicationMTO -> Boolean.FALSE.equals(applicationMTO.getUbpFlag()))
//                .collect(Collectors.toList());
        List<ApplicationMTO> all = applicationMTORepository.findAll();
        if (CollectionUtils.isEmpty(all)) {
            return ResponseEntity.ok(new ArrayList<>());
        }
        ArrayList<AppMTODTO> appMTODTOS = new ArrayList<>(all.size());
        for (ApplicationMTO applicationMTO : all) {
            appMTODTOS.add(new AppMTODTO(applicationMTO));
        }
        return ResponseEntity.ok(appMTODTOS);
    }
}
