package cec.jiutian.bc.sso.util;

import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static cec.jiutian.fc.log.utils.UserTypeUtil.SUPER_USER;

@Component
public class CreateHqlUtil {
    @Resource
    private FabosJsonDao fabosJsonDao;
    public String createHql(String type) {
        // 如果是超级管理员，则返回null，不拼接sql
        UserContext.CurrentUser currentUser = UserContext.get();
        if (currentUser.getHashAdmin()) {
            return null;
        }
        // 获取当前用户id
        String userId = UserContext.getUserId();
        String hql = "FROM Application a " +
                "JOIN a.userGroupSet ug " +
                "JOIN ug.userSet u " +
                "WHERE u.id = :userId";
        TypedQuery<Application> query = fabosJsonDao.getEntityManager().createQuery(hql, Application.class);
        query.setParameter("userId",userId);
        List<Application> applicationList = query.getResultList();
        if (CollUtil.isEmpty(applicationList)) {
            switch (type) {
                case "app":
                    return "id = ''";
                case "log":
                    return "ApplicationLog.id = ''";
                case "todo":
                    return "application.id = ''";
                default:
                    return null;
            }
        }
        String applicationIds = applicationList.stream()
                .map(Application::getId)
                .map(String::valueOf)
                .map(id -> "\"" + id + "\"") // 添加双引号
                .collect(Collectors.joining(","));
        switch (type) {
            case "app":
                return "id IN (" + applicationIds + ")";
            case "log":
                return "ApplicationLog.id IN (" + applicationIds + ")";
            case "todo":
                return "application.id IN (" + applicationIds + ")";
            default:
                return null;
        }
    }
}
