package cec.jiutian.bc.sso.domain.application.proxy;

import cec.jiutian.bc.sso.domain.application.model.ApplicationLog;
import cec.jiutian.bc.sso.util.CreateHqlUtil;
import cec.jiutian.bc.sso.util.UserUtil;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Component
public class ApplicationLogProxy implements DataProxy<ApplicationLog> {
    @Resource
    private CreateHqlUtil createHqlUtil;
    @Resource
    private UserUtil userUtil;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        return createHqlUtil.createHql("log");
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        userUtil.setUserAccount();
    }
}
