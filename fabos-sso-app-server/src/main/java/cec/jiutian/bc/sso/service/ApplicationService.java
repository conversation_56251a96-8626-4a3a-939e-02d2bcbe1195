package cec.jiutian.bc.sso.service;

import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.remote.dto.ApplicationLoginDto;
import cec.jiutian.bc.sso.repository.ApplicationRepository;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ApplicationService {
    private final ApplicationRepository applicationRepository;

    @Value("${spring.application.name}")
    public String currentService = "";

    private final FabosJsonDao fabosJsonDao;

    public ApplicationService(ApplicationRepository applicationRepository, FabosJsonDao fabosJsonDao) {
        this.applicationRepository = applicationRepository;
        this.fabosJsonDao = fabosJsonDao;
    }

    public Application findByClientId(String clientId) {
        return applicationRepository.findByClientId(clientId)
                .orElseThrow(() -> new UsernameNotFoundException("Application not found"));
    }

    public boolean checkSystemAvailableForUser(String svsCode, String username) {
        if (svsCode.equals(currentService)) {
            // 这里被调用，则说明当前服务是ubp，如果需要登录的目标系统恰好就是ubp，则直接通过
            return true;
        }
        Integer count = applicationRepository.getCount(svsCode, username);
        return Objects.nonNull(count) && count > 0;
    }

    public Application findByServiceCode(String svsCode) {
        return applicationRepository.findByServiceCode(svsCode)
                .orElseThrow(() -> new UsernameNotFoundException("Application not found"));
    }

    public Application activateApplication(String id, String registrationCode) {
        Application application = applicationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Application not found"));



        throw new RuntimeException("Invalid registration code");
    }

    public List<ApplicationLoginDto> getLoingAbleApplications() {
        return applicationRepository.findLoginApplications();
    }
}
