package cec.jiutian.bc.sso.remote.event;

import cec.jiutian.bc.sso.service.ApplicationRoleMenuDistributeService;
import cec.jiutian.bc.urm.inbound.remote.event.ApplicationRoleMenuEvent;
import jakarta.annotation.Resource;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Map;

@Component
public class AppEventListener {

    @Resource
    private ApplicationRoleMenuDistributeService applicationRoleMenuDistributeService;

    @TransactionalEventListener(
            phase = TransactionPhase.BEFORE_COMMIT, // 事务提交前触发
            fallbackExecution = false // 无事务时不执行
    )
    @GlobalTransactional(timeoutMills = 120000)
    public void onFrameworkEvent(ApplicationRoleMenuEvent event) {
        // 执行业务系统权限更新
        applicationRoleMenuDistributeService.distributeRoleMenu((Map<String, Object>) event.getResult());
    }
}
