package cec.jiutian.bc.sso.proxy;

import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.view.fun.AttachmentProxy;
import io.minio.BucketExistsArgs;
import io.minio.DownloadObjectArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.RemoveObjectArgs;
import io.minio.UploadObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.HashMap;
import java.util.UUID;

import static cec.jiutian.api.file.utils.FileUtil.FS_SEP;


/**
 * <AUTHOR>
 * @time 2024-10-30 15:51
 */

@Service
@Slf4j
public class FileProxy implements AttachmentProxy {

//    private static final String TEMPORARY_DOWNLOAD_FILE = "/.temporaryDownloadFile";
    private static final String TEMPORARY_DOWNLOAD_PATH = "/.temporaryDownload";

    private final FabosJsonProp fabosJsonProp;

    public FileProxy(FabosJsonProp fabosJsonProp) {
        this.fabosJsonProp = fabosJsonProp;
    }

    @Override
    public HashMap<String, String> upLoad(File file, String path) {
        String endPoint = fabosJsonProp.getMinioEndPoint();
        String accessKey = fabosJsonProp.getMinioKey();
        String secret = fabosJsonProp.getMinioSecret();
        String bucket = fabosJsonProp.getFileStorageBucketName();
        checkConnectionArgumentsLegal(endPoint, accessKey, secret);
        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(endPoint)
                .credentials(accessKey, secret)
                .build()) {
            // If specified bucket cannot be found, create one
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
                log.info("Created storage bucket: {}", bucket);
            }

            // If upload path does not exist, create one
            File uploadDir = new File(fabosJsonProp.getUploadPath());
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            minioClient.uploadObject(UploadObjectArgs.builder()
                    .bucket(bucket)
                    .object(path)
                    .filename(file.getAbsolutePath())
                    .build());

            log.info("将文件上传到minio成功: {}，{}。", path, isLocalSave()
                    ? "同时存储于本机文件系统"
                    : "由于设置了本地不存储，此文件将不会存储于本机文件系统");
            return new HashMap<>() {{
                put("path", path);
                put("bucket", bucket);
            }};
        } catch (Exception e) {
            log.error("将文件上传到minio时发生错误：{}", e.toString());
            throw new FabosJsonApiErrorTip("将文件上传到minio时发生错误：" + e);
        }
    }

    /**
     * 注意使用完成destFile后记得删除
     **/
    @Override
    @Transactional
    public File downLoad(String bucket, String path) {
        String endPoint = fabosJsonProp.getMinioEndPoint();
        String accessKey = fabosJsonProp.getMinioKey();
        String secret = fabosJsonProp.getMinioSecret();
        checkConnectionArgumentsLegal(endPoint, accessKey, secret);
        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(endPoint)
                .credentials(accessKey, secret)
                .build()) {

            String tempFilePath =fabosJsonProp.getUploadPath() + TEMPORARY_DOWNLOAD_PATH;
            // If temp path does not exist, create one
            File uploadDir = new File(tempFilePath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 这里重新生成一个文件名，避免多客户端同时下载同一个大文件时，文件名重复
            tempFilePath += FS_SEP + UUID.randomUUID();
            minioClient.downloadObject(
                    DownloadObjectArgs.builder()
                            .bucket(bucket)
                            .object(path)
                            .filename(tempFilePath)
                            .build());
            log.info("从minio成功下载文件: {}", path);
            return new File(tempFilePath);
        } catch (Exception e) {
            log.error("从minio下载文件时发生错误: {}", e.toString());
            throw new FabosJsonApiErrorTip("从minio下载文件时发生错误：" + e);
        }
    }

    private static void checkConnectionArgumentsLegal(String endPoint, String accessKey, String secret) {
        if (StringUtils.isBlank(endPoint)
                || StringUtils.isBlank(accessKey)
                || StringUtils.isBlank(secret)) {
            log.error("minio连接信息配置有误，请检查...");
            throw new FabosJsonApiErrorTip("minio连接信息配置有误，请检查...");
        }
    }

    @Override
    public String fileDomain() {
        return "";
    }

    @Override
    public boolean remove(String bucket, String path) {
        String endPoint = fabosJsonProp.getMinioEndPoint();
        String accessKey = fabosJsonProp.getMinioKey();
        String secret = fabosJsonProp.getMinioSecret();
        checkConnectionArgumentsLegal(endPoint, accessKey, secret);
        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(endPoint)
                .credentials(accessKey, secret)
                .build()) {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(path)
                            .build());
            return true;
        } catch (Exception e) {
            log.error("从minio删除文件时发生错误: {}", e.toString());
            throw new FabosJsonApiErrorTip("从minio删除文件时发生错误：" + e);
        }
    }

    @Value("${file.upload.localSave:false}")
    private boolean localSave;

    @Override
    public boolean
    isLocalSave() {
        return this.localSave;
    }
}
