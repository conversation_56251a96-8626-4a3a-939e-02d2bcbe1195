package cec.jiutian.bc.sso.remote.controller;

import cec.jiutian.bc.sso.repository.UserRepository;
import cec.jiutian.bc.sso.service.CustomerLoginService;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Optional;

@Controller
public class CustomerLogin {
    @Resource
    private CustomerLoginService customerLoginService;

    /**
     * 进行用户信息加载到自定义登录页面中
     * <AUTHOR>
     * @date 2025/2/14 14:30
     * @param model
     * @return
     */
    @GetMapping("/login")
    public String login(Model model) {
       return customerLoginService.login(model);
    }
}
