package cec.jiutian.bc.sso.domain.application.model;

import cec.jiutian.bc.sso.domain.application.proxy.ApplicationLogProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "应用日志",
        orderBy = "ApplicationLog.createTime desc",
        dataProxy = ApplicationLogProxy.class,
        power = @Power(add = false, edit = false, delete = false, viewDetails = false),
        rowOperation = {
                @RowOperation(
                        code = "ApplicationLog@JUMP",
                        mode = RowOperation.Mode.SINGLE,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.JUMP,
                        callHint = "确定跳转到应用页面吗？",
                        title = "跳转到应用",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "ApplicationLog@JUMP"
                        )
                )
        }
)
@Table(name = "fd_application"
)
@Entity
@Getter
@Setter
@TemplateType(type = "usual")
public class ApplicationLog extends MetaModel {
    @FabosJsonField(
            views = @View(title = "应用名称"),
            edit = @Edit(title = "应用名称", search = @Search())
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "客户端ID", show = false),
            edit = @Edit(title = "客户端ID", show = false)
    )
    private String clientId;

    @FabosJsonField(
            views = @View(title = "应用日志URI"),
            edit = @Edit(title = "应用日志URI")
    )
    private String logUri;

}
