package cec.jiutian.bc.sso.domain.todo.proxy;

import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.util.CreateHqlUtil;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TodoProxy implements DataProxy<TodoItem> {

    @Resource
    private CreateHqlUtil createHqlUtil;

    @Override
    public String beforeFetch(List<Condition> conditions) {
       return createHqlUtil.createHql("todo");
    }
}
