package cec.jiutian.bc.sso.service;

import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.sso.enumeration.ToDoStatusTypeEnum;
import cec.jiutian.bc.sso.remote.dto.TodoSummary;
import cec.jiutian.bc.sso.repository.TodoItemRepository;
import cec.jiutian.bc.sso.repository.UserRepository;
import cec.jiutian.bc.sso.repository.ApplicationRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
@Slf4j
public class TodoService {

    private TodoItemRepository todoItemRepository;

    private UserRepository userRepository;

    private ApplicationRepository applicationRepository;

    private NotificationService notificationService;

    public TodoService(TodoItemRepository todoItemRepository, UserRepository userRepository, ApplicationRepository applicationRepository, NotificationService notificationService) {
        this.todoItemRepository = todoItemRepository;
        this.userRepository = userRepository;
        this.applicationRepository = applicationRepository;
        this.notificationService = notificationService;
    }

    public TodoItem createTodoItem(String account, String applicationId, TodoItem todoItem) {
        User user = userRepository.findByAccount(account)
                .orElse(null);

        Application application = applicationRepository.findById(applicationId)
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("Application not found"));

        todoItem.setUser(user);
        todoItem.setApplication(application);
        // 拼接应用业务标识， 业务标识 + uuid
        todoItem.setBusinessKey(todoItem.getBusinessKey() + "-" + UUID.randomUUID());
        // 赋值重定向地址，为了前端方便处理，统一写成redirectUri
        //todoItem.setRedirectUri(todoItem.getProcessUrl());
        todoItem.setRedirectUri(application.getHomeUrl());
        todoItem.setClientId(application.getClientId());
        todoItem.setClientName(application.getName());
        TodoItem saved = todoItemRepository.save(todoItem);

        // 发送实时通知
        notificationService.sendTodoNotification(account, saved);

        return saved;
    }

    public TodoItem startProcessing(String todoId, String account) {
        TodoItem todoItem = todoItemRepository.findById(todoId)
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("Todo item not found"));

        if (!todoItem.getUser().getAccount().equals(account)) {
            throw new FabosJsonWebApiRuntimeException("Not authorized to process this todo item");
        }

//        todoItem.setCurrentStatus(ToDoStatusTypeEnum.Enum.Processing.name());
        return todoItemRepository.save(todoItem);
    }

    public TodoItem completeTodoItem(String businessKey) {
        TodoItem todoItem = todoItemRepository.findByBusinessKey(businessKey)
                .orElseThrow(() -> new FabosJsonWebApiRuntimeException("Todo item not found"));

        todoItem.setCurrentStatus(ToDoStatusTypeEnum.Enum.Completed.name());
        todoItem.setUpdateTime(LocalDateTime.now());
        // 设置处理时间
        todoItem.setDealTime(LocalDateTime.now());
        TodoItem completed = todoItemRepository.save(todoItem);

        // 发送完成通知
        notificationService.sendTodoCompletionNotification(
                todoItem.getUser().getAccount(),
                completed
        );

        return completed;
    }

    /**
     * 获取用户的待办事项摘要
     */
    public TodoSummary getUserTodoSummary(String username) {
        return TodoSummary.builder()
                .pendingCount(todoItemRepository.countPendingTodosByUsername(username))
                .todosByApp(todoItemRepository.countTodosByApplication(username, ToDoStatusTypeEnum.Enum.Pending.name()))
                .build();
    }

    /**
     * 分页获取用户的待办事项
     */
    public Page<TodoItem> getUserTodosPaged(String username, int page, int size, String sortBy) {
        Sort sort = Sort.by(Sort.Direction.DESC, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return todoItemRepository.findByUserUsername(username, pageable);
    }

    /**
     * 批量完成待办事项
     */
    @Transactional
    public int completeTodoItems(List<String> todoIds, String username) {
        // 验证所有待办事项属于当前用户
        List<TodoItem> todos = todoItemRepository.findAllById(todoIds);
        if (todos.stream().anyMatch(todo -> !todo.getUser().getAccount().equals(username))) {
            throw new FabosJsonWebApiRuntimeException("Some todo items do not belong to the current user");
        }

        return todoItemRepository.updateTodoStatus(todoIds, ToDoStatusTypeEnum.Enum.Completed.name(), LocalDateTime.now());
    }

    /**
     * 清理已完成的待办事项
     */
//    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    @Transactional
    public void cleanupCompletedTodos() {
        LocalDateTime threshold = LocalDateTime.now().minusMonths(1); // 删除一个月前的已完成事项
        int deletedCount = todoItemRepository.deleteCompletedTodos(threshold);
        log.info("Cleaned up {} completed todo items", deletedCount);
    }

    /**
     * 获取指定应用的待办事项
     */
    public List<TodoItem> getUserTodosByApplication(String username, String applicationId) {
        return todoItemRepository.findByUserUsernameAndApplicationId(username, applicationId);
    }

    /**
     * 获取过期的待办事项
     */
    public List<TodoItem> getExpiredTodos(String username) {
        return todoItemRepository.findExpiredTodos(username, LocalDateTime.now());
    }

    public Page<TodoItem> getUserTodosByApplicationPaged(String username, String applicationId, int page, int size, String sortBy) {
        Sort sort = Sort.by(Sort.Direction.DESC, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return todoItemRepository.findByUserUsernameAndApplicationIdOrderByCreatedAtDesc(username, applicationId,pageable);
    }

    public Page<TodoItem> getUserTodosByStatusPaged(String username, String currentStatus, int page, int size, String sortBy) {
        Sort sort = Sort.by(Sort.Direction.DESC, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        return todoItemRepository.findByUserUsernameAndStatus(username, currentStatus,pageable);
    }

    public List<TodoItem> findByUserUsernameAndDateRange(String username, LocalDateTime startDate, LocalDateTime endDate) {
        return todoItemRepository.findByUserUsernameAndDateRange(username,startDate,endDate);
    }
}
