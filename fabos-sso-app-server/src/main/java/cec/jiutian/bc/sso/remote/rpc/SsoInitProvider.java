package cec.jiutian.bc.sso.remote.rpc;

import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@Component("fabos-sso-app")
public class SsoInitProvider implements InitDataDTO.IInitDataProvider{
    @Override
    public List<MetaMenu> initData(InitDataDTO dto) {
        List<MetaMenu> menus = new ArrayList<>();
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(dto.getComponentName())).ifPresent(e -> menus.addAll(e));
        return menus;
    }

    @Override
    public List<MetaDict> initDict(InitDataDTO dto) {
        return null;
    }
}
