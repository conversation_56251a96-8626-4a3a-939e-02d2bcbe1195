package cec.jiutian.bc.sso.service;

import cec.jiutian.bc.sso.repository.UserRepository;
import cec.jiutian.core.service.FabosJsonSessionService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;


@Service
public class CustomerLoginService {
    @Resource
    private UserRepository userRepository;

    @Resource
    private FabosJsonSessionService sessionService;

    @Resource
    private HttpServletRequest httpServletRequest;


    public String login(Model model) {
        // 获取当前登录的用户信息，进行属性设置
//        model.addAttribute("username", THREAD_LOCAL);
//        // 根据用户账户查询用户的密码，进行属性设置
//        Optional<User> user = userRepository.findByAccount(THREAD_LOCAL);
//        if (!user.isPresent()) {
//            return "oauth-busy";
//        }
//        model.addAttribute("password", user.get().getPassword());
        // 返回 Thymeleaf 模板名
        return "oauth-login";
    }
}
