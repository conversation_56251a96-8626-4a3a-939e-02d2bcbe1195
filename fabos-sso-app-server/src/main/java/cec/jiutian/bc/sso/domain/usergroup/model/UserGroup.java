package cec.jiutian.bc.sso.domain.usergroup.model;

import cec.jiutian.bc.sso.domain.usergroup.proxy.UserGroupProxy;
import cec.jiutian.bc.sso.enumeration.CurrentStateEnum;
import cec.jiutian.bc.sso.enumeration.GroupTypeEnum;
import cec.jiutian.bc.sso.enumeration.YesOrNoEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@FabosJson(
        name = "用户组管理",
        orderBy = "UserGroup.sort",
        dataProxy = UserGroupProxy.class,
        tree = @Tree(pid = "parentGroup.id", expandLevel = 1)
)
@Table(name = "fd_user_group",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"name"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeForm")
public class UserGroup extends MetaModel {
    @FabosJsonField(
            views = @View(title = "用户组名称"),
            edit = @Edit(title = "用户组名称", notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "用户组类型"),
            edit = @Edit(title = "用户组类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = GroupTypeEnum.class))
    )
    private String type;

    /*@FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", type = EditType.CHOICE, notNull = true,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CurrentStateEnum.class)
            )
    )
    private String currentState;*/

    @ManyToMany
    @JoinTable(
            name = "fd_group_with_user",
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id")
    )
    @FabosJsonField(
            views = @View(title = "用户", column = "name", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "用户", notNull = true,
                    type = EditType.TAB_TABLE_REFER,
                    search = @Search(),
                    filter = @Filter(value = "User.state = 'Y' and User.account != 'superUser'"),
                    referenceTreeType = @ReferenceTreeType()
            )
    )
    private Set<User> userSet;

    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级分组",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentGroup.id", expandLevel = 1)
            )
    )
    private UserGroup parentGroup;

    @FabosJsonField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述"))
    private String desciption;

    @FabosJsonField(
            views = @View(title = "内置组标识"),
            edit = @Edit(title = "内置组标识", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)))
    private String flag;

}
