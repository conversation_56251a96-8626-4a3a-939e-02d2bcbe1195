package cec.jiutian.bc.sso.domain.collection.model;

import cec.jiutian.bc.sso.domain.collection.proxy.WebApiLogCollectionProxy;
import cec.jiutian.core.handler.SqlChoiceFetchHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.log.AsycLog.AsyncLog;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.VL;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "fd_web_api_log_collection_view")
@FabosJson(name = "接口日志", dataProxy = WebApiLogCollectionProxy.class,
        orderBy = "WebApiLogCollection.createTime desc",
        power = @Power(add = false, delete = false, edit = false, export = true, importable = false))
@FabosJsonI18n
@Getter
@Setter
//@SkipMetadataScanning(canOnlyBeScannedBy = "fabos-base-starter")
public class WebApiLogCollection extends AsyncLog {

    @FabosJsonField(
            views = @View(title = "应用"),
            edit = @Edit(title = "应用", notNull = true, search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "ubp",
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select system_code,name from fd_application where (ubp_flag = false or system_code = 'ubp')", "5000"}
                    )
            )
    )
    private String systemCode;

    @FabosJsonField(
            views = @View(title = "操作模块"),
            edit = @Edit(title = "操作模块", notNull = true, search = @Search(vague = true))
    )
    private String opModule;

    @FabosJsonField(
            views = @View(title = "操作路径"),
            edit = @Edit(title = "操作路径", notNull = true, search = @Search(vague = true))
    )
    private String path;

    @FabosJsonField(
            views = @View(title = "操作方法"),
            edit = @Edit(title = "操作方法", notNull = true, search = @Search)
    )
    private String method;

    @FabosJsonField(
            views = @View(title = "请求参数", toolTip = true),
            edit = @Edit(title = "请求参数")
    )
    @Column(columnDefinition = "text")
    private String requestParam;

    @FabosJsonField(
            views = @View(title = "行为分类", show = false),
            edit = @Edit(title = "行为分类", notNull = true,
                    type = EditType.CHOICE,
                    show = false,
                    choiceType = @ChoiceType(vl = {@VL(label = "用户行为", value = "user")
                            , @VL(label = "系统行为", value = "sys")}))
    )
    private String actionType;

    @FabosJsonField(
            views = @View(title = "操作结果"),
            edit = @Edit(title = "操作结果", notNull = true, search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(vl = {@VL(label = "成功", value = "succeed")
                            , @VL(label = "失败", value = "failed")}))
    )
    private String result;

    @FabosJsonField(
            views = @View(title = "操作级别", show = false),
            edit = @Edit(title = "操作级别", show = false)
    )
    private Integer opLevel;

    @FabosJsonField(
            views = @View(title = "返回明细", toolTip = true),
            edit = @Edit(title = "返回明细", notNull = true)
    )
    @Column(columnDefinition = "text")
    private String response;

    @FabosJsonField(
            views = @View(title = "操作人"),
            edit = @Edit(title = "操作人", notNull = true, search = @Search(vague = true))
    )
    private String username;

    @FabosJsonField(
            views = @View(title = "用户id", show = false),
            edit = @Edit(title = "用户id", notNull = true, show = false)
    )
    private String userId;

    @FabosJsonField(
            views = @View(title = "用户角色", toolTip = true),
            edit = @Edit(title = "用户角色", notNull = true)
    )
    private String roles;

    @FabosJsonField(
            views = @View(title = "IP"),
            edit = @Edit(title = "IP", notNull = true, search = @Search(vague = true))
    )
    private String ip;

    @FabosJsonField(
            views = @View(title = "用户类型", show = false),
            edit = @Edit(title = "用户类型", show = false)
    )
    private String userType;

    @FabosJsonField(
            views = @View(title = "操作时间", type = ViewType.text),
            edit = @Edit(title = "操作时间", notNull = true,
                    type = EditType.DATE_TIME_RANGE)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime opTime;

}
