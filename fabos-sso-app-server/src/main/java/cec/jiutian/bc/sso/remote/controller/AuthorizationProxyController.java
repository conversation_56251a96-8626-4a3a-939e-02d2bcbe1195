package cec.jiutian.bc.sso.remote.controller;


import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.service.ApplicationService;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.core.frame.module.R;
import cn.hutool.core.codec.Base64;
import kong.unirest.HttpResponse;
import kong.unirest.JsonNode;
import kong.unirest.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

import static cec.jiutian.core.frame.constant.FabosJsonRestPath.FABOS_API;

/**
 * <AUTHOR>
 * @time 2025-03-11 09:48
 */

@RestController
@RequestMapping(FABOS_API)
@Slf4j
public class AuthorizationProxyController {

    private final ApplicationService applicationService;

    //    @Value("${server.port}")
    private static final String authHost = "127.0.0.1";
    @Value("${server.port}")
    private String port;
    @Value("${app.id}")
    // should be ***-ubp
    private String applicationId;

    public AuthorizationProxyController(ApplicationService applicationService) {
        this.applicationService = applicationService;
    }

    /**
     * 接收code，生成access和refresh token
     */
    @GetMapping("/getOAuth2Params")
    public R<Map<String, Object>> getOAuth2Params(@RequestParam("code") String code) {
        // step 0: request a code
        log.info("Authorization code is: {}", code);
        Application application = applicationService.findByServiceCode(applicationId);
        String clientId = application.getClientId();
        String clientSecret = application.getClientSecret();
        String redirectUri = application.getRedirectUri();

        // step 1: request access token and refresh token
        Map<String, Object> authParams = performOauth2Token(clientId, clientSecret,redirectUri, code);
        // step 2: return data
        return R.ok(authParams);
    }

    /**
     * 刷新 token
     */
    @GetMapping("/refreshToken")
    public R<Map<String, Object>> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        log.info("RefreshToken is: {}", refreshToken);
        Application application = applicationService.findByServiceCode(applicationId);
        String clientId = application.getClientId();
        String clientSecret = application.getClientSecret();
        JsonNode authParams = performTokenRefreshing(clientId, clientSecret, refreshToken);
        return R.ok(authParams.getObject().toMap());
    }

    private JsonNode performTokenRefreshing(String clientId, String clientSecret, String refreshToken) {
        String encodedAuth = Base64.encode(clientId + SessionKey.COLON + clientSecret);
        HttpResponse<JsonNode> response = Unirest.post("http://" + authHost + SessionKey.COLON + port + "/oauth2/token")
                .header("Authorization", "Basic " + encodedAuth)
                .field("grant_type", "refresh_token")
                .field("refresh_token", refreshToken)
                .connectTimeout(0)
                .asJson();
        return response.getBody();
    }


    private Map<String, Object> performOauth2Token(String clientId, String clientSecret, String redirect, String code) {
        String encodedAuth = Base64.encode(clientId + SessionKey.COLON + clientSecret);
        HttpResponse<JsonNode> response = Unirest.post("http://" + authHost + SessionKey.COLON + port + "/oauth2/token")
                .header("Authorization", "Basic " + encodedAuth)
                .field("code", code)
                .field("grant_type", "authorization_code")
                .field("redirect_uri", redirect)
                .field("scope", "all")
                .field("client_id", clientId)
                .connectTimeout(0)
                .asJson();
        if (Objects.nonNull(response)
                && response.isSuccess()
                && Objects.nonNull(response.getBody().getObject())) {
            return response.getBody().getObject().toMap();
        }else {
            return null;
        }
    }
}

