package cec.jiutian.bc.sso.repository;

import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.remote.dto.TodoCountByApp;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TodoItemRepository extends JpaRepository<TodoItem, String> {

    /**
     * 根据用户名查询待办事项，按创建时间倒序排列
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account and t.application.id = :applicationId ORDER BY t.createTime DESC")
    Page<TodoItem> findByUserUsernameAndApplicationIdOrderByCreatedAtDesc(String account, String applicationId, Pageable pageable);

    /**
     * 根据用户名和状态查询待办事项
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account AND t.currentStatus = :currentStatus ORDER BY t.createTime DESC")
    Page<TodoItem> findByUserUsernameAndStatus(String account, String currentStatus,Pageable pageable);

    /**
     * 根据应用ID和业务标识查询待办事项
     */
    @Query("SELECT t FROM TodoItem t WHERE t.businessKey = :businessKey")
    Optional<TodoItem> findByBusinessKey(String businessKey);

    /**
     * 统计用户的待处理事项数量
     */
    @Query("SELECT COUNT(t) FROM TodoItem t WHERE t.user.account = :account AND t.currentStatus = 'Pending'")
    long countPendingTodosByUsername(String account);

    /**
     * 查询指定时间段内的待办事项
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account AND t.createTime BETWEEN :startDate AND :endDate ORDER BY t.createTime DESC")
    List<TodoItem> findByUserUsernameAndDateRange(String account, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 查询用户在指定应用的待办事项
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account AND t.application.id = :applicationId ORDER BY t.createTime DESC")
    List<TodoItem> findByUserUsernameAndApplicationId(String account, String applicationId);

    /**
     * 查询已过期的待办事项（如果有截止时间字段）
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account AND t.currentStatus = 'Pending' AND t.dueDate < :now")
    List<TodoItem> findExpiredTodos(String account, LocalDateTime now);

    /**
     * 批量更新待办事项状态
     */
    @Modifying
    @Query("UPDATE TodoItem t SET t.currentStatus = :currentStatus, t.updateTime = :completedAt WHERE t.id IN :ids")
    int updateTodoStatus(List<String> ids, String currentStatus, LocalDateTime completedAt);

    /**
     * 删除已完成的待办事项
     */
    @Modifying
    @Query("DELETE FROM TodoItem t WHERE t.currentStatus = 'Completed' AND t.updateTime < :beforeDate")
    int deleteCompletedTodos(LocalDateTime beforeDate);

    /**
     * 分页查询用户的待办事项
     */
    @Query("SELECT t FROM TodoItem t WHERE t.user.account = :account")
    Page<TodoItem> findByUserUsername(String account, Pageable pageable);

    /**
     * 按应用分组统计待办事项数量
     */
    @Query("SELECT new cec.jiutian.bc.sso.remote.dto.TodoCountByApp(t.application.name, COUNT(t)) " +
            "FROM TodoItem t WHERE t.user.account = :account AND t.currentStatus = :currentStatus " +
            "GROUP BY t.application.name")
    List<TodoCountByApp> countTodosByApplication(String account, String currentStatus);
}
