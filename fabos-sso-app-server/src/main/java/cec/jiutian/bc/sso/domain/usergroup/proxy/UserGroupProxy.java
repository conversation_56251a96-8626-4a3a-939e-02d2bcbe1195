package cec.jiutian.bc.sso.domain.usergroup.proxy;

import cec.jiutian.bc.sso.domain.usergroup.model.UserGroup;
import cec.jiutian.bc.sso.enumeration.YesOrNoEnum;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class UserGroupProxy implements DataProxy<UserGroup> {

    @Resource
    private UrmCommandService urmCommandService;

    @Override
    public void beforeAdd(UserGroup userGroup) {
        // 设置默认的内置标识权限为false
        userGroup.setFlag(YesOrNoEnum.Enum.N.name());
    }

    @Override
    public void afterUpdate(UserGroup userGroup) {
        // 在系统换组，组换用户时强行清理所有的token,无论在不在线
        urmCommandService.removeAllTokens();
    }
}
