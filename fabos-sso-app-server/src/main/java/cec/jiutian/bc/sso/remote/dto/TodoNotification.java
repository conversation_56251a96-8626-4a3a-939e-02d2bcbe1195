package cec.jiutian.bc.sso.remote.dto;

import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.enumeration.ToDoStatusTypeEnum;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

@Schema(description = "待办事项通知")
@Data
public class TodoNotification {
    /**
     * 工号
     */
    @Schema(description = "用户工号", example = "10001")
    private String account;
    /**
     * 待办事项标题
     */
    @Schema(description = "待办事项标题", example = "审批申请")
    private String title;
    /**
     * 待办事项描述
     */
    @Schema(description = "待办事项描述", example = "请审批XX采购申请")
    private String description;
    /**
     * 应用业务标识
     */
    @Schema(description = "业务标识", example = "PO-2024-001")
    private String businessKey;
    /**
     * 应用业务URL
     */
/*    @Schema(description = "处理URL", example = "https://example.com/approve/123")
    private String processUrl;*/


    public TodoItem toTodoItem() {
        TodoItem item = new TodoItem();
        item.setTitle(title);
        item.setDescription(description);
        item.setBusinessKey(businessKey);
   /*     item.setProcessUrl(processUrl);*/
        item.setCurrentStatus(ToDoStatusTypeEnum.Enum.Pending.name());
        item.setCreateTime(LocalDateTime.now());
        item.setSubmitTime(LocalDateTime.now());
        item.setDueDate(LocalDateTime.now().plusDays(7));
        return item;
    }
}
