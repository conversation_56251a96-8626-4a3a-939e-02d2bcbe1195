package cec.jiutian.bc.sso.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnProperty(name = "spring.rabbitmq.host")
public class SsoRabbitMQConfig {

    @Bean
    public Queue todoNotificationQueue() {
        return new Queue("portal.notifications.todo", true);
    }

    @Bean
    public TopicExchange notificationExchange() {
        return new TopicExchange("portal.notifications");
    }

    @Bean
    public Binding todoNotificationBinding(Queue todoNotificationQueue, TopicExchange notificationExchange) {
        return BindingBuilder.bind(todoNotificationQueue)
                .to(notificationExchange)
                .with("todo.*");
    }

    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(jsonMessageConverter());
        return template;
    }
}
