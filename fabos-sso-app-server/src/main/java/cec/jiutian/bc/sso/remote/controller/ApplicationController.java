
package cec.jiutian.bc.sso.remote.controller;


import cec.jiutian.bc.sso.remote.dto.ApplicationLoginDto;
import cec.jiutian.bc.sso.repository.ApplicationRepository;
import cec.jiutian.bc.sso.service.ApplicationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.List;

@RestController
@RequestMapping("/fabos-api")
public class ApplicationController {


    private ApplicationService applicationService;

    private ApplicationRepository applicationRepository;
//    private PermissionService permissionService;


    public ApplicationController(ApplicationService applicationService, ApplicationRepository applicationRepository) {
        this.applicationService = applicationService;
        this.applicationRepository = applicationRepository;
    }

    @GetMapping("/getLoginAbleApplications")
    public ResponseEntity<List<ApplicationLoginDto>> getLoginAbleApplications(Principal principal) {
        List<ApplicationLoginDto> applications = applicationService.getLoingAbleApplications();
        return ResponseEntity.ok(applications);
    }

//    @GetMapping("/applications/{clientId}")
//    public ResponseEntity<Application> getApplication(@PathVariable String clientId, Principal principal) {
////        if (!permissionService.hasApplicationAccess(principal.getName(), clientId)) {
////            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
////        }
//        return ResponseEntity.ok(applicationService.findByClientId(clientId));
//    }
//
//    @PostMapping("/applications")
//    @PreAuthorize("hasAuthority('MANAGE_APPLICATION')")
//    public ResponseEntity<Application> registerApplication(@RequestBody Application application) {
//        String clientId = UUID.randomUUID().toString();
//        String clientSecret = UUID.randomUUID().toString();
//
//        application.setClientId(clientId);
//        application.setClientSecret(clientSecret);
//
//        Application savedApplication = applicationRepository.save(application);
//
//        RegisteredClient registeredClient = RegisteredClient.withId(savedApplication.getId().toString())
//                .clientId(clientId)
//                .clientSecret(clientSecret)
//                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
//                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
//                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
//                .redirectUri(savedApplication.getRedirectUri())
//                .scope(OidcScopes.OPENID)
//                .scope(OidcScopes.PROFILE)
//                .scope("read")
//                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(true).build())
//                .build();
//
//        registeredClientRepository.save(registeredClient);
//
//        return ResponseEntity.ok(savedApplication);
//    }
//
//    @GetMapping("/applications")
//    @PreAuthorize("hasAuthority('VIEW_APPLICATION')")
//    public ResponseEntity<List<Application>> getAllApplications() {
//        List<Application> applications = applicationRepository.findAll();
//        return ResponseEntity.ok(applications);
//    }
//
//    @GetMapping("/applications/{id}")
//    @PreAuthorize("hasAuthority('VIEW_APPLICATION')")
//    public ResponseEntity<Application> getApplication(@PathVariable String id) {
//        Optional<Application> application = applicationRepository.findById(id);
//        return application.map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @GetMapping("/applications/{id}/log-url")
//    @PreAuthorize("hasAuthority('ACCESS_APPLICATION_LOG')")
//    public ResponseEntity<String> getApplicationLogUrl(@PathVariable String id) {
//        Optional<Application> application = applicationRepository.findById(id);
//        return application.map(app -> ResponseEntity.ok(app.getLogUrl()))
//                .orElse(ResponseEntity.notFound().build());
//    }

//    @GetMapping
//    public String listApplications(Model model, @AuthenticationPrincipal UserDetails userDetails) {
//        List<Application> applications = applicationService.getUserApplications(userDetails.getUsername());
//        model.addAttribute("applications", applications);
//        return "applications/list";
//    }
//
//    @GetMapping("/add")
//    @PreAuthorize("hasAuthority('ADMIN')")
//    public String showAddForm(Model model) {
//        model.addAttribute("application", new Application());
//        return "applications/add";
//    }
//
//    @PostMapping("/add")
//    @PreAuthorize("hasAuthority('ADMIN')")
//    public String addApplication(@ModelAttribute Application application, RedirectAttributes redirectAttributes) {
//        try {
//            Application savedApplication = applicationService.registerApplication(application);
//            redirectAttributes.addFlashAttribute("message", "Application registered successfully. Registration Code: "
//                    + savedApplication.getRegistrationCode());
//            return "redirect:/applications";
//        } catch (Exception e) {
//            redirectAttributes.addFlashAttribute("error", "Failed to register application: " + e.getMessage());
//            return "redirect:/applications/add";
//        }
//    }
//
//    @PostMapping("/{id}/activate")
//    public String activateApplication(@PathVariable Long id, @RequestParam String registrationCode,
//                                      RedirectAttributes redirectAttributes) {
//        try {
//            applicationService.activateApplication(id, registrationCode);
//            redirectAttributes.addFlashAttribute("message", "Application activated successfully");
//        } catch (Exception e) {
//            redirectAttributes.addFlashAttribute("error", "Failed to activate application: " + e.getMessage());
//        }
//        return "redirect:/applications";
//    }
}

