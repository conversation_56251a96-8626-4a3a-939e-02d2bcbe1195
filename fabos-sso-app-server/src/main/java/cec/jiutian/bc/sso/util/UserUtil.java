package cec.jiutian.bc.sso.util;

import cec.jiutian.common.context.UserContext;
import org.springframework.stereotype.Component;

@Component
public class UserUtil {
    public static String THREAD_LOCAL = String.valueOf(ThreadLocal.withInitial(() -> "superUser"));

    public void setUserAccount() {
        UserContext.CurrentUser current = UserContext.get();
        // 用户信息存在THREAD_LOCAL中 用于后续拿当前登录用户信息(UserContext第一次拿不到用户信息)
        if (current != null) {
            THREAD_LOCAL = current.getAccount();
        }
    }
}
