package cec.jiutian.bc.sso.service;

import cec.jiutian.bc.sso.repository.UserRepository;
import jakarta.annotation.Resource;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CustomUserDetailsService implements UserDetailsService {
    @Resource
    private UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 从数据库或其他存储中加载用户信息
        Optional<cec.jiutian.bc.urm.domain.user.entity.User> user = userRepository.findByAccount(username);
        if (user == null) {
            throw new UsernameNotFoundException("User not found: " + username);
        }
        cec.jiutian.bc.urm.domain.user.entity.User currentUser = user.get();
        // 返回 UserDetails 对象
        return User.builder()
                .username(currentUser.getAccount())
                .password(currentUser.getPassword())
                .roles("read")
                .build();
    }
}