package cec.jiutian.bc.sso.remote.controller;

import cec.jiutian.bc.sso.config.MonitorConstant;
import cec.jiutian.bc.sso.remote.dto.Server;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(MonitorConstant.REST_MONITOR + "/server.html")
public class MonitorServerController {


    @GetMapping("/info")
    public Server info() {
        return new Server();
    }



}
