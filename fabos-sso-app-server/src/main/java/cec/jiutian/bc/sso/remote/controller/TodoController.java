package cec.jiutian.bc.sso.remote.controller;

import cec.jiutian.bc.sso.service.ApplicationService;
import cec.jiutian.bc.sso.service.TodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import cec.jiutian.bc.sso.remote.dto.TodoSummary;
import cec.jiutian.bc.sso.domain.todo.model.TodoItem;
import cec.jiutian.bc.sso.domain.application.model.Application;

import java.util.List;

@Controller
@RequestMapping("/todos")
@PreAuthorize("isAuthenticated()")
@Slf4j
public class TodoController {

    @Autowired
    private TodoService todoService;

    @Autowired
    private ApplicationService applicationService;

    /**
     * 待办事项列表页面
     */
/*    @GetMapping
    public String listTodos(Model model,
                            @AuthenticationPrincipal UserDetails userDetails,
                            @RequestParam(defaultValue = "0") int page,
                            @RequestParam(defaultValue = "10") int size) {
        // 获取用户待办事项摘要
        TodoSummary summary = todoService.getUserTodoSummary(userDetails.getUsername());
        model.addAttribute("todoSummary", summary);

        // 获取分页待办事项
        Page<TodoItem> todos = todoService.getUserTodosPaged(
                userDetails.getUsername(),
                page,
                size,
                "createTime"
        );
        model.addAttribute("todos", todos);

        // 获取用户的应用列表（用于筛选）
        List<Application> applications = applicationService.getUserApplications(userDetails.getUsername());
        model.addAttribute("applications", applications);

        return "todos/list";
    }*/
}
