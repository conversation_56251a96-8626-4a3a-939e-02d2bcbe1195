package cec.jiutian.bc.sso.domain.todo.model;

import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.domain.todo.proxy.TodoProxy;
import cec.jiutian.bc.sso.enumeration.ToDoStatusTypeEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@FabosJson(
        name = "待办事项",
        orderBy = "TodoItem.createTime desc",
        dataProxy = TodoProxy.class,
        power = @Power(add = false, edit = false, delete = false),
        rowOperation = {
                @RowOperation(
                        code = "TodoItem@JUMP",
                        mode = RowOperation.Mode.SINGLE,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.JUMP,
                        callHint = "确定跳转到应用页面吗？",
                        title = "跳转到应用",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "TodoItem@JUMP"
                        )
                )
        }
)
@Table(name = "fd_todo_item"
)
@Entity
@Getter
@Setter
@TemplateType(type = "usual")
public class TodoItem extends MetaModel {

    @ManyToOne
    @JoinColumn(name = "application_id")
    @FabosJsonField(
            views = @View(title = "应用",show = false),
            edit = @Edit(title = "应用", show = false)
    )
    private Application application;

    @FabosJsonField(
            views = @View(title = "客户端ID", show = false),
            edit = @Edit(title = "客户端ID", show = false)
    )
    private String clientId;

    @FabosJsonField(
            views = @View(title = "待办事项"),
            edit = @Edit(title = "待办事项",search = @Search())
    )
    private String title;

    @FabosJsonField(
            views = @View(title = "待办描述"),
            edit = @Edit(title = "待办描述", type = EditType.TEXTAREA)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "应用业务标识"),
            edit = @Edit(title = "应用业务标识", search = @Search(), type = EditType.AUTO)
    )
    private String businessKey;

    @FabosJsonField(
            views = @View(title = "应用待办Uri", show = false),
            edit = @Edit(title = "应用待办Uri", show = false)
    )
    private String redirectUri;

    @FabosJsonField(
            views = @View(title = "客户端名称"),
            edit = @Edit(title = "客户端名称", show = false,search = @Search())
    )
    private String clientName;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", notNull = true, show = false, type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = ToDoStatusTypeEnum.class)
            )
    )
    private String currentStatus;

    @FabosJsonField(
            views = @View(title = "提交人员", column = "name"),
            edit = @Edit(title = "提交人员",search = @Search())
    )
    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "提交时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "提交时间")
    )
    private LocalDateTime submitTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "截止时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "截止时间")
    )
    private LocalDateTime dueDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "处理时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "处理时间")
    )
    private LocalDateTime dealTime;

}
