package cec.jiutian.bc.sso.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;


public class ToDoStatusTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue(), data.getColor(), ""));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Pending("未处理", "active"),
        Completed("已处理", "inactive");

        private final String value;
        private final String color;
    }
}
