package cec.jiutian.bc.sso.inbound.remote.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.sso.service.ApplicationService;
import cec.jiutian.common.exception.MesErrorCodeException;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import org.apache.seata.core.context.RootContext;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.seata.core.exception.TransactionException;
import org.apache.seata.tm.api.GlobalTransactionContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2025-03-19 15:58
 */

@RestController
@Slf4j
public class ApplicationFeign {
    private final ApplicationService applicationService;
    private final FabosJsonDao fabosJsonDao;

    public ApplicationFeign(ApplicationService applicationService, FabosJsonDao fabosJsonDao) {
        this.applicationService = applicationService;
        this.fabosJsonDao = fabosJsonDao;
    }

    @PostMapping("/checkIfSystemAvailableForUser")
    public Boolean checkIfSystemAvailableForUser(@RequestBody(required = true) Map<String, String> params) {
        if (StringUtils.isNotBlank(params.get("svsCode")) && StringUtils.isNotBlank(params.get("userName"))) {
            return applicationService.checkSystemAvailableForUser(params.get("svsCode"), params.get("userName"));
        }
        return false;
    }

    @PostMapping(FabosJsonRestPath.FABOS_REMOTE_API + "/globalTest")
    @Transactional
    public RemoteCallResult<?> globalTest(@RequestBody(required = true) Map<String, String> params) {
        try {
            log.error("SSO REMOTED FUNCTION was called, xid: " + RootContext.getXID());
            String sql = "INSERT INTO fd_dict (id, oid, code) VALUES ('s123', '0', 'before');";
            Query query = fabosJsonDao.getEntityManager().createNativeQuery(sql);
            query.executeUpdate();
            log.warn("SQL 123 was executed======================");
            if (StringUtils.isNotBlank(params.get("sso"))) {
                throw new FabosJsonApiErrorTip("trigger 'sso' is consumed, throwing a new Exception in sso");
            }
//            if (Objects.nonNull(params.get("frsso"))) {
//                GlobalTransactionContext.reload(RootContext.getXID()).rollback();
//                log.warn("Forced rolled back global tx: \n {}", RootContext.getXID());
//            }
            String sql2 = "INSERT INTO fd_dict (id, oid, code) VALUES ('s456', '0', 'after');";
            Query query2 = fabosJsonDao.getEntityManager().createNativeQuery(sql2);
            query2.executeUpdate();
            log.warn("SQL 456 was executed======================");
            return new RemoteCallResult<>();

        } catch (Exception e) {
            log.error("Caught an exception: {}", e.getMessage());
            return new RemoteCallResult<>(e);
        }
    }

//    @PostMapping(FabosJsonRestPath.FABOS_TASK_API + "/taskTest")
//    @Transactional
//    public RemoteCallResult<?> taskTest(@RequestBody Map<String, String> params) {
//        params.put("sso", "pong");
//        return RemoteCallResult.success(params);
//    }

    @PostMapping(FabosJsonRestPath.FABOS_TASK_API + "/taskTest")
    public RemoteCallResult<?> taskTest(@RequestBody Map<String, String> params) {
        return RemoteCallResult.success(params);
    }


}

