package cec.jiutian.bc.sso.domain.application.model;

import cec.jiutian.bc.sso.domain.application.handler.ApplicationHandler;
import cec.jiutian.bc.sso.domain.application.proxy.ApplicationProxy;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "应用门户",
        dataProxy = ApplicationProxy.class,
        orderBy = "Application.createTime desc",
        filter = @Filter(value = "ubpFlag = false"),
        rowOperation = {

                @RowOperation(
                        code = "Application@RESTART",
                        mode = RowOperation.Mode.SINGLE,
                        popupType = RowOperation.PopupType.DEFAULT,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.DEFAULT,
                        callHint = "确定重新发布此系统？",
                        title = "重新发布",
                        operationHandler = ApplicationHandler.class,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "Application@RESTART"
                        )
                ),
        })
@Entity
@Getter
@Setter
@Table(name = "fd_application",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
public class Application extends ApplicationSuper {
}
