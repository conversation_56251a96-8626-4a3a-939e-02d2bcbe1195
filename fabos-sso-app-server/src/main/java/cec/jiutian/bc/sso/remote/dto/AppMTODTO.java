package cec.jiutian.bc.sso.remote.dto;


import cec.jiutian.bc.sso.remote.mto.ApplicationMTO;
import lombok.Data;

@Data
public class AppMTODTO {

    private String systemName;

    private String systemCode;

    private String validFlag;

    private String serviceCode;


    private String systemDescription;

    public AppMTODTO(ApplicationMTO applicationMTO) {
        this.systemName = applicationMTO.getName();
        this.systemCode = applicationMTO.getSystemCode();
        this.validFlag = applicationMTO.getValidFlag();
        this.serviceCode = applicationMTO.getServiceCode();
        this.systemDescription = applicationMTO.getDescription();
    }
}