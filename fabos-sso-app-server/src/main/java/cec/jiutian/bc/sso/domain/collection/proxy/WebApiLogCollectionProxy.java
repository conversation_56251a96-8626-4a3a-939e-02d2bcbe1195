package cec.jiutian.bc.sso.domain.collection.proxy;

import cec.jiutian.bc.sso.domain.collection.model.WebApiLogCollection;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WebApiLogCollectionProxy implements DataProxy<WebApiLogCollection> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        // 判断conditions中是否包含systemCode	的查询条件，未包含则提示需选择系统后查询
        boolean found = false;
        for (Condition condition : conditions) {
            if (condition.getKey().equals("systemCode") && condition.getValue() != null) {
                found = true;
                break;
            }
        }
        if (!found) {
            throw new FabosJsonApiErrorTip("请先选择系统");
        }
        return DataProxy.super.beforeFetch(conditions);
    }
}
