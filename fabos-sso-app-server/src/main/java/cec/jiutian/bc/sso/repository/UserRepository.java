package cec.jiutian.bc.sso.repository;

import cec.jiutian.bc.urm.domain.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {

    Optional<User> findByAccount(String account);

    Optional<User> findByName(String name);
}
