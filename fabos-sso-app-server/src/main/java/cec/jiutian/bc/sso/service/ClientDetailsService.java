package cec.jiutian.bc.sso.service;


import cec.jiutian.bc.sso.repository.ApplicationRepository;
import cec.jiutian.bc.sso.util.ClientUtil;
import jakarta.annotation.Resource;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;



@Service
public class ClientDetailsService {
    @Resource
    private ApplicationRepository applicationRepository;

    public List<RegisteredClient> loadClients() {
        return applicationRepository.findAsClients().stream()
                .map(application ->  ClientUtil.getClient(application))
                .collect(Collectors.toList());
    }


}