package cec.jiutian.bc.sso.domain.application.proxy;

import cec.jiutian.bc.sso.domain.application.model.MyApplication;
import cec.jiutian.bc.sso.util.CreateHqlUtil;
import cec.jiutian.bc.sso.util.UserUtil;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Component
public class MyApplicationProxy implements DataProxy<MyApplication> {
    @Resource
    private CreateHqlUtil createHqlUtil;
    @Resource
    private UserUtil userUtil;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        return createHqlUtil.createHql("app");
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        userUtil.setUserAccount();
    }
}
