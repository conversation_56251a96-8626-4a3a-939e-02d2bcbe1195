package cec.jiutian.bc.sso.remote.mto;

import cec.jiutian.bc.sso.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;


@Entity
@Getter
@Setter
@Table(name = "fd_application",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
public class ApplicationMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "应用名称"),
            edit = @Edit(title = "应用名称", search = @Search(), notNull = true, inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "系统编码"),
            edit = @Edit(title = "系统编码", inputType = @InputType(length = 40))
    )
    private String systemCode;

    @FabosJsonField(
            views = @View(title = "是否生效"),
            edit = @Edit(title = "是否生效",
                    notNull = true,
                    defaultVal = "Y",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)
            )
    )
    private String validFlag;

    @FabosJsonField(
            views = @View(title = "服务编码"),
            edit = @Edit(title = "服务编码",
                    inputType = @InputType(length = 40))
    )
    private String serviceCode;

    @FabosJsonField(
            views = @View(title = "应用描述"),
            edit = @Edit(title = "应用描述", inputType = @InputType())
    )
    private String description;

    @Comment("标记当前系统为基础平台，查询时不返回")
    @FabosJsonField(
            views = @View(title = "基础平台标识", show = false),
            edit = @Edit(title = "基础平台标识", show = false)
    )
    private Boolean ubpFlag ;


}
