package cec.jiutian.bc.sso.domain.application.handler;

import cec.jiutian.api.file.utils.FileUtil;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.provider.EcsMessageProvider;
import cec.jiutian.bc.sso.domain.application.model.Application;
import cec.jiutian.bc.sso.domain.application.model.ApplicationSuper;
import cec.jiutian.bc.sso.service.SystemRestartService;
import cec.jiutian.bc.urm.dto.MenuData;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.bc.file.service.FileService;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.view.fabosJson.invoke.UserButtonInvoke;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025-07-28 09:46
 */

@Slf4j
@Component
public class ApplicationHandler implements OperationHandler<Application, Void> {

    @Resource
    private FileService fileService;

    @Resource
    private SystemRestartService systemRestartService;

    @Override
    public String exec(List<Application> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data) && data.get(0) != null) {
            Application application = data.get(0);
            
            try {
                // 获取系统代码
                String systemCode = application.getServiceCode();
                
                if (StringUtils.isBlank(systemCode)) {
                    return "alert('系统代码不能为空')";
                }
                
                // 调用重启服务
                String result = systemRestartService.restartSystem(systemCode);
                
                log.info("系统 {} 重启操作完成", systemCode);
                return "alert('" + result + "')";
                
            } catch (Exception e) {
                log.error("重启系统失败", e);
                return "alert('重启失败: " + e.getMessage() + "')";
            }
        }
        return "alert('请选择要重启的系统')";
    }

}
