package cec.jiutian.bc.sso.domain.application.model;

import cec.jiutian.bc.sso.domain.application.proxy.MyApplicationProxy;
import cec.jiutian.bc.sso.domain.usergroup.model.UserGroup;
import cec.jiutian.bc.sso.enumeration.CurrentStateEnum;
import cec.jiutian.bc.sso.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.core.view.fabosJson.TransferModel;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Card;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@FabosJson(
        name = "我的应用",
        orderBy = "MyApplication.createTime desc",
        power = @Power(add = false, edit = false, delete = false, viewDetails = false),
        dataProxy = MyApplicationProxy.class,
        cardView = @Card(enable = true,titleField = "name", showTitle = true,galleryField = "icon", redirectField = "homeUrl")
)
@Entity
@Getter
@Setter
@TransferModel(baseModel = "Application")
@Table(name = "fd_application",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@InheritStrategy(
        dataProxyFlag = false,
        excludeParentRowOperation = {"Application@JUMP"}
)
public class MyApplication extends ApplicationSuper {

}
