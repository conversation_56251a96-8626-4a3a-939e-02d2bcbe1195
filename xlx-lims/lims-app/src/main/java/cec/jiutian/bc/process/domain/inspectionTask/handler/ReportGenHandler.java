package cec.jiutian.bc.process.domain.inspectionTask.handler;

import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionManyResult;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTask;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetail;
import cec.jiutian.bc.process.domain.inspectionTask.model.InspectionTaskDetailQuota;
import cec.jiutian.bc.process.domain.inspectionTask.service.InspectionTaskService;
import cec.jiutian.bc.process.domain.reportTemplate.model.LmReportTemplate;
import cec.jiutian.bc.process.domain.reportTemplate.model.LmReportTemplateDetail;
import cec.jiutian.bc.process.enumeration.InspectionTaskStateEnum;
import cec.jiutian.bc.process.enumeration.PrepareStatusEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ReportGenHandler implements OperationHandler<InspectionTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private InspectionTaskService  inspectionTaskService;

    @Override
    @Transactional
    public String exec(List<InspectionTask> data, Void modelObject, String[] param) {
        InspectionTask inspectionTask = data.get(0);
        if(!InspectionTaskStateEnum.Enum.COMPLETE.name().equals(inspectionTask.getTaskStatus())){
            throw new FabosJsonApiErrorTip("检测任务未完成");
        }
        if(YesOrNoStatus.YES.getValue().equals(inspectionTask.getIsReport())){
            throw new FabosJsonApiErrorTip("该检测任务已生成报告");
        }
        if(CollectionUtils.isEmpty(inspectionTask.getInspectionTaskDetails())){
            throw new FabosJsonApiErrorTip("该检测任务无检测项");
        }

        this.genReport(inspectionTask);
        inspectionTask.setIsReport(YesOrNoStatus.YES.getValue());
        fabosJsonDao.update(inspectionTask);
        return "success";
    }
    //生成报告
    public void genReport(InspectionTask inspectionTask) {
        LmReportTemplate lmReportTemplate = new LmReportTemplate();
        lmReportTemplate.setGeneralCode(inspectionTask.getGeneralCode());
        lmReportTemplate.setSampleTaskNo(inspectionTask.getSampleTaskNo());
        lmReportTemplate.setMaterialName(inspectionTask.getMaterialName());
        lmReportTemplate.setMaterialCode(inspectionTask.getMaterialCode());
        lmReportTemplate.setMaterialBatchNo(inspectionTask.getMaterialBatchNo());
        lmReportTemplate.setTotalQuantity(inspectionTask.getTotalQuantity());
        lmReportTemplate.setCheckType(inspectionTask.getCheckType());
        lmReportTemplate.setLabName(inspectionTask.getLabName());
        lmReportTemplate.setExpLabName(inspectionTask.getExpLabName());
        lmReportTemplate.setIsUrgent(inspectionTask.getIsUrgent());
        lmReportTemplate.setFinishTime(inspectionTask.getFinishTime());
        lmReportTemplate.setIsTimely(inspectionTask.getIsTimely());
        lmReportTemplate.setInspectionPerson(inspectionTask.getInspectionPerson().getName());
//        lmReportTemplate.setOrigInspectionResult(inspectionTask.getInspectionResult());
//        lmReportTemplate.setInspectionResult(inspectionTask.getInspectionResult());
        lmReportTemplate.setIsPublish(YesOrNoStatus.NO.getValue());
        lmReportTemplate.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
        //明细
        List<LmReportTemplateDetail> lmReportTemplateDetails = new ArrayList<>();
        for (InspectionTaskDetail inspectionItem : inspectionTask.getInspectionTaskDetails()) {
            List<InspectionTaskDetailQuota> inspectionTaskDetailQuotas = inspectionItem.getInspectionTaskDetailQuotas();
            if (CollectionUtils.isEmpty(inspectionTaskDetailQuotas)) {
                continue;
            }
            String testMethodName = inspectionItem.getTestMethodName();
            String testDevice = inspectionItem.getTestDevice();
            String equipArchiveCode = inspectionItem.getEquipArchiveCode();
//            String origDetailInsResult = inspectionItem.getDetailInsResult();
//            String detailInsResult = inspectionItem.getDetailInsResult();
            for (InspectionTaskDetailQuota quota : inspectionTaskDetailQuotas) {
                LmReportTemplateDetail lmReportTemplateDetail = new LmReportTemplateDetail();
                //检测项数据
                lmReportTemplateDetail.setTestMethodName(testMethodName);
                lmReportTemplateDetail.setTestDevice(testDevice);
                lmReportTemplateDetail.setEquipArchiveCode(equipArchiveCode);
//                lmReportTemplateDetail.setOrigDetailInsResult(origDetailInsResult);
//                lmReportTemplateDetail.setDetailInsResult(detailInsResult);
                //指标数据
                lmReportTemplateDetail.setName(quota.getName());
                Optional.ofNullable(quota.getInspectionValueType())
                        .ifPresent(lmReportTemplateDetail::setInspectionValueType);
                Optional.ofNullable(quota.getUnit())
                        .ifPresent(lmReportTemplateDetail::setUnit);
                Optional.ofNullable(quota.getStandardValue())
                        .ifPresent(lmReportTemplateDetail::setStandardValue);
                Optional.ofNullable(quota.getUpperValue())
                        .ifPresent(lmReportTemplateDetail::setUpperValue);
                Optional.ofNullable(quota.getLowerValue())
                        .ifPresent(lmReportTemplateDetail::setLowerValue);
                //测试结果兼容多次
                if(CollectionUtils.isNotEmpty(quota.getInspectionManyResults())){
                    //将quota.getInspectionManyResults()检测结果值inspectValue使用逗号间隔
                    String valus = quota.getInspectionManyResults().stream()
                            .map(InspectionManyResult::getInspectValue) // 提取 inspectValue
                            .filter(Objects::nonNull) // 过滤null和空值
                            .map(Objects::toString)
                            .collect(Collectors.joining(", "));
                    lmReportTemplateDetail.setInspectValue(valus);
                }else {
                    lmReportTemplateDetail.setInspectValue(quota.getInspectValue());
                }
                lmReportTemplateDetail.setOrigInspectValue(lmReportTemplateDetail.getInspectValue());
//                lmReportTemplateDetail.setInspectResult(quota.getInspectResult());
//                lmReportTemplateDetail.setOrigInspectResult(quota.getInspectResult());

                lmReportTemplateDetails.add(lmReportTemplateDetail);
            }
        }
        lmReportTemplate.setReportTemplateDetails(lmReportTemplateDetails);
        fabosJsonDao.persistAndFlush(lmReportTemplate);

    }
}
