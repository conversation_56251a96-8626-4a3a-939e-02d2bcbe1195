package cec.jiutian.bc.msa.domain.plan.model;

import cec.jiutian.bc.compare.domain.comparePlan.model.ComparePlan;
import cec.jiutian.bc.compare.enumeration.ComparePlanDetailStateEnum;
import cec.jiutian.bc.compare.enumeration.CompareSampleTypeEnum;
import cec.jiutian.bc.process.enumeration.UrgentEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "检测任务",
        orderBy = "MsaPlanDetail.createTime desc"
)
@Entity
@Getter
@Setter
@Table(name = "lm_msa_plan_detail")
public class MsaPlanDetail extends MetaModel {
    @FabosJsonField(
            views = @View(title = "样品单号"),
            edit = @Edit(title = "样品单号")
    )
    private String sampleTaskNo;

    @FabosJsonField(
            views = @View(title = "委托申请单号"),
            edit = @Edit(title = "委托申请单号")
    )
    private String inspectionTaskNo;

    // 质检标准id
    @FabosJsonField(
            views = @View(title = "质检标准id", show = false),
            edit = @Edit(title = "质检标准id", show = false)
    )
    private String inspectionStandardId;

    // 质检标准名称
    @FabosJsonField(
            views = @View(title = "质检标准"),
            edit = @Edit(title = "质检标准")
    )
    private String inspectionStandardName;

    @FabosJsonField(
            views = @View(title = "样品来源"),
            edit = @Edit(title = "样品来源")
    )
    private String sampleSource;

    @FabosJsonField(
            views = @View(title = "样品类型"),
            edit = @Edit(title = "样品类型", notNull = true, type = EditType.CHOICE, search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = CompareSampleTypeEnum.class))
    )
    private String sampleType;

    @FabosJsonField(
            views = @View(title = "是否取样"),
            edit = @Edit(title = "是否取样", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class))
    )
    private String takeSample;

    @FabosJsonField(
            views = @View(title = "检测人id", show = false),
            edit = @Edit(title = "检测人id", show = false)
    )
    private String testPersonId;

    @FabosJsonField(
            views = @View(title = "检测人"),
            edit = @Edit(title = "检测人")
    )
    private String testPerson;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ComparePlanDetailStateEnum.class))
    )
    private String detailState;

    @FabosJsonField(
            views = @View(title = "紧急度"),
            edit = @Edit(title = "紧急度", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = UrgentEnum.class), defaultVal = "N"),
            dynamicField = @DynamicField(passive = true)
    )
    private String isUrgent;

    // 备注
    @FabosJsonField(
            views = @View(title = "备注", toolTip = true),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    // 关联主表, 不展示
    @FabosJsonField(
            views = @View(title = "MSA计划", show = false,column = "msaName"),
            edit = @Edit(title = "MSA计划", show = false, type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(label = "msaName"))
    )
    @ManyToOne
    @JsonIgnoreProperties("msaPlanDetails")
    private MsaPlan msaPlan;
}
