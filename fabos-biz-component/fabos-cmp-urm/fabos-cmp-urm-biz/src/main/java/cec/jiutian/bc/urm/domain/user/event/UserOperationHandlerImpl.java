package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.view.fun.OperationHandler;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserOperationHandlerImpl implements OperationHandler<User, Void> {

    @Resource
    private UserService userService;
    @Override
    public String exec(List<User> data, Void modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }
//        if (!(curr.getRoles().contains(UserManagerTypeEnum.securityManager.getCode())||curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode()))) {
//            return "msg.error(当前登录用户无操作权限')";
//        }
        Boolean isReset = userService.resetPassword(data.get(0).getAccount());
        return isReset?"msg.success('重置成功')":"msg.error('重置失败')";
    }
}
