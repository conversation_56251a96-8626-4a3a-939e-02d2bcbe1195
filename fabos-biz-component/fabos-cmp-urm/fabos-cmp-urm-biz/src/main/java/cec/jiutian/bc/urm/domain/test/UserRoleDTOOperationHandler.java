package cec.jiutian.bc.urm.domain.test;

import cec.jiutian.view.fun.OperationHandler;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
@Slf4j
public class UserRoleDTOOperationHandler implements OperationHandler<UserRoleDTO, Void>{
    @Override
    public String exec(List<UserRoleDTO> data, Void modelObject, String[] param) {
        log.info(JSONUtil.toJsonStr(data));
        return null;
    }
}
