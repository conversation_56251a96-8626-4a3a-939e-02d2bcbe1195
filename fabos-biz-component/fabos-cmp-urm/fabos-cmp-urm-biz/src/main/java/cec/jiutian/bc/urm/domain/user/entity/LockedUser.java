package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.bc.urm.domain.user.event.LockedUserDataProxy;
import cec.jiutian.bc.urm.domain.user.event.UserLockOperationHandler;
import cec.jiutian.bc.urm.domain.user.event.UserUnlockOperationHandler;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@LinkTable
@Table(name = "fd_meta_user")
@FabosJsonI18n
@FabosJson(name = "锁定用户",
        orderBy = "LockedUser.createTime desc",
        dataProxy = LockedUserDataProxy.class,
        power = @Power(add = false, edit = false, delete = false, export = false, importable = false),
        rowOperation = {
                @RowOperation(
                        operationHandler = UserUnlockOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定是否要解锁用户，允许用户登录？",
                        code = "User@UNLOCK",
                        title = "用户解锁",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "User@UNLOCK"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        code = "User@LOCK",
                        mode = RowOperation.Mode.SINGLE,
                        type = RowOperation.Type.POPUP,
                        ifExpr = "lockedFlag !='N'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = LockedUserView.class,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        operationHandler = UserLockOperationHandler.class,
                        title = "锁定用户",
                        show = @ExprBool(
                                value = true,
                                params = "User@LOCK"
                        )
                ),
        }
)
public class LockedUser extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号", search = @Search(vague = true), notNull = true, readonly = @Readonly(add = false))
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true), notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

    /**
     * 该状态用于业务是否能选择该用户，该字段禁用不影响登录
     */
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", search = @Search(value = false),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;

    /**
     * 该字段用于登录，锁定不可登录。该字段不影响业务是否可选择用户
     */
    @FabosJsonField(
            views = @View(title = "锁定标识"),
            edit = @Edit(title = "锁定标识", search = @Search(vague = true),
                    defaultVal = "N",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}))
    )
    private String lockedFlag;

    @Comment("锁定日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "锁定日期"),
            edit = @Edit(title = "锁定日期", show = false, search = @Search)
    )
    private LocalDateTime lockDate; // 锁定日期字段

    @FabosJsonField(
            views = @View(title = "锁定原因"),
            edit = @Edit(title = "锁定原因", search = @Search(vague = true))
    )
    private String lockReason;


    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "电话号码", show = false),
            edit = @Edit(title = "电话号码", search = @Search(vague = true), readonly = @Readonly(add = false), notNull = true, inputType = @InputType(regex = RegexConst.PHONE_REGEX)
            ))
    private String phoneNumber;

}
