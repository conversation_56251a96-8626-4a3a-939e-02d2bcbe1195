package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.bc.urm.domain.menu.event.MenuDataProxy;
import cec.jiutian.core.frame.constant.*;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.handler.SqlChoiceFetchHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@FabosJson(name = "跳转菜单参数配置",
        orderBy = "JumpMenuArgAddView.sequenceNumber asc, JumpMenuArgAddView.createTime asc",
        filter = @Filter(value = "JumpMenuArgAddView.jumpType = 'model' and JumpMenuArgAddView.buttonType = 'jump'"),
        power = @Power(add = false,delete = false,export = false, importable = false, print = false)
)
@Table(name = "fd_menu", uniqueConstraints = @UniqueConstraint(columnNames = {"moduleValue"}))
@Entity
@Getter //@Data重写hashcode 方法导致了栈溢出，所以使用setter，getter代替
@Setter
public class JumpMenuArgAddView extends MetaModel {

    public static final String VALUE = "moduleValue";

    @ManyToOne(cascade = CascadeType.MERGE)
    @FabosJsonField(
            edit = @Edit(
                    title = "上级树节点",
                    type = EditType.REFERENCE_TREE,
                    readonly = @Readonly,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
            )
    )
    private Menu parent;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            edit = @Edit(title = "编码", readonly = @Readonly)
    )
    private String moduleCode;
    @FabosJsonField(
            edit = @Edit(
                    title = "菜单类型",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuTypeEnum.ChoiceFetch.class}
                    )
            )
    )
    private String moduleTypeCode;

    @FabosJsonField(
            edit = @Edit(
                    title = "菜单子类型",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    readonly = @Readonly,
                    defaultVal = "usual",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuSubTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode == 'model'")
            )
    )
    private String subType;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "图标"),
            edit = @Edit(title = "图标",
                    readonly = @Readonly,
                    type = EditType.select_icon))
    private String moduleIconText;

    @FabosJsonField(
            views = @View(title = "排序"),
            edit = @Edit(title = "排序",
                    notNull = true,
                    readonly = @Readonly,
                    numberType = @NumberType(min = 0, max = 9999))
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            edit = @Edit(title = "路由名称",
                    readonly = @Readonly)
    )
    private String moduleValue;

    @FabosJsonField(
            edit = @Edit(
                    title = "状态",
                    type = EditType.CHOICE,
                    notNull = true,
                    defaultVal = "1",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuStatus.ChoiceFetch.class}
                    ),
                    //按钮时隐藏
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode!='button'")
            )
    )
    private Integer status;

    @FabosJsonField(
            edit = @Edit(
                    title = "是否隐藏",
                    notNull = true,
                    defaultVal = "false",
                    readonly = @Readonly,
                    //按钮时隐藏
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode!='button'")
            )
    )
    private Boolean hideFlag;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "跳转菜单", column = "name"),
            edit = @Edit(title = "跳转菜单",
                    readonly = @Readonly,
                    type = EditType.REFERENCE_TREE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "jumpType=='model'"))
    )
    private JumpMenuView routerModule;

    @FabosJsonField(
            views = @View(title = "页面"),
            edit = @Edit(
                    title = "页面",
                    readonly = @Readonly,
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            //查询已发布的页面供选择
                            fetchHandlerParams = {"select id, page_code from page_design where is_publish = 'Y'", "5000"}
                    )
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='lowCode'")
            )
    )
    private String pageId;

    @FabosJsonField(
            views = @View(title = "按钮类型"),
            edit = @Edit(title = "按钮类型",
                    type = EditType.CHOICE,
                    defaultVal = "default",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuButtonTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button'")
            )
    )
    private String buttonType;

    @FabosJsonField(
            views = @View(title = "页面地址"),
            edit = @Edit(title = "页面地址",
                    readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='router'|| buttonType =='jump' || jumpType == 'link'"))
    )
    private String href;

    //    跳转方式
    @FabosJsonField(
            views = @View(title = "跳转方式"),
            edit = @Edit(title = "跳转方式",
                    type = EditType.CHOICE,
                    defaultVal = "link",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuRouterTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "!showType &&(moduleTypeCode=='router' || buttonType =='jump')"))
    )
    private String routerType;

    //    展示方式
    @FabosJsonField(
            views = @View(title = "展示方式"),
            edit = @Edit(title = "展示方式",
                    type = EditType.CHOICE,
                    defaultVal = "popup",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuShowTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "!routerType && buttonType =='default'"))
    )
    private String showType;

    //    跳转类型
    @FabosJsonField(
            views = @View(title = "跳转类型"),
            edit = @Edit(title = "跳转类型",
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    readonly = @Readonly,
                    choiceType = @ChoiceType(
                            fetchHandler = {ButtonJumpTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button'"))
    )
    private String jumpType;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    readonly = @Readonly,
                    type = EditType.TEXTAREA)
    )
    private String moduleDescription;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "menu_id")
    @FabosJsonField(
            views = @View(title = "字段列表",
                    column = "currentName"
            ),
            edit = @Edit(
                    title = "字段列表",
                    type = EditType.TAB_TABLE_ADD,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<ModelJumpArg> jumpArgs;
}
