package cec.jiutian.bc.urm.domain.menu.event;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.meta.model.PageDesignAction;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Service
public class MenuDataProxy implements DataProxy<Menu> {

    private final FabosJsonDao fabosJsonDao;

    private final MenuService menuService;

    private final UrmCommandService userCommandService;

    private final FabosJsonSessionService sessionService;
    private final JpaCrud jpaCrud;

    @Value("${spring.application.name}")
    public String currentService = "";

    public MenuDataProxy(MenuService menuService, FabosJsonSessionService sessionService, FabosJsonDao fabosJsonDao, UrmCommandService userCommandService, JpaCrud jpaCrud) {
        this.menuService = menuService;
        this.sessionService = sessionService;
        this.fabosJsonDao = fabosJsonDao;
        this.userCommandService = userCommandService;
        this.jpaCrud = jpaCrud;
    }


    @Override
    public void afterAdd(Menu menu) {
        menuService.addPowerButton(menu);
        if(StringUtils.isNotBlank(menu.getPageId())){
            //保存页面设计的按钮  也就是action
            List<PageDesignAction> actionList = fabosJsonDao.queryEntityList(PageDesignAction.class,"belongPage='"+menu.getPageId()+"'");
            if(!CollectionUtils.isEmpty(actionList)){
                for (int i = 0; i < actionList.size(); i++) {
                    PageDesignAction d = actionList.get(i);
                    Menu buttonMenu = new Menu();
                    String code = menu.generateCode(8);
                    buttonMenu.create(menu,d.getActionDisplayName(),code,code, MenuTypeEnum.BUTTON.getCode(),i+1, MenuStatus.OPEN.getValue(),menu.getModuleIconText(), currentService);
//                    buttonMenu.setOwnComponent(menu.getOwnComponent());
//                    buttonMenu.setOid(menu.getOid());
                    jpaCrud.insert(buttonMenu);
                }
            }
        }
        //更新缓存
//        menuService.getAllMenuList();
        userCommandService.flushCache();
    }

    @Override
    public void beforeAdd(Menu menu) {
        menuService.checkByParentIdAndMenuName(menu);
        menu.setModuleCode(menu.generateCode(8));
        menu.setCreateTime(LocalDateTime.now());
        menu.setCreateBy(UserContext.getAccount());
        menu.setUpdateTime(LocalDateTime.now());
        menu.setUpdateBy(UserContext.getAccount());
        menu.setApplicationName(currentService);
        //保存父级菜单、组件信息时需要查询父级菜单完整信息，前端只传id
        if (menu.getParent() != null && StringUtils.isNotBlank(menu.getParent().getId())) {
            Menu targetMenu = fabosJsonDao.findById(menu.getParent().getClass(), menu.getParent().getId());
            menu.setParent(targetMenu);
        }
//        if (menu.getOwnComponent() != null && StringUtils.isNotBlank(menu.getOwnComponent().getId())) {
//            MetadataComponent targetComponent = fabosJsonDao.findById(menu.getOwnComponent().getClass(), menu.getOwnComponent().getId());
//            menu.setOwnComponent(targetComponent);
//        }
//        if(StringUtils.isNotBlank(menu.getPageId())){
//            //保存fabosJson
//            PageDesign page = fabosJsonDao.findById(PageDesign.class, menu.getPageId());
//            menu.setFabosJson(page.getPageContent());
//        }
        userCommandService.removeAllTokens();
    }

    @Override
    public void beforeUpdate(Menu menu) {
        //更新父级菜单、组件信息时需要查询父级菜单完整信息，前端只传id
        if (menu.getParent() != null && StringUtils.isNotBlank(menu.getParent().getId())) {
            Menu targetMenu = fabosJsonDao.findById(menu.getParent().getClass(), menu.getParent().getId());
            menu.setParent(targetMenu);
        }
//        if (menu.getOwnComponent() != null && StringUtils.isNotBlank(menu.getOwnComponent().getId())) {
//            MetadataComponent targetComponent = fabosJsonDao.findById(menu.getOwnComponent().getClass(), menu.getOwnComponent().getId());
//            menu.setOwnComponent(targetComponent);
//        }
        menu.setUpdateTime(LocalDateTime.now());
        menu.setUpdateBy(UserContext.getAccount());
        menu.setApplicationName(currentService);
    }
    @Override
    public void afterUpdate(Menu menu) {
        //更新缓存
        userCommandService.removeAllTokens();
//        userCommandService.flushCache();
    }

    @Override
    public void beforeDelete(Menu menu) {
//        menuService.deleteMenu(menu.getId());
        menuService.tryToDeleteChildButtonWithException(menu);
    }
    @Override
    public void afterDelete(Menu menu) {
        userCommandService.removeAllTokens();
        //更新缓存
//        userCommandService.flushCache();
    }
}
