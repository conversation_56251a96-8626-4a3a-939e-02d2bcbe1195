package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.user.entity.LockedUser;
import cec.jiutian.bc.urm.domain.user.entity.LockedUserView;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


@Component
@Transactional
public class UserLockOperationHandler implements OperationHandler<LockedUser, LockedUserView> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<LockedUser> data, LockedUserView modelObject, String[] param) {
        LockedUser lockedUser = fabosJsonDao.getById(LockedUser.class, modelObject.getId());
        lockedUser.setLockedFlag("Y");
        lockedUser.setLockDate(LocalDateTime.now());
        lockedUser.setLockReason(modelObject.getLockReason());
        fabosJsonDao.mergeAndFlush(lockedUser);
        return "msg.success('用户已锁定')";
    }
}
