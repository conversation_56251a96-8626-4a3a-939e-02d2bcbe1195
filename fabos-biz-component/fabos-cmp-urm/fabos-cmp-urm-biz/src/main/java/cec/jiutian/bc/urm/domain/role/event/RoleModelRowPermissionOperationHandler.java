package cec.jiutian.bc.urm.domain.role.event;

import cec.jiutian.bc.urm.ModelRowPermission.entity.ModelRowPermission;
import cec.jiutian.bc.urm.domain.role.entity.RoleAuthDistribute;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class RoleModelRowPermissionOperationHandler implements OperationHandler<RoleAuthDistribute, ModelRowPermission> {
    @Resource
    private UserService userService;
    @Resource
    private EntityManagerFactory entityManagerFactory;
    @Resource
    private PermissionLogManager permissionLogManager;
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<RoleAuthDistribute> data, ModelRowPermission modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }

        String rId = data.get(0).getId();
        String sql1 = "select permission_id from fd_role_row_permission  where role_id = '" + rId +"'";
        List<String> idsDb = fabosJsonDao.getJdbcTemplate().query(sql1, (rs, rowNum) -> rs.getString("permission_id"));

        EntityManager entityManager = entityManagerFactory.createEntityManager();
        try {
            entityManager.getTransaction().begin();
            if (param != null) {
                // 使用SQL进行删除操作
                String deletedSql = "DELETE FROM fd_role_row_permission WHERE role_id = ?";
                Query dquery = entityManager.createNativeQuery(deletedSql);
                dquery.setParameter(1, rId); // 设置要删除记录的ID值
                dquery.executeUpdate(); // 执行删除操作
                for (String permissionId : param) {
                    String sql = "INSERT INTO fd_role_row_permission (permission_id, role_id) VALUES( ?1, ?2)";
                    Query query = entityManager.createNativeQuery(sql);
                    query.setParameter(1, permissionId);
                    query.setParameter(2, rId);
                    query.executeUpdate();
                }
            }
            entityManager.flush();
            entityManager.getTransaction().commit();
        } catch (Exception e) {
            entityManager.getTransaction().rollback();
        } finally {
            entityManager.close();
        }
        List<String> ids = Arrays.stream(param).toList();
        try {
            //行列权限暂时没有使用这个自定义按钮handler
            //permissionLogManager.updatePermission(getPermissionDTO(rId,data.get(0).getName(),idsDb,ids), PermissionTypeEnum.COLUMN);
        }catch (Exception e){
            e.printStackTrace();
            log.error("角色添加列权限配置添加三员日志异常:",e);
        }
        return "msg.success('成功')";
    }

    private PermissionDTO getPermissionDTO(String roleId, String roleName, List<String> idsDb, List<String> ids){
        // 差集 (ids - idsDb)
        List<String> addIds = ids.stream().filter(item -> !idsDb.contains(item)).toList();
        List<String> deleteIds = idsDb.stream().filter(item -> !ids.contains(item)).toList();

        List<String> addNames = getNameByIds(addIds);
        List<String> deleteNames = getNameByIds(deleteIds);
        return new PermissionDTO(roleId,roleName,addIds,addNames,deleteIds,deleteNames);
    }

    private List<String> getNameByIds(List<String> ids){
        List<String> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ids)){
            EntityManager em = fabosJsonDao.getEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<ModelRowPermission> cq = cb.createQuery(ModelRowPermission.class);
            Root<ModelRowPermission> root = cq.from(ModelRowPermission.class);
            cq.select(root).where(cb.in(root.get("id")).value(ids));
            TypedQuery<ModelRowPermission> q = em.createQuery(cq);
            List<ModelRowPermission> resultList = q.getResultList();
            if(CollectionUtils.isNotEmpty(resultList)){
                resultList.forEach(d->{
                    result.add(d.getModelDisplayName()+"("+ d.getFilter()+")");
                });
            }
        }
        return result;
    }
}
