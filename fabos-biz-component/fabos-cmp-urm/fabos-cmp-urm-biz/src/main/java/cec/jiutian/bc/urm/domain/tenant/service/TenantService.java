package cec.jiutian.bc.urm.domain.tenant.service;

import cec.jiutian.bc.urm.domain.tenant.entity.Tenant;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.bc.urm.message.command.TenantData;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Query;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024-05-29 16:20
 */

@Service
@AllArgsConstructor
public class TenantService {

    private final JpaCrud jpaCrud;

    @Resource
    private EntityManagerFactory entityManagerFactory;

    public void handCreateTenant(TenantData tenantData){
        Tenant tenant=jpaCrud.getById(Tenant.class,tenantData.getId());
        if(tenant==null){
            EntityManager entityManager = entityManagerFactory.createEntityManager();
            try {
                entityManager.getTransaction().begin();
                Date nowDate=new Date();
                String sql="INSERT INTO fd_tenant (id, create_by, create_time, update_by, update_time, effect_date, enterprise, expire_date, name, state) VALUES( ?1, ?2, ?3, ?4, ?5, ?6,?7, ?8, ?9, ?10)";
                Query query=entityManager.createNativeQuery(sql);
                query.setParameter(1,tenantData.getId());
                query.setParameter(2,"");
                query.setParameter(3,nowDate);
                query.setParameter(4,"");
                query.setParameter(5,nowDate);
                query.setParameter(6,tenantData.getEffectDate());
                query.setParameter(7,tenantData.getEnterprise());
                query.setParameter(8,tenantData.getExpireDate());
                query.setParameter(9,tenantData.getName());
                query.setParameter(10,tenantData.getState());
                query.executeUpdate();
                entityManager.flush();
                entityManager.getTransaction().commit();
            }catch (Exception e){
                entityManager.getTransaction().rollback();
            }finally {
                entityManager.close();
            }
            addComponent(tenantData);
        }
    }

    private void addComponent(TenantData tenantData){
        Tenant tenant=jpaCrud.getById(Tenant.class,tenantData.getId());
        List<FabModule> fabModuleList = tenantData.getComponentList().stream().map(it->getFabModule(it)).collect(Collectors.toList());
        tenant.setAuthorizedModule(fabModuleList);
        jpaCrud.update(tenant);
    }

    public FabModule getFabModule(String moduleName){
        if(StringUtils.isEmpty(moduleName)){
            return null;
        }
        FabModule fabModule=new FabModule();
        fabModule.setName(moduleName.trim());
        List<FabModule> list = jpaCrud.select(fabModule);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }


    public Tenant getTenant(String oid){
        return jpaCrud.getById(Tenant.class,oid);
    }

    @Transactional
    public List<String> findModuleNameListById(String oid) {
        Tenant tenant=getTenant(oid);
        if( tenant==null|| CollectionUtils.isEmpty(tenant.getAuthorizedModule())){
            return new ArrayList<>();
        }
        List<FabModule> list= tenant.getAuthorizedModule();
        return list.stream().map(it->it.getName()).collect(Collectors.toList());
    }
}
