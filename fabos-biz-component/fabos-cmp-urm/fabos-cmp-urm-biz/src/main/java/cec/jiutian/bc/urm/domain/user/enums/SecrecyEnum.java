package cec.jiutian.bc.urm.domain.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description:
 */
@AllArgsConstructor
@Getter
public enum SecrecyEnum {
    NON_SECRECY("non-secret", -1,5,24,"非密"),
    SECRECY("secret", 30,5,24,"秘密"),
    CONFIDENTIAL("confidential", 10,5,24,"机密"),
    ;
    private final String value;
    //连接时间  单位分钟
    private final Integer connectTime;
    //禁止登录前允许失败的次数
    private final Integer allowLoginFailCount;
    //
    private final Integer disableLoginHour;
    private final String msg;

    public static SecrecyEnum getByValue(String value) {
        for (SecrecyEnum type : SecrecyEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
