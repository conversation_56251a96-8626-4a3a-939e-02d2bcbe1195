package cec.jiutian.bc.urm.domain.role.service;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.user.entity.UserByRoleView;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@Transactional
public class RoleService {

    private final FabosJsonDao fabosJsonDao;
    private final JpaCrud jpaCrud;

    public RoleService(FabosJsonDao fabosJsonDao, JpaCrud jpaCrud) {
        this.fabosJsonDao = fabosJsonDao;
        this.jpaCrud = jpaCrud;
    }

    public List<Role> createRoles4Init(List<Role> roles) {
        Optional.ofNullable(roles).ifPresent(roles1 -> {
                    for (Role role : roles) {
                        String[] vals = new String[]{role.getCode(),role.getOid()};
                        fabosJsonDao.persistIfNotExistWithTenantId(Role.class, role, Role.CODE, vals);
                    }
                }
        );
        return roles;
    }

    private PermissionDTO getPermissionDTO(String roleId, String roleName, List<String> idsDb, List<String> ids){
        // 差集 (ids - idsDb)
        List<String> addIds = ids.stream().filter(item -> !idsDb.contains(item)).toList();
        List<String> deleteIds = idsDb.stream().filter(item -> !ids.contains(item)).toList();

        List<String> addNames = getNameByIds(addIds);
        List<String> deleteNames = getNameByIds(deleteIds);
        return new PermissionDTO(roleId,roleName,addIds,addNames,deleteIds,deleteNames);
    }

    private List<String> getNameByIds(List<String> menuIds){
        List<String> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(menuIds)){
            EntityManager em = fabosJsonDao.getEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Menu> cq = cb.createQuery(Menu.class);
            Root<Menu> root = cq.from(Menu.class);
            cq.select(root).where(cb.in(root.get("id")).value(menuIds));
            TypedQuery<Menu> q = em.createQuery(cq);
            List<Menu> resultList = q.getResultList();
            if(CollectionUtils.isNotEmpty(resultList)){
                resultList.forEach(d->{
                    if(MenuTypeEnum.BUTTON.getCode().equals(d.getModuleTypeCode())){
                        String parentName = d.getParent().getName();
                        result.add(d.getName()+"("+parentName+")");
                    }else {
                        result.add(d.getName());
                    }
                });
            }
        }
        return result;
    }

    public Set<String> getUserPhonesByRoles(List<String> roleIds) {
        EntityManager em = fabosJsonDao.getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<Role> root = cq.from(Role.class);

        // 1. 关联UserByRoleView表
        Join<Role, UserByRoleView> userJoin = root.join("users", JoinType.INNER);

        // 2. 选择User的phone字段
        cq.select(userJoin.get("phoneNumber"))
                .where(root.get("id").in(roleIds));

        return new HashSet<>(em.createQuery(cq).getResultList());
    }

}
