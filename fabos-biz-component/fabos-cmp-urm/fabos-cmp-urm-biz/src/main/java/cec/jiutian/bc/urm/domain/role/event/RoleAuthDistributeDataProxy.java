package cec.jiutian.bc.urm.domain.role.event;

import cec.jiutian.bc.urm.ModelRowPermission.entity.ModelRowPermission;
import cec.jiutian.bc.urm.domain.ModelColumnPermission.entity.ModelColumnPermission;
import cec.jiutian.bc.urm.domain.role.entity.RoleAuthDistribute;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.PermissionTypeEnum;
import cec.jiutian.meta.PermissionLevelEnum;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.QueryException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RoleAuthDistributeDataProxy implements DataProxy<RoleAuthDistribute> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private PermissionLogManager permissionLogManager;

    @Override
    public void beforeAdd(RoleAuthDistribute roleAuthDistribute) {
        throw new RuntimeException("权限管理不允许新增角色！");
    }

    @Override
    public void beforeUpdate(RoleAuthDistribute roleAuthDistribute) {
        beforeOperate(roleAuthDistribute);
    }

    @Override
    public void afterUpdate(RoleAuthDistribute roleAuthDistribute) {
        //编辑之前添加日志
        try {
            addPermissionLog(roleAuthDistribute);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理行列权限三员日志异常：", e);
        }
    }

    private void beforeOperate(RoleAuthDistribute roleAuthDistribute) {
        List<ModelColumnPermission> columnPermissions = roleAuthDistribute.getColumnPermissions();
        if (CollectionUtils.isNotEmpty(columnPermissions)) {
            HashSet<String> filelds = new HashSet<>();
            for (ModelColumnPermission columnPermission : columnPermissions) {
                if (filelds.contains(columnPermission.getFieldId())) {
                    throw new FabosJsonApiErrorTip(columnPermission.getFiledDisplayName() + "：字段重复，请删除相关选项。");
                }
                filelds.add(columnPermission.getFieldId());
                columnPermission.setRoleId(roleAuthDistribute.getId());
                fabosJsonDao.saveOrUpdate(columnPermission);
            }
        }
        beforeOpRow(roleAuthDistribute);
    }

    private void beforeOpRow(RoleAuthDistribute roleAuthDistribute) {
        List<ModelRowPermission> rowPermissions = roleAuthDistribute.getRowPermissions();
        if (CollectionUtils.isNotEmpty(rowPermissions)) {
            HashSet<String> models = new HashSet<>();
            for (ModelRowPermission rowPermission : rowPermissions) {
                if (models.contains(rowPermission.getModelName())) {
                    throw new FabosJsonApiErrorTip(rowPermission.getModelDisplayName() + "：过滤条件重复，请删除相关选项。");
                }
                rowPermission.setRoleId(roleAuthDistribute.getId());
                models.add(rowPermission.getModelName());
                validateHql(rowPermission);
                fabosJsonDao.saveOrUpdate(rowPermission);
            }
        }
    }

    private void addPermissionLog(RoleAuthDistribute roleAuthDistribute) {
        //行列权限只存在编辑
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        RoleAuthDistribute roleAuthDistributeDb = entityManager.find(RoleAuthDistribute.class, roleAuthDistribute.getId());
        addColumnPermissionLog(roleAuthDistribute, roleAuthDistributeDb);
        addRowPermissionLog(roleAuthDistribute, roleAuthDistributeDb);
    }

    private void addColumnPermissionLog(RoleAuthDistribute roleAuthDistribute, RoleAuthDistribute roleAuthDistributeDb) {
        List<ModelColumnPermission> columnPermissions = new ArrayList<>();
        List<ModelColumnPermission> columnPermissionsDb = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleAuthDistribute.getColumnPermissions())) {
            columnPermissions = roleAuthDistribute.getColumnPermissions();
        }
        if (CollectionUtils.isNotEmpty(roleAuthDistributeDb.getColumnPermissions())) {
            columnPermissionsDb = roleAuthDistributeDb.getColumnPermissions();
        }
        permissionLogManager.updatePermission(getColumnPermissionDTO(roleAuthDistribute.getId(), roleAuthDistribute.getName(), columnPermissions, columnPermissionsDb), PermissionTypeEnum.COLUMN);
        log.info("添加列权限三员日志");
    }

    private PermissionDTO getColumnPermissionDTO(String roleId, String roleName, List<ModelColumnPermission> columnPermissions, List<ModelColumnPermission> columnPermissionsDb) {
        //差集columnPermissions-columnPermissionsDb
        List<ModelColumnPermission> addList = columnPermissions.stream().filter(d -> columnPermissionsDb.stream().noneMatch(data -> data.getId().equals(d.getId()))).toList();
        List<ModelColumnPermission> deleteList = columnPermissionsDb.stream().filter(d -> columnPermissions.stream().noneMatch(data -> data.getId().equals(d.getId()))).toList();
        List<String> addIds = new ArrayList<>();
        List<String> deleteIds = new ArrayList<>();
        List<String> addNames = new ArrayList<>();
        List<String> deleteNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            addIds = addList.stream().map(ModelColumnPermission::getId).collect(Collectors.toList());
            addNames = addList.stream().map(d -> d.getFiledDisplayName() + "(" + PermissionLevelEnum.getDesc(d.getPermissionLevel()) + ")").collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            deleteIds = deleteList.stream().map(ModelColumnPermission::getId).collect(Collectors.toList());
            deleteNames = deleteList.stream().map(d -> d.getFiledDisplayName() + "(" + PermissionLevelEnum.getDesc(d.getPermissionLevel()) + ")").collect(Collectors.toList());
        }
        return new PermissionDTO(roleId, roleName, addIds, addNames, deleteIds, deleteNames);
    }

    private void addRowPermissionLog(RoleAuthDistribute roleAuthDistribute, RoleAuthDistribute roleAuthDistributeDb) {
        List<ModelRowPermission> rowPermissions = new ArrayList<>();
        List<ModelRowPermission> rowPermissionsDb = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleAuthDistribute.getRowPermissions())) {
            rowPermissions = roleAuthDistribute.getRowPermissions();
        }
        if (CollectionUtils.isNotEmpty(roleAuthDistributeDb.getRowPermissions())) {
            rowPermissionsDb = roleAuthDistributeDb.getRowPermissions();
        }
        permissionLogManager.updatePermission(getRowPermissionDTO(roleAuthDistribute.getId(), roleAuthDistribute.getName(), rowPermissions, rowPermissionsDb), PermissionTypeEnum.ROW);
        log.info("添加行权限三员日志");
    }

    private PermissionDTO getRowPermissionDTO(String roleId, String roleName, List<ModelRowPermission> rowPermissions, List<ModelRowPermission> rowPermissionsDb) {
        //rowPermissions-rowPermissionsDb
        List<ModelRowPermission> addList = rowPermissions.stream().filter(d -> rowPermissionsDb.stream().noneMatch(data -> data.getId().equals(d.getId()))).toList();
        List<ModelRowPermission> deleteList = rowPermissionsDb.stream().filter(d -> rowPermissions.stream().noneMatch(data -> data.getId().equals(d.getId()))).toList();
        List<String> addIds = new ArrayList<>();
        List<String> deleteIds = new ArrayList<>();
        List<String> addNames = new ArrayList<>();
        List<String> deleteNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addList)) {
            addIds = addList.stream().map(ModelRowPermission::getId).collect(Collectors.toList());
            addNames = addList.stream().map(d -> d.getModelDisplayName() + "(" + d.getFilter() + ")").collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            deleteIds = deleteList.stream().map(ModelRowPermission::getId).collect(Collectors.toList());
            deleteNames = deleteList.stream().map(d -> d.getModelDisplayName() + "(" + d.getFilter() + ")").collect(Collectors.toList());
        }
        return new PermissionDTO(roleId, roleName, addIds, addNames, deleteIds, deleteNames);
    }

    private void validateHql(ModelRowPermission rowPermission) {
        String hql = generateHql(rowPermission);
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        try {
            TypedQuery<?> query = entityManager.createQuery(hql, Object.class);
            log.info("HQL语法正确:{}", hql);
        } catch (IllegalArgumentException e) {
            log.error("HQL语法错误: " + e.getMessage());
            throw new FabosJsonApiErrorTip("请检查过滤条件在模型中是否存在相关字段：" + e.getMessage());
        } catch (QueryException e) {
            log.error("HQL语法错误: " + e.getMessage());
            throw new FabosJsonApiErrorTip("HQL语法错误:" + e.getMessage());
        }
    }

    private static final String select = "SELECT COUNT(1) FROM ";
    private static final String where = " WHERE 1 = 1 AND (";
    private static final Pattern replaceRegxPattern = Pattern.compile("##(?=(?:[^\']*'[^']*')*[^\']*$)");
    public static String replaceOutsideQuotes(String input) {
        // 使用正则表达式匹配不在单引号内的##
        Matcher matcher = replaceRegxPattern.matcher(input);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            matcher.appendReplacement(sb, "");
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    private static String generateHql(ModelRowPermission rowPermission){
        if (StringUtils.isBlank(rowPermission.getFilter())) {
            throw new FabosJsonApiErrorTip("过滤条件不能为空");
        }
        if (!validateConditionPlaceholders(rowPermission.getFilter())){
            throw new FabosJsonApiErrorTip("占位符中的内容只能为以下数据：" + ALLOWED_PLACEHOLDERS + "；格式：{{CURRENT_TIME}}");
        }
        String filter = rowPermission.getFilter().replace("{{", ":")
                .replace("}}", "");
        String replace = replaceOutsideQuotes(filter);

        StringBuilder hqlBuilder = new StringBuilder();
        hqlBuilder.append(select)
                .append( rowPermission.getModelName())
                .append(where)
                .append(replace)
                .append(")");
        return hqlBuilder.toString();
    }


    // 定义允许的占位符
    private static final String ALLOWED_PLACEHOLDERS = "CURRENT_TIME|CURRENT_ACCOUNT|CURRENT_DEPARTMENT|CURRENT_SECRET_LEVEL";
    // 定义正则表达式
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{(" + ALLOWED_PLACEHOLDERS + ")\\}\\}");

    public static boolean validateConditionPlaceholders(String filter) {
        // 先检查是否有任何占位符
        if (!filter.contains("{{") || !filter.contains("}}")) {
            return true; // 没有占位符，直接返回 true
        }

        // 使用正则表达式匹配所有占位符
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(filter);

        // 使用临时字符串替换所有允许的占位符
        String temp = matcher.replaceAll("");

        // 检查是否还有其他占位符
        return !temp.contains("{{") || !temp.contains("}}");
    }

    @Override
    public void beforeDelete(RoleAuthDistribute roleAuthDistribute) {
        throw new RuntimeException("权限管理不允许删除角色！");
    }
}
