package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Formula;

@FabosJson(
        name = "模型字段"
)
@Entity
@Table(name = "metadata_model_field")
public class ModelFieldMTO extends BaseModel {

    // 字段编码, 限定名
    @FabosJsonField(
            views = @View(title = "字段编码"),
            edit = @Edit(title = "字段编码")
    )
    private String code;

    // 字段简短名
    @FabosJsonField(
            views = @View(title = "字段简短名"),
            edit = @Edit(title = "字段简短名",search = @Search(vague = true))
    )
    private String name;

    // 依赖模型中对应的关联字段 记录依赖模型字段id
    @ManyToOne
    @JsonIgnoreProperties({"fields"})
    private MetadataModel referenceModel;

    @Formula("(select t2.NAME from metadata_model_field t1, METADATA_MODEL t2 where t1.REFERENCE_MODEL_ID = t2.id and t1.id = id)")
    @FabosJsonField(
            views = @View(title = "模型名称"),
            edit = @Edit(title = "模型名称", search = @Search(vague = true))
    )
    @Comment("模型名称")
    private String modelName;

}
