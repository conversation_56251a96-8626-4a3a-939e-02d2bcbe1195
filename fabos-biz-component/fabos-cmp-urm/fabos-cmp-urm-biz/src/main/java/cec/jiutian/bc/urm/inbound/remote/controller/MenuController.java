package cec.jiutian.bc.urm.inbound.remote.controller;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.service.query.MenuQueryDTO;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.bc.urm.inbound.local.service.command.init.ComponentInitService;
import cec.jiutian.bc.urm.inbound.remote.event.ApplicationRoleMenuEvent;
import cec.jiutian.bc.urm.service.RoleAuthDistributeService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import jakarta.transaction.Transactional;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/fabos-cmp-urm"+FabosJsonRestPath.FABOS_API)
public class MenuController {

    private final UrmCommandService menuCommandService;

    private final ComponentInitService componentInitService;

    private final RoleAuthDistributeService roleAuthDistributeService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public MenuController(UrmCommandService menuCommandService, ComponentInitService componentInitService, RoleAuthDistributeService roleAuthDistributeService) {
        this.menuCommandService = menuCommandService;
        this.componentInitService = componentInitService;
        this.roleAuthDistributeService = roleAuthDistributeService;
    }

    @PostMapping("/getModuleUrlById")
    public FabosJsonApiModel getModuleUrlById(@RequestBody MenuQueryDTO queryDTO){
        Object object =  menuCommandService.getModuleUrlById(queryDTO);
        return FabosJsonApiModel.successApi(object);
    }

    @PostMapping("/getModuleByModuleValue")
    public FabosJsonApiModel getByModuleValue(@RequestBody MenuQueryDTO queryDTO) {
        Menu object = menuCommandService.getByModuleValue(queryDTO);
        return FabosJsonApiModel.successApi(object);
    }

    @PostMapping("/getAllMenuList")
    public FabosJsonApiModel getAllMenuList(){
        return menuCommandService.getAllMenuList();
    }

    @PostMapping("/getModuleListByParentId")
    public FabosJsonApiModel getModuleListByParentId(@RequestBody MenuQueryDTO queryDTO){
        Object object =  menuCommandService.getModuleListByParentId(queryDTO);
        return FabosJsonApiModel.successApi(object);
    }

    @PostMapping("/getMenuListForPage")
    public FabosJsonApiModel getMenuList(){
        return FabosJsonApiModel.successApi(menuCommandService.getRemoveButtonMenuList());
    }

    @PostMapping("/initMenu")
    public FabosJsonApiModel initMenu(@RequestBody List<String> componentNames){
        if(CollectionUtils.isNotEmpty(componentNames)){
            componentNames.forEach(componentInitService::initMenu);
        }
        return FabosJsonApiModel.successApi();
    }

    @PostMapping("/getExternlMenusByRoleId")
    public FabosJsonApiModel getRoleMenus(@RequestBody Map<String, Object> params) {
        Object object = roleAuthDistributeService.getRoleExternalMenus(params.get("roleId").toString());
        return FabosJsonApiModel.successApi(object);
    }

    // 在UBP上更新角色的应用系统菜单权限
    // params参数格式为：{"roleId": "1", "roleExternalMenus": {"systemCode1": ["menuId1", "menuId2"], "systemCode2": ["menuId3"]}}
    @PostMapping("/updateRoleExternalMenus")
    @Transactional()
    public FabosJsonApiModel updateRoleExternalMenus(@RequestBody Map<String, Object> params) {
        String roleId = params.get("roleId").toString();
        Map<String, List<String>> roleExternalMenus = (Map<String, List<String>>) params.get("roleExternalMenus");
        roleAuthDistributeService.updateRoleExternalMenus(roleId, roleExternalMenus);
        // 发布应用系统菜单权限更新事件，由基础平台进行后续处理（移除掉系统编码）
        roleExternalMenus.replaceAll((systemCode, menuIds) ->
                menuIds.stream()
                        .map(id -> id.replaceAll("_.*$", ""))
                        .collect(Collectors.toList())
        );
        eventPublisher.publishEvent(new ApplicationRoleMenuEvent(this, params));
        return FabosJsonApiModel.successApi();
    }


}
