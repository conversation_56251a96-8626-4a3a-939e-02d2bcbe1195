package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;


@FabosJson(name = "菜单管理",
        orderBy = "JumpMenuView.sequenceNumber asc, JumpMenuView.createTime asc",
        tree = @Tree(pid = "parent.id", expandLevel = 1)
)
@Table(name = "fd_menu")
@Entity
@Getter
@Setter
public class JumpMenuView extends MetaModel {

    @ManyToOne(cascade = CascadeType.MERGE)
    @FabosJsonField(
            edit = @Edit(
                    title = "上级树节点",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
            )
    )
    private JumpMenuView parent;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            edit = @Edit(title = "编码", readonly = @Readonly)
    )
    private String moduleCode;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            //模型编码/路由编码/按钮编码
            edit = @Edit(title = "路由名称"
            )
    )
    private String moduleValue;

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            edit = @Edit(
                    title = "菜单类型",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuTypeEnum.ChoiceFetch.class}
                    )
            )
    )
    private String moduleTypeCode;

    @FabosJsonField(
            views = @View(title = "排序"),
            edit = @Edit(title = "排序",
                    notNull = true,
                    numberType = @NumberType(min = 0, max = 9999))
    )
    private Integer sequenceNumber;
}
