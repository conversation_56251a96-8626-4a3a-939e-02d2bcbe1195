package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "外部菜单",
        orderBy = "Menu.sequenceNumber asc",
        tree = @Tree(pid = "parent.id", expandLevel = 0))
@Table(name = "fd_menu_external_view")
@Entity
@Getter
@Setter
@TemplateType(type = "treeForm")
public class ExternalMenu {

    @Id
    @Column(name = "id")
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            sort = 0,
            views = @View(title = "应用系统"),
            edit = @Edit(title = "应用系统", desc = "应用系统编码，与应用管理中编码一致",
                    notNull = true,
                    search = @Search(vague = true))
    )
    @SubTableField
    private String systemCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @FabosJsonField(
            sort = 1,
            edit = @Edit(
                    title = "上级树节点",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ExternalMenu parent;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            edit = @Edit(title = "编码", readonly = @Readonly)
    )
    private String moduleCode;

    @FabosJsonField(
            sort = 4,
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            sort = 6,
            views = @View(title = "排序"),
            edit = @Edit(title = "排序",
                    notNull = true,
                    numberType = @NumberType(min = 0, max = 9999))
    )
    private Integer sequenceNumber;


    private String moduleTypeCode;

}
