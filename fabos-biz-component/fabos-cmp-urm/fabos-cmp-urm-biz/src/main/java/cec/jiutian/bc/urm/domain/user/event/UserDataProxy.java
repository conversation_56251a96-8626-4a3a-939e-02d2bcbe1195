package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.user.entity.FabosJsonUserConst;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.i18n.FabosI18nTranslate;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.jpa.dao.FabosJsonJpaDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.data.redis.utils.BaseRedisCacheUtil;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.OperationResultEnum;
import cec.jiutian.fc.log.enums.OperationTargetEnum;
import cec.jiutian.fc.log.enums.OperationTypeEnum;
import cec.jiutian.meta.core.util.MD5Util;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
public class UserDataProxy implements DataProxy<User> {

    @Resource
    private UserService userService;
    @Resource
    private PermissionLogManager permissionLogManager;
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(User user) {
        this.checkDataLegal(user);
        user.setPassword(FabosJsonUserConst.DEFAULT_USER_PASSWORD);
        if (user.getIsMd5()) {
            user.setPassword(MD5Util.digest(user.getPassword()));
        } else {
            user.setPassword(user.getPassword());
        }
        User user1=userService.getUserByAccount(user.getAccount());
        if (user1!=null){
            throw new FabosJsonApiErrorTip(FabosJsonApiModel.Status.ERROR,"重复的帐号！" , FabosJsonApiModel.PromptWay.MESSAGE);
        }
        User user2 = userService.getUserByPhoneNumber(user.getPhoneNumber());
        if (user2 != null) {
            throw new FabosJsonApiErrorTip(user.getPhoneNumber() + ": " + FabosI18nTranslate.$translate("fabosUser.phoneNumber_duplicate"));
        }
        user.setCreateTime(LocalDateTime.now());
        user.setCreateBy(UserContext.getAccount());
        user.setUpdateTime(user.getCreateTime());
        user.setUpdateBy(UserContext.getAccount());
    }

    private void checkDataLegal(User user) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            throw new FabosJsonApiErrorTip("current user is not login!");
        }
//        if(!CollectionUtils.isEmpty(curr.getRoles())){
//            if (!curr.getRoles().contains(UserManagerTypeEnum.systemManager.getCode()) && !curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode())) {
//                // 如果不是系统管理员，不可添加、修改管理员用户
//                throw new FabosJsonApiErrorTip(FabosI18nTranslate.$translate("fabosUser.not_admin_unable_add"));
//            }
//        }
    }
    @Override
    public void afterAdd(User user) {
        permissionLogManager.addOrDelete(user, OperationTargetEnum.Enum.USER, OperationTypeEnum.Enum.ADD);
    }

    @Override
    public void beforeUpdate(User user) {
        checkDataLegal(user);
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        User userDb = entityManager.find(User.class, user.getId());
        permissionLogManager.update(userDb,user, OperationTargetEnum.Enum.USER, OperationTypeEnum.Enum.UPDATE);

    }

    @Override
    public void afterDelete(User user) {
        permissionLogManager.addOrDelete(user, OperationTargetEnum.Enum.USER, OperationTypeEnum.Enum.DELETE);
    }

}
