package cec.jiutian.bc.urm.domain.test;

/**
 * <AUTHOR>
 * @description:
 */

import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonJpaUtils;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.Parameters;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.data.jpa.base.BaseModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.config.QueryExpression;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Id;
import lombok.Data;


@FabosJson(name = "角色管理",rowOperation = {@RowOperation(
        operationHandler = UserRoleDTOOperationHandler.class,
        code = "UserRoleDTO@TEST",
        mode = RowOperation.Mode.SINGLE,
        title = "自定义按钮测试",
        type = RowOperation.Type.FABOSJSON

)}
)
@FabosJsonI18n
@Data
@QueryModel(hql = "select new map (r.id as id,r.name as roleName,r.code as roleCode,r.description as description) from Role r  ${customWhere} and code in (:ABC) group by r.id,r.name ,r.code ,r.description "
        ,parameters = @Parameters(parameter = "ABC",isCollection = true))
public class UserRoleDTO{
    @FabosJsonField(
            views = @View(title = "id"),
            edit = @Edit(title = "id"),
            customHqlField = "r.id"
    )
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "角色编码"),
            edit = @Edit(title = "角色编码", search = @Search())
    )
    private String roleCode;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.name"
    )
    private String roleName;


    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.description"
    )
    private String description;


}
