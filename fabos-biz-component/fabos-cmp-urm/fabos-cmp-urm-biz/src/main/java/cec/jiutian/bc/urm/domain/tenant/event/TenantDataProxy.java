package cec.jiutian.bc.urm.domain.tenant.event;

import cec.jiutian.bc.urm.domain.tenant.entity.Tenant;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.inbound.local.service.command.init.ComponentInitService;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.i18n.FabosI18nTranslate;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.bc.urm.domain.tenant.constant.RightManagementPolicyEnum;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.inbound.local.service.command.InitDataCommandService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024-05-31 10:15
 */
@Service
public class TenantDataProxy implements DataProxy<Tenant> {

    @Resource
    private InitDataCommandService initDataCommandService;

    @Resource
    private UserService userService;

    @Resource
    private ComponentInitService componentInitService;


    private void checkDataLegal(Tenant tenant) {
        if (RightManagementPolicyEnum.SUPER.getValue().equals(tenant.getRightManagementPolicy())) {
            User superUser = userService.getUserByPhoneNumber(tenant.getSuperPhoneNumber());
            if (superUser != null) {
                throw new FabosJsonApiErrorTip(tenant.getSuperPhoneNumber() + ": " + FabosI18nTranslate.$translate("fabosUser.phoneNumber_duplicate"));
            }
        } else if (RightManagementPolicyEnum.TRIPARTITE.getValue().equals(tenant.getRightManagementPolicy())) {
            User auditUser = userService.getUserByPhoneNumber(tenant.getAuditPhoneNumber());
            if (auditUser != null) {
                throw new FabosJsonApiErrorTip(tenant.getAuditPhoneNumber() + ": " + FabosI18nTranslate.$translate("fabosUser.phoneNumber_duplicate"));
            }

            User systemUser = userService.getUserByPhoneNumber(tenant.getSystemPhoneNumber());
            if (systemUser != null) {
                throw new FabosJsonApiErrorTip(tenant.getSystemPhoneNumber() + ": " + FabosI18nTranslate.$translate("fabosUser.phoneNumber_duplicate"));
            }

            User securityUser = userService.getUserByPhoneNumber(tenant.getSecurityPhoneNumber());
            if (securityUser != null) {
                throw new FabosJsonApiErrorTip(tenant.getSecurityPhoneNumber() + ": " + FabosI18nTranslate.$translate("fabosUser.phoneNumber_duplicate"));
            }
        }
    }

    private void checkComponent(Tenant tenant) {
        if (CollectionUtils.isEmpty(tenant.getAuthorizedModule())) {
            throw new FabosJsonApiErrorTip("请手动添加至少一个组件, 且fabos-cmp-urm必选选中");
        } else {
            if (!tenant.getAuthorizedModule().stream().map(FabModule::getName).collect(Collectors.toList()).contains("fabos-cmp-urm")) {
                throw new FabosJsonApiErrorTip("请手动添加至少一个组件, 且fabos-cmp-urm必选选中");
            }
        }
    }


    @Override
    public void beforeAdd(Tenant tnt) {
        checkComponent(tnt);
        checkDataLegal(tnt);
        tnt.setCreateTime(LocalDateTime.now());
        tnt.setCreateBy(UserContext.getAccount());
        tnt.setUpdateTime(tnt.getCreateTime());
        tnt.setUpdateBy(UserContext.getAccount());
    }

    @Override
    public void beforeUpdate(Tenant tnt) {
        checkComponent(tnt);
        tnt.setUpdateTime(LocalDateTime.now());
        tnt.setUpdateBy(UserContext.getAccount());
    }

    @Override
    public void afterAdd(Tenant tenant) {
        if (!CollectionUtils.isEmpty(tenant.getAuthorizedModule())) {
            for (FabModule e : tenant.getAuthorizedModule()) {
                InitDataDTO dto = new InitDataDTO();
                dto.setOid(tenant.getId());
                dto.setComponentName(e.getName());
                dto.setComponentVersion(e.getVersion());
                if (!"fabos-cmp-urm".equals(e.getName())) {
                   componentInitService.menuInit(dto,e);
                } else {
                    dto.setSystemPhoneNumber(tenant.getSystemPhoneNumber());
                    dto.setSecurityPhoneNumber(tenant.getSecurityPhoneNumber());
                    dto.setSuperPhoneNumber(tenant.getSuperPhoneNumber());
                    dto.setAuditPhoneNumber(tenant.getAuditPhoneNumber());
                    dto.setEmailAddress(tenant.getEmailAddress());
                    dto.setRightManagementPolicy(tenant.getRightManagementPolicy());
                    initDataCommandService.initData(dto);
                }
            }
        }
    }

    public void afterUpdate(Tenant tenant) {
        if (!CollectionUtils.isEmpty(tenant.getAuthorizedModule())) {
            for (FabModule e : tenant.getAuthorizedModule()) {
                InitDataDTO dto = new InitDataDTO();
                dto.setOid(tenant.getId());
                dto.setComponentName(e.getName());
                dto.setComponentVersion(e.getVersion());
                if (!"fabos-cmp-urm".equals(e.getName())) {
                    componentInitService.menuInit(dto,e);
                } else {
                    initDataCommandService.initMenu(dto);
                }
            }
        }
    }
}
