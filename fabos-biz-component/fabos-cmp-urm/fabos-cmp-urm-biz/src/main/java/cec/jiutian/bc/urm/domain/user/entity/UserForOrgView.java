package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Entity
@LinkTable
@Table(name = "fd_meta_user")
@Getter
@Setter
@FabosJsonI18n
@FabosJson(name = "组织关联用户视图",
        orderBy = "UserForOrgView.createTime desc",
        filter = @Filter(value = "UserForOrgView.state = 'Y'" ),
        power = @Power(edit = false,add = false)
)
public class UserForOrgView extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号", search = @Search(vague = true), notNull = true, readonly = @Readonly(add = false))
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true), notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

//    @FabosJsonField(
//            views = @View(title = "岗位"),
//            edit = @Edit(title = "岗位", search = @Search(vague = true))
//    )
//    private String jobPosition;

    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "电话号码", show = false),
            edit = @Edit(title = "电话号码", search = @Search(vague = true), readonly = @Readonly(add = false)
                    , notNull = true, inputType = @InputType(regex = RegexConst.PHONE_REGEX)
            ))
    private String phoneNumber;

    @FabosJsonField(
            views = @View(title = "状态",show = false),
            edit = @Edit(title = "状态", show = false, search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;

}
