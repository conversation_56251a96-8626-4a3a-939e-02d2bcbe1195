package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.bc.urm.domain.position.entity.Position;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.user.event.UserDataProxy;
import cec.jiutian.bc.urm.domain.user.event.UserOperationHandlerImpl;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.OperationFixed;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@LinkTable
@Table(name = "fd_user")
@Getter
@Setter
@FabosJsonI18n
@FabosJson(name = "用户管理",
        orderBy = "User.createTime desc",
        dataProxy = {UserDataProxy.class},
        rowOperation = {@RowOperation(
                code = "User@RESET",
                operationHandler = UserOperationHandlerImpl.class,
                mode = RowOperation.Mode.SINGLE,
                callHint = "确定要重置密码吗？",
                title = "重置密码",
                show = @ExprBool(
                        exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                        params = "User@RESET"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                )
        ),
        }
)
@OperationFixed()
public class User extends MetaUser {

    public static final String PHONE = "phoneNumber";
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "fd_user_role",
            joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    )
    @OrderBy
    @FabosJsonField(
            views = @View(title = "所属角色", export = false),
            edit = @Edit(
                    show = false,
                    title = "所属角色",
                    filter = @Filter(value = "Role.status = true "),
                    type = EditType.CHECKBOX,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<Role> roles;


    @ManyToOne
    @FabosJsonField(
            views = @View(title = "行政部门", column = "name", index = 3),
            edit = @Edit(title = "行政部门", notNull = true, type = EditType.REFERENCE_TABLE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search())
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Org org;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "业务或费用部门", column = "name", index = 3),
            edit = @Edit(title = "业务或费用部门", notNull = false, type = EditType.REFERENCE_TABLE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search())
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Org businessOrg;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属岗位", column = "name"),
            edit = @Edit(title = "所属岗位", notNull = false, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType, search = @Search())
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Position position;

    @FabosJsonField(
            views = @View(title = "身份证件类型"),
            edit = @Edit(title = "身份证件类型", search = @Search,
                    notNull = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "RYMD004")
            )
    )
    private String idType;

    @FabosJsonField(
            views = @View(title = "证件号码", index = 2),
            edit = @Edit(title = "证件号码",
                    notNull = false,
                    inputType = @InputType(regex = RegexConst.ID_CARD),
                    search = @Search(vague = true))
    )
    private String idCard;

    @FabosJsonField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", search = @Search,
                    notNull = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                                              fetchHandlerParams = "RYMD003")
            )
    )
    private String gender;

    @FabosJsonField(
            views = @View(title = "出生日期"),
            edit = @Edit(title = "出生日期", search = @Search,
                    type = EditType.DATE
            )
    )
    private String birthday;

    @FabosJsonField(
            views = @View(title = "政治面貌", show = false),
            edit = @Edit(title = "政治面貌", search = @Search, show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                                              fetchHandlerParams = "RYMD007")
            )
    )
    private String politicalStatus;

//    毕业院校

    @FabosJsonField(
            views = @View(title = "毕业院校", show = false),
            edit = @Edit(title = "毕业院校", show = false,
                    search = @Search,
                    inputType = @InputType(length = 64)
            )
    )
    private String graduateSchool;
    @FabosJsonField(
            views = @View(title = "最高学历", show = false),
            edit = @Edit(title = "最高学历", search = @Search, show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                                              fetchHandlerParams = "RYMD011")
            )
    )
    private String highestEducation;

    // 人员雇佣状态(在制、离职、退休等)
    @FabosJsonField(
            views = @View(title = "雇佣状态"),
            edit = @Edit(title = "雇佣状态", search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                                              fetchHandlerParams = "RYMD014")
            )
    )
    private String personnelStatus;

    @FabosJsonField(
            views = @View(title = "审核状态"),
            edit = @Edit(title = "审核状态", search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "RYMD014-1")
            )
    )
    private String auditStatus;

    // 人员密级
    @FabosJsonField(
            views = @View(title = "人员密级", show = false),
            edit = @Edit(title = "人员密级", show = false,
                    search = @Search,
//                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                                              fetchHandlerParams = "RYMD016")
            )
    )
    private String secretLevel;

    @FabosJsonField(
            views = @View(title = "到职日期"),
            edit = @Edit(title = "到职日期", search = @Search,
                    type = EditType.DATE
            )
    )
    private String employmentDate;

    @FabosJsonField(
            views = @View(title = "离职日期"),
            edit = @Edit(title = "离职日期", search = @Search,
                    type = EditType.DATE
            )
    )
    private String resignationDate;

    public boolean getIsAdmin() {
        return "Y".equals(this.getAdminFlag());
    }

    public boolean getIsMd5() {
        return true;
    }
}
