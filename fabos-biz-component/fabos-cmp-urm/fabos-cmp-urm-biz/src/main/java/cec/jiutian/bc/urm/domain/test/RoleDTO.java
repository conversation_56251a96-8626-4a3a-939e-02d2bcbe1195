package cec.jiutian.bc.urm.domain.test;

/**
 * <AUTHOR>
 * @description:
 */

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.Parameters;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.config.QueryExpression;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Id;
import lombok.Data;


@FabosJson(name = "角色管理测试",rowOperation = {@RowOperation(
        operationHandler = UserRoleDTOOperationHandler.class,
        code = "UserRoleDTO@TEST",
        mode = RowOperation.Mode.SINGLE,
        title = "自定义按钮测试",
        type = RowOperation.Type.FABOSJSON

)}
)
@FabosJsonI18n
@Data
@QueryModel(hql = "select new map (r.id as id,r.name as roleName,r.code as roleCode,r.description as description) from Role r  group by r.id,r.name ,r.code ,r.description ")
public class RoleDTO {
    @FabosJsonField(
            views = @View(title = "id"),
            edit = @Edit(title = "id"),
            customHqlField = "r.id"
    )
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "角色编码"),
            edit = @Edit(title = "角色编码", search = @Search())
    )
    private String roleCode;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.name"
    )
    private String roleName;


    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.description"
    )
    private String description;


}
