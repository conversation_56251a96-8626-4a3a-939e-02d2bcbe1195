package cec.jiutian.bc.urm.domain.role.entity;

import cec.jiutian.bc.urm.ModelRowPermission.entity.ModelRowPermission;
import cec.jiutian.bc.urm.domain.ModelColumnPermission.entity.ModelColumnPermission;
import cec.jiutian.bc.urm.domain.menu.entity.ExternalMenu;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.event.RoleAuthDistributeDataProxy;
import cec.jiutian.bc.urm.domain.role.event.RoleAuthOperationHandler;
import cec.jiutian.bc.urm.domain.role.event.RoleBindUserOperationHandler;
import cec.jiutian.bc.urm.domain.user.entity.UserByRoleView;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "fd_role")
@Getter
@Setter
@FabosJson(name = "权限管理",
        orderBy = "RoleAuthDistribute.sort asc, RoleAuthDistribute.createTime desc",
        dataProxy = RoleAuthDistributeDataProxy.class,
        power = @Power(add = false,delete = false),
        rowOperation = {
                @RowOperation(
                        operationHandler = RoleAuthOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "Role@AUTH",
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.TREE,
                        returnPopupDataField = "menus",
                        fabosJsonClass = Menu.class,
                        title = "分配权限",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Role@AUTH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        mode = RowOperation.Mode.SINGLE,
                        code = "Role@ExternalAuth",
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.MIX,
                        title = "应用权限管理",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Role@ExternalAuth"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
                @RowOperation(
                        operationHandler = RoleBindUserOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        code = "Role@User",
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.TABLE,
                        returnPopupDataField = "users",
                        fabosJsonClass = UserByRoleView.class,
                        title = "添加用户",
                        show = @ExprBool(
                                value = true,
                                params = "Role@User"
                        )
                )
        }
)
@FabosJsonI18n
public class RoleAuthDistribute extends MetaModel {

    public static final String CODE = "code";

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(edit = true),
                    search = @Search(vague = true))
    )
    @SubTableField
    private String code;

    @FabosJsonField(
            views = @View(title = "名称", toolTip = true),
            edit = @Edit(title = "名称", readonly = @Readonly(edit = true), search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "角色说明", toolTip = true),
            edit = @Edit(title = "角色说明",show = false)
    )
    @SubTableField
    private String description;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序",show = false, notNull = true, numberType = @NumberType(min = 1, max = 9999))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态",
                    type = EditType.BOOLEAN,
                    notNull = true,
                    readonly = @Readonly(edit = true),
                    defaultVal = "true",
                    search = @Search(vague = true, defaultVal = "true"),
                    boolType = @BoolType(trueText = "有效", falseText = "失效")
            )
    )
    private Boolean status = true;

    @JoinTable(
            name = "fd_role_menu",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "menu_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "菜单权限",show = false, export = false),
            edit = @Edit(
                    show = false,
                    title = "菜单权限",
                    filter = @Filter(value = "Menu.status = 1"),
                    type = EditType.TAB_TREE
            )
    )
    @ManyToMany(fetch = FetchType.EAGER)
    private List<Menu> menus;

    @JoinTable(
        name = "fd_role_menu_external",
        joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
        inverseJoinColumns = @JoinColumn(name = "menu_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
        @FabosJsonField(
                views = @View(title = "应用菜单权限",show = false, export = false),
                edit = @Edit(
                        show = false,
                        title = "应用菜单权限"
                )
        )
        @ManyToMany(fetch = FetchType.LAZY)
        private List<ExternalMenu> externalMenus;


    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "fd_user_role",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    )
    @FabosJsonField(
            views = @View(title = "用户列表",show = false, export = false),
            edit = @Edit(
                    show = false,
                    title = "用户列表",
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<UserByRoleView> users;

    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinTable(
            name = "fd_role_column_permission",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "permissions_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "列权限限制",column = "filedDisplayName", export = false,
                     type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "列权限限制",
                    type = EditType.TAB_TABLE_ADD,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<ModelColumnPermission> columnPermissions;

    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinTable(
            name = "fd_role_row_permission",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "permissions_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "行权限限制",column = "modelDisplayName", export = false,
                    type = ViewType.TABLE_VIEW),
            edit = @Edit(
                    title = "行权限限制",
                    type = EditType.TAB_TABLE_ADD,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<ModelRowPermission> rowPermissions;

}
