package cec.jiutian.bc.urm.config;

/**
 * <AUTHOR>
 * @description:
 */
public class AsyncContextHolder {
    private static final ThreadLocal<String> authorizationHolder = new ThreadLocal<>();
    public static void setAuthorization(String token) {
        if (token != null) {
            authorizationHolder.set(token);
        }
    }

    public static String getAuthorization() {
        return authorizationHolder.get();
    }

    public static void clear() {
        authorizationHolder.remove();
    }
}