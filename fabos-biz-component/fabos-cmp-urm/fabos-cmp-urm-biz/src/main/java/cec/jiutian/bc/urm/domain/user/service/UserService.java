package cec.jiutian.bc.urm.domain.user.service;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.enums.UnlockMethodEnum;
import cec.jiutian.bc.urm.domain.tenant.constant.TenantContext;
import cec.jiutian.bc.urm.domain.user.entity.FabosJsonUserConst;
import cec.jiutian.bc.urm.domain.user.entity.LockedUser;
import cec.jiutian.bc.urm.domain.user.entity.LoginModel;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.context.SecurityConfigContext;
import cec.jiutian.bc.urm.inbound.local.service.command.FabosJsonContextService;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.enums.SystemSecrecyConfigEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.service.FabosJsonApplication;
import cec.jiutian.core.prop.FabosJsonAppProp;
import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.core.prop.FabosJsonUpmsProp;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.meta.core.util.MD5Util;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Transactional
@Slf4j
public class UserService {

    private final FabosJsonDao fabosJsonDao;
    private final FabosJsonSessionService sessionService;
    private final HttpServletRequest request;
    private final FabosJsonAppProp fabosJsonAppProp;
    private final FabosJsonProp fabosJsonProp;
    private final FabosJsonUpmsProp fabosJsonUpmsProp;
    private final FabosJsonContextService contextService;
    private final JpaCrud jpaCrud;
    private final SystemConfig systemConfig;

    public static final String LOGIN_ERROR_HINT = "账户信息或密码错误";
    public static final String LOGIN_ERROR_NEED_PHONE = "请输入电话号码";


    public boolean checkPassword(User user, String password) {
        if (fabosJsonAppProp.getPwdTransferEncrypt()) {
            return password.equalsIgnoreCase(user.getPassword());
        } else {
            if (user.getIsMd5()) password = MD5Util.digest(password);
            return password.equals(user.getPassword());
        }
    }

    public boolean checkPassword(MetaUserinfo metaUserinfo, String password) {
        if (fabosJsonAppProp.getPwdTransferEncrypt()) {
            return password.equalsIgnoreCase(metaUserinfo.getPassword());
        } else {
            if (metaUserinfo.isMd5) password = MD5Util.digest(password);
            return password.equals(metaUserinfo.getPassword());
        }
    }

    public boolean checkAdminPassword(String account, String password){
        if(fabosJsonUpmsProp.getSuperAdminName().equals(account)){
            if (fabosJsonAppProp.getPwdTransferEncrypt()) {
                return password.equalsIgnoreCase(fabosJsonUpmsProp.getSuperAdminPassword());
            } else {
                String decryptPassword = MD5Util.digest(password);
                return fabosJsonUpmsProp.getSuperAdminPassword().equals(decryptPassword);
            }
        }
        return false;
    }

    public List<MetaUserinfo> getUserByRoleIds(List<String> items){
        String hql="from User mu left join Role r where mu.state='Y' and r.id IN :roleIds";
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("roleIds",items);
        List<User> userList=jpaCrud.select(hql,paramMap);
        if(CollectionUtils.isEmpty(userList)){
            return new ArrayList<>();
        }
        return userList.stream().map(it->getMetaUserInfo(it)).collect(Collectors.toList());
    }

    @Transactional
    public MetaUserinfo getMetaUserByAccount(String account) {
        User user = getUserByAccount(account);
        if(user==null){
            return null;
        }
        return getMetaUserInfo(user);
    }

    public User getUserByAccount(String account) {
        return fabosJsonDao.queryEntity(User.class, "account = :account", new HashMap<String, Object>(1) {{
            this.put("account", account);
        }});
    }

    public User getUserByPhoneNumber(String phoneNumber) {
        return fabosJsonDao.queryEntity(User.class, "phoneNumber = :phoneNumber", new HashMap<String, Object>(1) {{
            this.put("phoneNumber", phoneNumber);
        }});
    }

    @Transactional
    public MetaUserinfo getMetaUserByPhoneNumber(String phoneNumber) {
        User user = getUserByPhoneNumber(phoneNumber);
        if(user==null){
            return null;
        }
        return getMetaUserInfo(user);
    }


    public List<MetaUserinfo> getMetaUserByPhoneNumbers(List<String> phoneNumbers){
        String hql="from User mu where mu.state='Y' and mu.phoneNumber IN :phoneNumbers";
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("phoneNumbers",phoneNumbers);
        List<User> userList=jpaCrud.select(hql,paramMap);
        if(CollectionUtils.isEmpty(userList)){
            return new ArrayList<>();
        }
        return userList.stream().map(it->getMetaUserInfo(it)).collect(Collectors.toList());
    }

    public boolean checkVerifyCode(String account, String verifyCode) {
        Object loginError = sessionService.get(SessionKey.LOGIN_ERROR + account);
        long loginErrorCount = 0;
        if (null != loginError) loginErrorCount = Long.parseLong(loginError.toString());
        if (loginErrorCount >= fabosJsonAppProp.getVerifyCodeCount()) {
            if (StringUtils.isBlank(verifyCode)) return false;
            String key = SessionKey.VERIFY_CODE;
            Object vc = sessionService.get(key);
            sessionService.remove(key);
            return vc != null && vc.toString().equalsIgnoreCase(verifyCode);
        }
        return true;
    }

    @Transactional
    public FabosJsonApiModel changePassword(String account, String oldPassword, String newPassword, String confirmPassword) {
        if (!newPassword.equals(confirmPassword)) {
            return FabosJsonApiModel.errorNoInterceptMessage("修改失败，新密码与确认密码不匹配");
        }
        User user = getUserByAccount(account);
        if (null == user) {
            return FabosJsonApiModel.errorNoInterceptMessage(LOGIN_ERROR_HINT);
        }
//        if (!fabosJsonAppProp.getPwdTransferEncrypt()) {
//            oldPassword = MD5Util.digest(oldPassword);
//            newPassword = MD5Util.digest(newPassword);
//        }

        if (!oldPassword.equals(user.getPassword())) {
            return FabosJsonApiModel.errorNoInterceptMessage("密码错误");
        } else {
            //查询密码设置
            List<SystemSecurityConfig> select = jpaCrud.select(new SystemSecurityConfig());
            if(!CollectionUtils.isEmpty(select)){
                user.changePassword(newPassword,select.get(0).getPasswordMinLength(),Integer.parseInt(select.get(0).getPasswordComplexity()));
            }
//            user.setResetPwdTime(new Date());
            fabosJsonDao.getEntityManager().merge(user);
            return FabosJsonApiModel.successApi();
        }
    }

    @Transactional
    public Boolean resetPassword(String account) {
        User user = getUserByAccount(account);
        if (null == user) {
            throw new FabosJsonApiErrorTip("Not found account!");
        }
        String password=FabosJsonUserConst.DEFAULT_USER_PASSWORD;
        if (fabosJsonAppProp.getPwdTransferEncrypt()) {
            password = MD5Util.digest(password);
        }
        user.setPassword(password);
        fabosJsonDao.getEntityManager().merge(user);
        return true;
    }

    public LocalDateTime getExpireTime() {
        if (fabosJsonProp.isRedisSession()) {
            return LocalDateTime.now().plusMinutes(fabosJsonUpmsProp.getExpireTimeByLogin());
        } else {
            return LocalDateTime.now().plusSeconds(request.getSession().getMaxInactiveInterval());
        }
    }

    /**
     * 校验是否允许登录   如果登录失败次数超过配置次数 将不允许登录
     * @param account
     * @return
     */
    public boolean checkAllowLogin(String account) {
        Object loginError = sessionService.get(SessionKey.DISABLE_LOGIN_ACCOUNT + account);
        return null == loginError;
    }

    /**
     * 登录失败次数超过配置。禁止登录过后。该接口解锁
     * 重置登录失败次数
     * @param user
     */
    public void unlockUser(LockedUser user) {
        user.setLockedFlag(YesOrNoStatus.NO.getValue());
        user.setLockDate(null);
        user.setLockReason(null);
        fabosJsonDao.mergeAndFlush(user);
        sessionService.remove(SessionKey.DISABLE_LOGIN_ACCOUNT + user.getPhoneNumber());
        sessionService.remove(SessionKey.LOGIN_ERROR + user.getPhoneNumber());
    }
    public boolean checkToLogin(String account) {
        Object loginError = sessionService.get(SessionKey.LOGIN_ERROR + account);
        long loginErrorCount = 0;
        if (null != loginError) loginErrorCount = Long.parseLong(loginError.toString());
        return loginErrorCount >= fabosJsonAppProp.getVerifyToLoginCount();
    }

    /**
     * 登录失败次数   普通账户存在重复数据  需要使用电话号码  超级管理员没有电话号码   使用账号
     * @param accountOrPhoneNumber
     * @return
     */
    public boolean loginErrorCountPlus(String accountOrPhoneNumber) {
        String key = SessionKey.LOGIN_ERROR + accountOrPhoneNumber;
        Object loginError = sessionService.get(key);
        int loginErrorCount = 0;
        if (null != loginError) {
            loginErrorCount = Integer.parseInt(loginError.toString());
        }
        sessionService.put(key, ++loginErrorCount + "", fabosJsonUpmsProp.getExpireTimeByLogin(), TimeUnit.MINUTES);
        //登录失败超过限制 添加禁止登录账号
        //SecrecyEnum secrecyEnum = SecrecyEnum.getByValue(new SystemSecrecyConfig().getSecrecy());
        if(SystemSecrecyConfigEnum.get().getAllowLoginFailCount()<=loginErrorCount&&checkAllowLogin(accountOrPhoneNumber)){
            sessionService.put(SessionKey.DISABLE_LOGIN_ACCOUNT + accountOrPhoneNumber, loginErrorCount + "", SystemSecrecyConfigEnum.get().getDisableLoginHour(), TimeUnit.HOURS);
        }
        if (loginErrorCount >= SecurityConfigContext.getAuthenticationFailureCount()
                && UnlockMethodEnum.Manual.getCode().equals(SecurityConfigContext.getAccountUnlockMethod())) {
            // 如果超过最大失败次数，且为手动更新模式，则需要更新meta user表的isLocked字段
            UserContext.get().setNeedForceLock(true);
        }
        return loginErrorCount >= fabosJsonAppProp.getVerifyCodeCount();
    }

    //获取当前登录用户对象(数据库中查找)
    public User getCurrentUser() {
        String uid = this.getCurrentUid();
        return null == uid ? null : fabosJsonDao.getEntityManager().find(User.class, uid);
    }

    //获取当前用户ID
    public String getCurrentUid() {
        MetaUserinfo metaUserinfo = getSimpleUserInfo();
        return null == metaUserinfo ? null : metaUserinfo.getId();
    }

    //获取当前登录用户基础信息（缓存中查找）
    public MetaUserinfo getSimpleUserInfo() {
        Object info = sessionService.get(SessionKey.USER_INFO + contextService.getCurrentToken());
        try {
            return null == info ? null : JacksonUtil.fromJson(info.toString(), MetaUserinfo.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public MetaUserinfo getSimpleUserInfoByToken(String token) throws IOException {
        Object info = sessionService.get(SessionKey.USER_INFO + token);
        return null == info ? null : JacksonUtil.fromJson(info.toString(), MetaUserinfo.class);
    }

    public static LoginProxy findFabosJsonLogin() {
        if (null == FabosJsonApplication.getPrimarySource()) {
            throw new FabosJsonWebApiRuntimeException("Not found '@FabosJsonScan' Annotation");
        }
        FabosJsonLogin fabosJsonLogin = FabosJsonApplication.getPrimarySource().getAnnotation(FabosJsonLogin.class);
        if (null != fabosJsonLogin) {
            return FabosJsonSpringUtil.getBean(fabosJsonLogin.value());
        }
        return null;
    }

    public LoginModel superLogin(String account,String password,String verifyCode){
        if (!checkAllowLogin(account)) {
            return new LoginModel(false,"fabosUser_User_disable_login", false);
        }
        if (!checkVerifyCode(account, verifyCode)) {
            return new LoginModel(false,"fabosUser_Verification_code_error", true);
        }
        if(checkAdminPassword(account,password)){
            User user = getSuperAdminUser();
            request.getSession().invalidate();
            sessionService.remove(SessionKey.LOGIN_ERROR + account);
            return new LoginModel(true, user, getMetaUserInfo(user));
        }else {
            return new LoginModel(false, LOGIN_ERROR_HINT, loginErrorCountPlus(account));
        }
    }

    private void rejectLockedUser(User user) {
        if (YesOrNoStatus.YES.getValue().equals(user.getLockedFlag())) {
            throw new FabosJsonApiErrorTip("您的账户已被锁定："+user.getLockReason());
        }
    }

    public LoginModel getSuccessLoginModel(String account) {
        User user;
        if (isValidPhoneNumber(account)) {
            user = getUserListByPhone(account);
        } else {
            List<User> users = getUserListByAccount(account);
            if (CollectionUtils.isEmpty(users) || users.size() != 1) {
                throw new FabosJsonApiErrorTip("查询到多个用户信息，请检查数据：" + account);
            }
            user = users.get(0);
        }
        //超级管理员拥有所有权限  之前的逻辑超级管理员是root账户并且不会初始化到数据库  现在需要调整为superUser为超级管理员
        if(user.getAccount().equals(systemConfig.getSuperUserName())){
            user.setAdminFlag(YesOrNoStatus.YES.getValue());
        }
        return new LoginModel(true, user, getMetaUserInfo(user));
    }

    /**
     * 普通管理员登录存在相同账户  相同账户需要输入电话号码或者直接使用电话号码登录
     * @param account  可能是账号或者电话号码
     * @param password
     * @param verifyCode
     * @param phoneNumber
     * @return
     */
    public LoginModel login(String account,String password,String verifyCode,String phoneNumber) {
        User user;
        boolean needPhoneNumber = false;
        if(isValidPhoneNumber(account)){
            //电话号码
            user = getUserListByPhone(account);
        }else {
            //优先定位到登录的账户。后续需要使用电话号码校验
            List<User> users = getUserListByAccount(account);
            if(CollectionUtils.isEmpty(users)){
                return new LoginModel(false, UserService.LOGIN_ERROR_HINT, loginErrorCountPlus(account));
            }
            user = users.get(0);
            if(users.size()>1){
                needPhoneNumber = true;
                //需要电话号码
                if(StringUtils.isBlank(phoneNumber)){
                    return new LoginModel(false, UserService.LOGIN_ERROR_NEED_PHONE, false,needPhoneNumber);
                }else {
                    user= getUserListByPhone(phoneNumber);
                }
            }
        }
        if (null == user) {
            return new LoginModel(false, UserService.LOGIN_ERROR_HINT, loginErrorCountPlus(account),needPhoneNumber);
        }
        rejectLockedUser(user);
        //普通管理员都是用电话号码校验 不需要用账户  账户存在重复数据 超级管理员使用账号
        String dbPhoneNumber = user.getPhoneNumber();
        if (!checkAllowLogin(dbPhoneNumber)) {
            return new LoginModel(false,"fabosUser_User_disable_login", false,needPhoneNumber);
        }
        if (!checkVerifyCode(dbPhoneNumber, verifyCode)) {
            return new LoginModel(false,"fabosUser_Verification_code_error", true,needPhoneNumber);
        }
        if (!checkPassword(user, password)) {
            return new LoginModel(false, LOGIN_ERROR_HINT, loginErrorCountPlus(dbPhoneNumber),needPhoneNumber);
        }
        //超级管理员拥有所有权限  之前的逻辑超级管理员是root账户并且不会初始化到数据库  现在需要调整为superUser为超级管理员
        if(user.getAccount().equals(systemConfig.getSuperUserName())){
            user.setAdminFlag(YesOrNoStatus.YES.getValue());
        }
        if (!checkState(user.getState())) {
            if (UnlockMethodEnum.Auto.getCode().equals(SecurityConfigContext.getAccountUnlockMethod())) {
                // 如果是自动解锁模式，尝试解锁
                // 锁定时间
                if (null != user.getLockDate()
                        && SecurityConfigContext.getAccountAutoUnlockMinutes() > 0
                        && LocalDateTime.now().isAfter(user.getLockDate().plusMinutes(SecurityConfigContext.getAccountAutoUnlockMinutes()))) {
                    user.setState(SwitchStatus.ENABLED.getValue());
                    user.setLockDate(null);
                    user.setLockReason(null);
                    fabosJsonDao.merge(user);
                    sessionService.remove(SessionKey.DISABLE_LOGIN_ACCOUNT + user.getPhoneNumber());
                    sessionService.remove(SessionKey.LOGIN_ERROR + user.getPhoneNumber());
                }
                return new LoginModel(true, user, getMetaUserInfo(user));
            }
            return new LoginModel(false, "fabosUser_User_status_is_not_valid", loginErrorCountPlus(dbPhoneNumber),needPhoneNumber);
        }
        request.getSession().invalidate();
        sessionService.remove(SessionKey.LOGIN_ERROR + dbPhoneNumber);
        return new LoginModel(true, user, getMetaUserInfo(user));
    }
    private static boolean isValidPhoneNumber(String phoneNumber) {
        Pattern pattern = Pattern.compile(RegexConst.PHONE_REGEX);//和前端使用同一个正则
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }

    public User getUserListByPhone(String phoneNumber) {
        return fabosJsonDao.queryEntity(User.class, "phoneNumber = :phoneNumber", new HashMap<String, Object>(1) {{
            this.put("phoneNumber", phoneNumber);
        }});
    }
    //登录时无oid 可能存在重复账号
    public List<User> getUserListByAccount(String account) {
        return fabosJsonDao.queryEntityList(User.class, "account = :account", new HashMap<String, Object>(1) {{
            this.put("account", account);
        }});
    }
    public MetaUserinfo getMetaUserInfo(User user) {
        MetaUserinfo metaUserinfo = new MetaUserinfo();
        BeanUtils.copyProperties(user, metaUserinfo);
        metaUserinfo.setTenantId(user.getOid());
        metaUserinfo.setAdmin(user.getIsAdmin());
        metaUserinfo.setSecretLevel(user.getSecretLevel());
        if (Objects.nonNull(user.getOrg())) {
            metaUserinfo.setOrgId(user.getOrg().getId());
        }
        if (!CollectionUtils.isEmpty(user.getRoles())){
            metaUserinfo.setRoles(user.getRoles().stream().map(Role::getCode).collect(Collectors.toList()));
            metaUserinfo.setRoleIds(user.getRoles().stream().map(Role::getId).collect(Collectors.toList()));
        }
        return metaUserinfo;
    }

    public User getSuperAdminUser() {
        User user=new User();
        user.setName("平台超级管理员");
        user.setAccount(fabosJsonUpmsProp.getSuperAdminName());
        user.setPassword(fabosJsonUpmsProp.getSuperAdminPassword());
        user.setAdminFlag(YesOrNoStatus.YES.getValue());
        user.setId("1001");
        user.setOid(String.valueOf(TenantContext.ROOT_TENANT_ID));
        return user;
    }

    public boolean checkState(String state) {
        return SwitchStatus.ENABLED.getValue().equals(state);
    }

    public List<User> createUsers4Init(List<User> users) {
        Optional.ofNullable(users).ifPresent(users1 -> {
                    for (User user : users) {
                        String[] vals = new String[]{user.getPhoneNumber(),user.getOid()};
                        fabosJsonDao.persistIfNotExistWithTenantId(User.class, user, User.PHONE, vals);
                    }
                }
        );
        return users;
    }

    public void logoutToken(String token) {
        if (!fabosJsonProp.isRedisSession()) {
            request.getSession().invalidate();
        }
        sessionService.remove(SessionKey.OPEN_API_TOKENS + token);
    }

    public void createOpenApiToken(String token, String data, Integer expire) {
        sessionService.put(SessionKey.OPEN_API_TOKENS + token, data, expire, TimeUnit.SECONDS);
    }
}
