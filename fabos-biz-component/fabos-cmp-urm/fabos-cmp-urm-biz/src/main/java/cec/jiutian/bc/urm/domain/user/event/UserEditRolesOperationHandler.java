package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.view.fun.OperationHandler;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Query;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class UserEditRolesOperationHandler implements OperationHandler<User, Role> {
    @Resource
    private UserService userService;
    @Resource
    private EntityManagerFactory entityManagerFactory;
    @Override
    public String exec(List<User> data, Role modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }
//        if (!(curr.getRoles().contains(UserManagerTypeEnum.securityManager.getCode())||curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode()))) {
//            return "msg.error(当前登录用户无操作权限')";
//        }
        String userId = data.get(0).getId();
        EntityManager entityManager = entityManagerFactory.createEntityManager();
        try {
            entityManager.getTransaction().begin();
            if(param!=null){
                // 使用SQL进行删除操作
                String deletedSql = "DELETE FROM fd_user_role WHERE user_id = ?";
                Query dquery = entityManager.createNativeQuery(deletedSql);
                dquery.setParameter(1, userId); // 设置要删除记录的ID值
                dquery.executeUpdate(); // 执行删除操作
                for (String roleId:param) {
                    String sql="INSERT INTO fd_user_role (user_id, role_id) VALUES( ?1, ?2)";
                    Query query=entityManager.createNativeQuery(sql);
                    query.setParameter(1,userId);
                    query.setParameter(2,roleId);
                    query.executeUpdate();
                }
            }
            entityManager.flush();
            entityManager.getTransaction().commit();
        }catch (Exception e){
            entityManager.getTransaction().rollback();
        }finally {
            entityManager.close();
        }
        return "msg.success('成功')";
    }
}
