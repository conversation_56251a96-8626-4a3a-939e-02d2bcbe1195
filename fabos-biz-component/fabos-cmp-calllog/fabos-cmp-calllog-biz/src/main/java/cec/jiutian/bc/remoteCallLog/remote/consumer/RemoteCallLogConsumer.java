package cec.jiutian.bc.remoteCallLog.remote.consumer;

import cec.jiutian.api.remoteCallLog.config.RemoteCallLogMQConfig;
import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
import cec.jiutian.bc.remoteCallLog.service.RemoteCallLogService;
import cec.jiutian.common.util.JacksonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
public class RemoteCallLogConsumer {

    @Resource
    private RemoteCallLogMQConfig remoteCallLogMQConfig;

    @Resource
    private RemoteCallLogService remoteCallLogService;


    @RabbitListener(queues = "#{@remoteCallLogMQConfig.getQueueName()}")
    public void receiveMessage(String message) {
        log.info("Received remoteCallLog message: " + message);
        RemoteCallLog remoteCallLog = null;
        try {
            remoteCallLog = JacksonUtil.fromJson(message, RemoteCallLog.class);
        } catch (IOException e) {
            log.error("消息json转换失败：{}", message);
            return;
        }
        try {
            remoteCallLogService.saveRemoteCallLog(remoteCallLog);
        } catch (Exception e) {
            log.error("消息保存失败：{}", e.getMessage());
        }
    }
}
