package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/10/31 17:01
 * @description：
 */
public class SupplierStatusEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (SupplierStatusEnum.Enum data : SupplierStatusEnum.Enum.values()) {
            list.add(new VLModel(data.getCode(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Effective("1", "生效"),
        Invalid("2", "失效");

        private final String code;
        private final String value;

    }
}
