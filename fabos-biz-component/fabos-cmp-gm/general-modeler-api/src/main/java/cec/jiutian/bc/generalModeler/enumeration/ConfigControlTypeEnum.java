package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class ConfigControlTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (ConfigControlTypeEnum.Enum data : ConfigControlTypeEnum.Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        UpperLimit("控制上限"),
        LowLimit("控制下限"),
        Both("控制上下限");

        private final String value;

    }
}
