package cec.jiutian.bc.lcp.domain.lowCodeMenu.event;

import cec.jiutian.bc.lcp.domain.lowCodeMenu.constant.LowCodeMenuStatusEnum;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.LowCodeMenuService;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Service
public class LowCodeMenuDataProxy implements DataProxy<LowCodeMenu> {

    private final LowCodeMenuService menuService;

    private final FabosJsonSessionService sessionService;

    public LowCodeMenuDataProxy(LowCodeMenuService menuService, FabosJsonSessionService sessionService) {
        this.menuService = menuService;
        this.sessionService = sessionService;
    }

    @Override
    public void beforeAdd(LowCodeMenu menu) {
        menuService.generateVersion(menu);
        menu.setModuleCode(menu.generateCode(8));
        menu.setModuleTypeCode(MenuTypeEnum.LOWCODE.getCode());
        menu.setStatus(LowCodeMenuStatusEnum.UNRELEASED.getValue());
        menu.setCreateTime(LocalDateTime.now());
        menu.setCreateBy(UserContext.getAccount());
        menu.setUpdateTime(LocalDateTime.now());
        menu.setUpdateBy(UserContext.getAccount());
    }

    @Override
    public void beforeUpdate(LowCodeMenu menu) {
        menu.setUpdateTime(LocalDateTime.now());
        menu.setUpdateBy(UserContext.getAccount());
    }



}
