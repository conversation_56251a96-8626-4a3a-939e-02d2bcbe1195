package cec.jiutian.bc.lcp.domain.lowCodeMenu.service;

import cec.jiutian.bc.lcp.domain.lowCodeMenu.constant.LowCodeMenuStatusEnum;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.bc.lcp.outbound.adapter.repository.LowCodeMenuRepository;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.query.LowCodeMenuQueryDTO;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.context.MetaContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LowCodeMenuService {
    private final FabosJsonDao fabosJsonDao;

    private final JpaCrud jpaCrud;
    private final LowCodeMenuRepository lowCodeMenuRepository;

    public LowCodeMenuService(FabosJsonDao fabosJsonDao, JpaCrud jpaCrud, LowCodeMenuRepository lowCodeMenuRepository) {
        this.fabosJsonDao = fabosJsonDao;
        this.jpaCrud = jpaCrud;
        this.lowCodeMenuRepository = lowCodeMenuRepository;
    }


    public Page<LowCodeMenu> getAllLowCodeMenuList(LowCodeMenuQueryDTO query) {
//        String hql = "from LowCodeMenu m where
//        (m.parentId, m.version) in
//        (select n.parentId, MAX(n.version) from LowCodeMenu n group by n.parentId, n.version)
//        order by m.version desc,m.createTime desc";
//        return jpaCrud.select(hql);
        Specification<LowCodeMenu> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if(StringUtils.isNotBlank(query.getId())){
                predicates.add(criteriaBuilder.equal(root.get("id"), query.getId()));
            }
            if(StringUtils.isNotBlank(query.getName())){
                predicates.add(criteriaBuilder.like(root.get("name"), "%" +query.getName() + "%"));
            }
            if(StringUtils.isNotBlank(query.getParentId())){
                predicates.add(criteriaBuilder.equal(root.get("parentId"), query.getParentId()));
            }
            // 子查询：找出每个组合的适当版本
            Subquery<Integer> subquery = criteriaQuery.subquery(Integer.class);
            Root<LowCodeMenu> subRoot = subquery.from(LowCodeMenu.class);

            // 构建 CASE 表达式，只在状态为4时考虑版本号
            Expression<Integer> caseExpression = criteriaBuilder.<Integer>selectCase()
                    .when(criteriaBuilder.equal(subRoot.get("status"), 4), subRoot.get("version"))
                    .otherwise(criteriaBuilder.nullLiteral(Integer.class));  // 明确指定返回 null 的类型

            // 聚合 CASE 表达式以找到可能的最大版本
            Expression<Integer> maxVersionWhenStatusFour = criteriaBuilder.max(caseExpression);
            Expression<Integer> maxVersion = criteriaBuilder.max(subRoot.get("version"));

            // 选择适当的版本
            subquery.select(criteriaBuilder.coalesce(maxVersionWhenStatusFour, maxVersion))
                    .where(criteriaBuilder.or(
                            criteriaBuilder.and(criteriaBuilder.equal(subRoot.get("parentId"), root.get("parentId")),
                                    criteriaBuilder.equal(subRoot.get("name"), root.get("name"))),
                            criteriaBuilder.and(criteriaBuilder.isNull(subRoot.get("parentId")),
                                    criteriaBuilder.isNull(root.get("parentId")),
                                    criteriaBuilder.equal(subRoot.get("name"), root.get("name")))
                    ))
                    .groupBy(subRoot.get("parentId"), subRoot.get("name"));

            // Ensure we're selecting where the version matches the max version found
            predicates.add(criteriaBuilder.in(root.get("version")).value(subquery));
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
        Pageable pageable = PageRequest.of((query.getPage()-1), query.getPerPage(), Sort.by(Sort.Direction.DESC,"createTime").descending());
        return lowCodeMenuRepository.findAll(specification,pageable);

    }

    public void generateVersion(LowCodeMenu menu) {
        String hql = "from LowCodeMenu m where m.parentId= :parentId and m.name= :name  order by m.version desc ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", menu.getParentId());
        paramMap.put("name", menu.getName());
        List<LowCodeMenu> list = jpaCrud.select(hql, paramMap);
        if (!CollectionUtils.isEmpty(list)) {
            menu.setVersion(list.get(0).getVersion() == null ? 0 : list.get(0).getVersion() + 1);
        } else {
            menu.setVersion(1);
        }

    }

    public LowCodeMenu getById(String id) {
        return jpaCrud.getById(LowCodeMenu.class, id);
    }
    public LowCodeMenu releaseLowCodeMenu(LowCodeMenu menu) {
        menu.setStatus(LowCodeMenuStatusEnum.RELEASED.getValue());
        menu.setUpdateTime(LocalDateTime.now());
        menu.setUpdateBy(MetaContext.getUser().getName());
        List<LowCodeMenu> list = updateOtherMenuStatus(menu);
        list.add(menu);
        list.forEach(fabosJsonDao::mergeAndFlush);
        return menu;
    }

    private List<LowCodeMenu> updateOtherMenuStatus(LowCodeMenu menu){
        String hql = "from LowCodeMenu m where m.parentId= :parentId and m.name= :name and id != :id  order by m.version desc ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", menu.getParentId());
        paramMap.put("name", menu.getName());
        paramMap.put("id", menu.getId());
        List<LowCodeMenu> list = jpaCrud.select(hql, paramMap);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().peek(e -> {
                e.setUpdateTime(LocalDateTime.now());
                e.setUpdateBy(MetaContext.getUser().getName());
                e.setStatus(LowCodeMenuStatusEnum.UNRELEASED.getValue());
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public boolean checkReleaseLowCodeMenu(LowCodeMenu menu) {
        String fabosJson = menu.getFabosJson();
        if (StringUtils.isAllBlank(fabosJson)) {
            throw new FabosJsonApiErrorTip("页面设计数据为空，不能发布");
        }
        return true;
    }
}
