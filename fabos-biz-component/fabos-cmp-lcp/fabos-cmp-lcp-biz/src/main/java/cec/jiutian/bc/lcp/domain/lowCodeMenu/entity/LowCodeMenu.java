package cec.jiutian.bc.lcp.domain.lowCodeMenu.entity;

import cec.jiutian.bc.lcp.domain.lowCodeMenu.event.LowCodeMenuDataProxy;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.RandomStringUtils;

@FabosJson(name = "低代码页面管理",
        orderBy = "LowCodeMenu.version desc, LowCodeMenu.createTime desc",
        dataProxy = {LowCodeMenuDataProxy.class})
@Table(name = "fd_low_code_menu")
@Entity
@Getter //@Data重写hashcode 方法导致了栈溢出，所以使用setter，getter代替
@Setter
@TemplateType
public class LowCodeMenu extends MetaModel {

    @FabosJsonField(
            views = @View(title = "上级树节点"),
            edit = @Edit(title = "上级树节点", search = @Search(vague = true))
    )
    private String parentId;

    @FabosJsonField(
            views = @View(
                    title = "上级树节点名称"),
            edit = @Edit(
                    title = "上级树节点名称")
    )
    private String parentName;


    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true), readonly = @Readonly(add = false), inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    private String name;

    @FabosJsonField(
            edit = @Edit(title = "类型值")
    )
    private String moduleValue;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            edit = @Edit(title = "编码", readonly = @Readonly)
    )
    private String moduleCode;

    @FabosJsonField(
            edit = @Edit(
                    title = "菜单类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuTypeEnum.ChoiceFetch.class}
                    )
            )
    )
    private String moduleTypeCode;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属组件", column = "name"),
            edit = @Edit(title = "所属组件",
                    filter = @Filter(value = "FabModule.state = 'activated'"),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    search = @Search)
    )
    private FabModule ownComponent;


    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(
                    title = "状态",
                    type = EditType.CHOICE,
                    defaultVal = "5",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuStatus.ChoiceFetch.class}
                    )
            )
    )
    private Integer status;

    @FabosJsonField(
            views = @View(title = "页面展示标题"),
            edit = @Edit(title = "页面展示标题", inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    private String pageTitleText;

    @FabosJsonField(
            views = @View(title = "图标"),
            edit = @Edit(title = "图标", type = EditType.select_icon))
    private String moduleIconText;

    @FabosJsonField(
            views = @View(title = "排序"),
            edit = @Edit(title = "排序", numberType = @NumberType(min = 0))
    )
    private Integer sequenceNumber;


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注")
    )
    private String moduleDescription;

    @Column(columnDefinition = "TEXT")
    @FabosJsonField(
            views = @View(title = "低代码引擎渲染json", show = false),
            edit = @Edit(title = "低代码引擎渲染json", show = false)
    )
    private String fabosJson;

    @FabosJsonField(
            views = @View(title = "低代码引擎页面版本"),
            edit = @Edit(title = "低代码引擎页面版本", readonly = @Readonly)
    )
    private Integer version;

    public String generateCode(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }

}
