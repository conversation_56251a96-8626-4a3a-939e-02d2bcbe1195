package cec.jiutian.bc;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Slf4j
@SpringBootApplication(scanBasePackages = {
        "cec.jiutian"
})
@ComponentScan(basePackages = {
        "cec.jiutian"
})
@FabosJsonScan(value = {
        "cec.jiutian"
})
//@EnableJpaRepositories(basePackages = "cec.jiutian")
@EntityScan(basePackages = {
        "cec.jiutian"
})
public class CmpLcpApp {
    private static final Logger LOGGER = LoggerFactory.getLogger(CmpLcpApp.class);

    public static void main(String[] args) throws IOException {
        Properties properties = PropertiesLoaderUtils.loadAllProperties("application.properties");
        properties.forEach((k, v) -> MDC.put(k.toString(), v.toString()));
        ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
        ClassLoader classLoader = CmpLcpApp.class.getClassLoader();
        System.out.println(classLoader.equals(systemClassLoader));
        System.out.println("类加载器名称:" + classLoader.getName());
        SpringApplication.run(CmpLcpApp.class, args);

        LOGGER.info("lcpApplication start!");
        LOGGER.info("Spring Boot Version: "
                + SpringApplication.class.getPackage().getImplementationVersion());
        LOGGER.info("lcpApplication classLoader: " + CmpLcpApp.class.getClassLoader());
    }
}
