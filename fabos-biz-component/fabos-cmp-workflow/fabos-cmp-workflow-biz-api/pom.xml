<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-cmp-workflow</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fabos-cmp-workflow-biz-api</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.52</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-ecs-biz-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
