package cec.jiutian.bc.flow.domain.model.vo;

import cec.jiutian.view.config.Comment;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ProcessInstanceInfoVO implements Serializable {


    /**
     * 流程实例发起人。
     */
    @Comment(value = "流程实例发起人")
    private String startUserAccount;

    /**
     * 流程实例发起人名称。
     */
    @Comment(value = "流程实例发起人名称")
    private String startUserName;

    /**
     * 模型名称
     */
    @Comment(value = "模型名称")
    private String modelName;

    /**
     * 表单数据
     */
    @Comment(value = "表单数据")
    private String formData;

    /**
     * 申请原因
     */
    @Comment(value = "申请原因")
    private String reason;

    /**
     * 流程实例创建时间。
     */
    @Comment(value = "流程实例创建时间")
    private LocalDateTime processInstanceStartTime;

    /**
     * 流程实例结束时间。
     */
    @Comment(value = "流程实例结束时间")
    private LocalDateTime processInstanceEndTime;


    /**
     * 流程定义Id。
     */
    @Comment(value = "流程定义Id")
    private String processDefinitionId;


    /**
     * 流程定义名称。
     */
    @Comment(value = "流程定义名称")
    private String processDefinitionName;

    /**
     * 流程实例Id。
     */
    @Comment(value = "流程实例Id")
    private String processInstanceId;

    /**
     * 流程实例名称。
     */
    @Comment(value = "流程实例名称")
    private String processInstanceName;

    /**
     * 流程定义标识。
     */
    @Comment(value = "流程定义标识")
    private String processDefinitionKey;

    /**
     * 流程实例状态。
     */
    @Comment(value = "流程实例状态")
    private Integer status;

    /**
     * 流程实例状态名
     */
    @Comment(value = "流程实例状态名称")
    private String statusName;

    /**
     * 流程实例主表业务数据主键。
     */
    @Comment(value = "流程实例主表业务数据主键")
    private String businessKey;

    /**
     * 当前处理人,多个处理人时逗号隔
     */
    @Comment(value = "当前处理人,多个处理人时逗号隔开")
    private String currentAssignees;

    /**
     * 任务列表
     */
    @Comment(value = "任务列表")
    List<FlowTaskVO> taskList;
}
