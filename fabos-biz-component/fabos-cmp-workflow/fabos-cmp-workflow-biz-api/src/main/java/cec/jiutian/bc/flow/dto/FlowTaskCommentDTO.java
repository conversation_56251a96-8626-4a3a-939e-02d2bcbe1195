package cec.jiutian.bc.flow.dto;

import cec.jiutian.view.config.Comment;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FlowTaskCommentDTO {

    /**
     * 流程任务触发按钮类型，内置值可参考FlowTaskButton。
     */
    @Comment(value = "流程任务触发按钮类型")
    @NotNull(message = "数据验证失败，任务的审批类型不能为空！")
    private String approvalType;

    /**
     * 流程任务的批注内容。
     */
    @Comment(value = "流程任务的批注内容")
    @NotBlank(message = "数据验证失败，任务审批内容不能为空！")
    private String comment;

    /**
     * 委托指定人，比如加签、转办等。
     */
    @Comment(value = "委托指定人，比如加签、转办等")
    private String delegateAssginee;

    public FlowTaskCommentDTO(){}

    public FlowTaskCommentDTO(String approvalType, String comment) {
        this.approvalType = approvalType;
        this.comment = comment;
    }
}
