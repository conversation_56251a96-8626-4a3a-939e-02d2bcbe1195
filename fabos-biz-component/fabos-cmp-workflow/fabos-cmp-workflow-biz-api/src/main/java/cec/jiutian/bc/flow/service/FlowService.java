package cec.jiutian.bc.flow.service;

import cec.jiutian.bc.flow.domain.model.vo.ProcessInstanceInfoVO;
import cec.jiutian.bc.flow.dto.FlowTaskCompleteDTO;
import cec.jiutian.bc.flow.message.command.FlowStartOrCompleteDTO;

public interface FlowService {
    String startOrCompleteTask(FlowStartOrCompleteDTO flowStartOrCompleteDTO);

    ProcessInstanceInfoVO getProcessInstanceInfoById(String processInstanceId);

    Boolean submitUserTask(FlowTaskCompleteDTO flowTaskCompleteDTO);

}
