package cec.jiutian.bc.flow.event;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.flow.enums.ExamineConstantEnum;
import cec.jiutian.bc.flow.message.command.FabosWorkflowOrder;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.view.fun.FlowProxy;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

@Slf4j
@Component
public class FlowStatusUpdate {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private FabosJsonSpringUtil fabosJsonSpringUtil;

    @Transactional

    public void onEvent(FabosWorkflowEvent event) {
        //通过编码拿到
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJsonView(event.getBusinessCategory());
        if (fabosJsonModel == null) {
            throw new FabosJsonApiErrorTip("流程编码配置有误");
        }
        FabosWorkflowOrder flowEvent = (FabosWorkflowOrder) event.getEntity();
        if (flowEvent.getParamData() == null) {
            throw new FabosJsonApiErrorTip("表单数据为空");
        }
        JSONObject paramData = flowEvent.getParamData();
        String dataJson = (String) paramData.get(ExamineConstantEnum.EXAMINE_MODEL_DATA.name());
        ExamineModel model = null;
        try {
            Object object = JacksonUtil.fromJson(dataJson, fabosJsonModel.getClazz());
            ExamineModel examineModel = (ExamineModel) object;
            model = (ExamineModel) fabosJsonDao.findById(fabosJsonModel.getClazz(), examineModel.getId());
        } catch (IOException e) {
            throw new FabosJsonApiErrorTip(fabosJsonModel.getFabosJsonName() + " 数据转换异常:" + e.getMessage());
        }
        //更新状态
        EventTypeHandler.handleEvent(event, model);
        fabosJsonDao.mergeAndFlush(model);
        FlowProxy bean = fabosJsonSpringUtil.getBean(fabosJsonModel.getFabosJson().flowProxy()[0]);
        try {
            bean.onEvent(event, model);
        }catch (FabosJsonApiErrorTip | ServiceException e) {
            log.error("流程回调异常", e);
            throw e;
        } catch (Exception e) {
            log.error("流程回调异常", e);
            throw new FabosJsonApiErrorTip("流程回调业务侧发生异常" );
        }
    }
}
