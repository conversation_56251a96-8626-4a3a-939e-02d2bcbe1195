package cec.jiutian.bc.flow.enums;

import cec.jiutian.core.frame.constant.GenderEnum;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum ExamineStatusEnum {
    UNAUDITED("0", "未提交"),
    AUDITED("1", "审批通过"),
    REJECTED("2", "已驳回"),
    AUDITING("3","审批中"),
    DELETED("4","已删除"),
    CANCEL("5","已取消"),
    REVOKE("6","已撤回"),
    ;
    private String code;
    private String label;

    ExamineStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }


    private static HashMap<String, String> codeLabelMap = new HashMap();

    static {
        for (ExamineStatusEnum statusEnum : ExamineStatusEnum.values()) {
            codeLabelMap.put(statusEnum.getCode(), statusEnum.getLabel());
        }
    }

    public static String getLabelByCode(String  code) {
        return codeLabelMap.get(code);
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(ExamineStatusEnum.values()).map(examineStatusEnum ->
                    new VLModel(examineStatusEnum.getCode()+"", examineStatusEnum.getLabel())).collect(Collectors.toList());
        }

    }
}
