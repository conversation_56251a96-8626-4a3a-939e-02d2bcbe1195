package cec.jiutian.bc.flow.domain.model.event;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class ExamineModelDataProxy implements DataProxy<ExamineModel> {

    @Override
    public void beforeAdd(ExamineModel examineModel) {
        //新增的数据均为未提交状态
        examineModel.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
    }

    @Override
    public void beforeUpdate(ExamineModel examineModel) {
        if (ExamineStatusEnum.AUDITING.getCode().equals(examineModel.getExamineStatus())) {
            throw new FabosJsonApiErrorTip("单据状态为审批中，不允许修改");
        }
    }

    @Override
    public void beforeDelete(ExamineModel examineModel) {
        if (ExamineStatusEnum.AUDITING.getCode().equals(examineModel.getExamineStatus())) {
            throw new FabosJsonApiErrorTip("单据状态为审批中，不允许删除");
        }
        if (ExamineStatusEnum.AUDITED.getCode().equals(examineModel.getExamineStatus())) {
            throw new FabosJsonApiErrorTip("单据审批通过，不允许删除");
        }
    }
}
