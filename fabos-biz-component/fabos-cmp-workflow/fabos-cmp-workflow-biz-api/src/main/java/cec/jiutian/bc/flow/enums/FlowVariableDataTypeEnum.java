package cec.jiutian.bc.flow.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum FlowVariableDataTypeEnum {

    String("string", "字符串"),
    Number("number", "数值"),
    Bool("bool", "布尔"),
    DateTime("datetime", "日期时间"),
    ;
    private String code;
    private String label;

    FlowVariableDataTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(FlowVariableDataTypeEnum.values()).map(flowVariableDataTypeEnum ->
                    new VLModel(flowVariableDataTypeEnum.getCode() + "", flowVariableDataTypeEnum.getLabel())).collect(Collectors.toList());
        }
    }
}
