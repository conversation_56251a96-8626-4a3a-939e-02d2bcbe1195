package cec.jiutian.bc.flow.message.command;


import cec.jiutian.bc.flow.event.FabosWorkflowEventType;

public enum FabosWorkflowProcessEventType implements FabosWorkflowEventType {

    SUBMITTED,
    PROCESSING,
    REFUSED,
    COMPLETE,
    STOPPED,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DELETE,
    WITHDRAW,
    ;
    public static final FabosWorkflowProcessEventType[] EMPTY_ARRAY = new FabosWorkflowProcessEventType[0];
}
