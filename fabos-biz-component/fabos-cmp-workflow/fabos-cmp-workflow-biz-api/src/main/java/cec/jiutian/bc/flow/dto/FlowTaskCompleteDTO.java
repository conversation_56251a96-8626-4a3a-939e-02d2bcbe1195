package cec.jiutian.bc.flow.dto;

import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FlowTaskCompleteDTO {
    /**
     * 流程实例Id。
     */
    @NotBlank
    private String processInstanceId;

    /**
     * 审批意见
     */
    @NotNull
    private FlowTaskCommentDTO flowTaskComment;

    /**
     * 任务ID
     */
    @NotBlank
    String taskId;

    /**
     * 流程变量数据
     */
    private JSONObject taskVariableData;
}
