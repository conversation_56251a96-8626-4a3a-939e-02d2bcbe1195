package cec.jiutian.bc.flow.controller;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.flow.domain.model.vo.ProcessInstanceInfoVO;
import cec.jiutian.bc.flow.dto.FlowTaskCommentDTO;
import cec.jiutian.bc.flow.dto.FlowTaskCompleteDTO;
import cec.jiutian.bc.flow.enums.ExamineConstantEnum;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.flow.enums.FlowApprovalType;
import cec.jiutian.bc.flow.invock.FlowProxyInvoke;
import cec.jiutian.bc.flow.message.command.FlowStartOrCompleteDTO;
import cec.jiutian.bc.flow.service.FlowService;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.R;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.frame.service.ModelRenderService;
import cec.jiutian.core.frame.util.FabosJsonPowerUtil;
import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.core.view.fabosJson.util.InheritStrategyUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.view.fun.FlowProxy;
import cec.jiutian.view.fun.PowerObject;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_FLOW)
@RequiredArgsConstructor
public class FabJsonFlowController {

    @Resource
    private FlowService flowService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private ModelRenderService modelRenderService;

    /**
     * 提交审批
     *
     * @param fabosJsonName
     * @param data
     * @return
     */
    @Transactional
    @PostMapping("/submitFlow/{fabosJson}")
    public R<Object> submitFlow(@PathVariable("fabosJson") String fabosJsonName, @RequestBody JSONObject data) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJsonView(fabosJsonName);
        if (data == null || data.isEmpty() || !data.containsKey("id")) {
            throw new FabosJsonApiErrorTip("审批表单数据不能为空");
        }
        // 权限校验
        FabosJsonPowerUtil.checkPowerLegal(fabosJsonModel, PowerObject::isExamine);
        String flowCode = fabosJsonModel.getFabosJson().flowCode();
        if (StringUtils.isBlank(flowCode)) {
            throw new FabosJsonApiErrorTip(fabosJsonName + "模型FabosJson中未配置流程编码：flowCode");
        }
        Object obj = fabosJsonDao.findById(fabosJsonModel.getClazz(), data.getString("id"));
        if (!(obj instanceof ExamineModel examineModel)) {
            throw new FabosJsonApiErrorTip(fabosJsonName + "模型实体未实现审批相关功能");
        }
        JSONObject examineData = null;
        try {
            Map<String, Object> map = modelRenderService.singleRender(fabosJsonModel, data.getString("id"));
            String modelJson = JacksonUtil.toJson(map);
            examineData = new JSONObject();
            examineData.put(ExamineConstantEnum.EXAMINE_MODEL_DATA.name(), modelJson);
        } catch (JsonProcessingException e) {
            throw new FabosJsonApiErrorTip("模型实体Json转换失败，请检查模型结构：" + e.getMessage());
        }
        if (Objects.equals(ExamineStatusEnum.REJECTED.getCode(), examineModel.getExamineStatus())) {
            FlowTaskCompleteDTO completeDTO = new FlowTaskCompleteDTO();
            completeDTO.setProcessInstanceId(examineModel.getProcessInstanceId());
            FlowTaskCommentDTO flowTaskCommentDTO = new FlowTaskCommentDTO();
            flowTaskCommentDTO.setComment("提交审批");
            flowTaskCommentDTO.setApprovalType(FlowApprovalType.AGREE);
            completeDTO.setFlowTaskComment(flowTaskCommentDTO);
            completeDTO.setTaskVariableData(examineData);
            Boolean b = flowService.submitUserTask(completeDTO);
            if (!b) {
                throw new FabosJsonApiErrorTip("审批流程处理失败");
            }
            examineModel.setExamineStatus(ExamineStatusEnum.AUDITING.getCode());
            fabosJsonDao.mergeAndFlush(examineModel);
            return R.ok(examineModel.getProcessInstanceId());
        }
        if (!Objects.equals(ExamineStatusEnum.UNAUDITED.getCode(), examineModel.getExamineStatus())) {
            throw new FabosJsonApiErrorTip(ExamineStatusEnum.getLabelByCode(examineModel.getExamineStatus()) + "状态，不能发起审批");
        }
        //提交审批前扩展方法
        InheritStrategy inheritStrategy = InheritStrategyUtil.getInheritStrategy(fabosJsonModel);
        Class<? extends FlowProxy> flowProxy = FlowProxyInvoke.getFlowProxy(fabosJsonModel, inheritStrategy);
        try {
            FlowProxyInvoke.invoke(flowProxy, (proxy -> proxy.beforeSubmit(examineModel)));
        } catch (FabosJsonApiErrorTip e) {
            log.error("提交审批后扩展方法调用异常", e);
            throw e;
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip("提交审批前扩展方法调用出错：" + e.getMessage());
        }
        //发起流程
        String instanceId = doStartFlow(InheritStrategyUtil.getFlowCode(fabosJsonModel, inheritStrategy), examineModel, examineData);
        //提交审批后扩展方法
        try {
            FlowProxyInvoke.invoke(flowProxy, (proxy -> proxy.afterSubmit(examineModel)));
        } catch (FabosJsonApiErrorTip e) {
            log.error("提交审批后扩展方法调用异常", e);
            throw e;
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip("提交审批后扩展方法调用出错：" + e.getMessage());
        }
        return R.ok(instanceId);
    }

    private String doStartFlow(String flowCode, ExamineModel examineModel, JSONObject data) {
        FlowStartOrCompleteDTO flowStartOrCompleteDTO = new FlowStartOrCompleteDTO();
        flowStartOrCompleteDTO.setBusinessCategory(flowCode);
        flowStartOrCompleteDTO.setBusinessKey(examineModel.getId());
        flowStartOrCompleteDTO.setFormData(data);
        flowStartOrCompleteDTO.setProcessDefinitionKey(flowCode);
        JSONObject copyConfig = flowStartOrCompleteDTO.getCopyConfig();
        if (copyConfig != null) {
            copyConfig.putAll(data);
        } else {
            flowStartOrCompleteDTO.setCopyConfig(data);
        }
        String instanceId = flowService.startOrCompleteTask(flowStartOrCompleteDTO);
        //绑定流程
        examineModel.setExamineStatus(ExamineStatusEnum.AUDITING.getCode());
        examineModel.setProcessInstanceId(instanceId);
        fabosJsonDao.mergeAndFlush(examineModel);
        return instanceId;
    }


    //查询审批信息
    @PostMapping("/getFlowInfo/{fabosJson}/{flowId}")
    public R<Object> getFlowInfo(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("flowId") String dataId) {
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJsonView(fabosJsonName);
        FabosJsonPowerUtil.checkPowerLegal(fabosJsonModel, PowerObject::isExamineDetails);
        ProcessInstanceInfoVO processInstanceInfoVO = flowService.getProcessInstanceInfoById(dataId);
        return R.ok(processInstanceInfoVO);
    }


}
