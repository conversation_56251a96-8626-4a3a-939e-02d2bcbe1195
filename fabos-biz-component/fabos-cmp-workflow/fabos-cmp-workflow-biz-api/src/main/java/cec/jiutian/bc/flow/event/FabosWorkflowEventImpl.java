package cec.jiutian.bc.flow.event;

public class FabosWorkflowEventImpl implements FabosWorkflowEvent{

    private String businessCategory;
    private FabosWorkflowEventType type;
    private Object entity;

    @Override
    public FabosWorkflowEventType getType() {
        return this.type;
    }

    @Override
    public Object getEntity() {
        return this.entity;
    }

    public void setEntity(Object entity) {
        this.entity = entity;
    }

    @Override
    public String getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    public void setType(FabosWorkflowEventType type) {
        this.type = type;
    }

    public FabosWorkflowEventImpl(){}

    public FabosWorkflowEventImpl(FabosWorkflowEventType type, Object entity) {
        this.type = type;
        this.entity = entity;
    }
}
