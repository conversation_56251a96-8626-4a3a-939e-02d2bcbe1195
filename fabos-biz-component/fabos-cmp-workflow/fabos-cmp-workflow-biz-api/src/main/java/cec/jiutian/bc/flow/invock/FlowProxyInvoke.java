package cec.jiutian.bc.flow.invock;

import cec.jiutian.core.view.fabosJson.InheritStrategy;
import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.view.fun.FlowProxy;

import java.util.function.Consumer;

public class FlowProxyInvoke {

    public static void invoke(Class<? extends FlowProxy> flowProxy, Consumer<FlowProxy> consumer){
        consumer.accept( FabosJsonSpringUtil.getBean(flowProxy));
    }

    public static Class<? extends FlowProxy> getFlowProxy(FabosJsonModel fabosJsonModel, InheritStrategy inheritStrategy) {
        if (inheritStrategy != null && inheritStrategy.flowExtend().extendFlag()) {
           return inheritStrategy.flowExtend().patentFlowProxy();
        }
        Class<? extends FlowProxy>[] flowProxy = fabosJsonModel.getFabosJson().flowProxy();
        return flowProxy.length > 0 ? flowProxy[0] : null;
    }
}
