package cec.jiutian.bc.flow.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> @date
 */
@Getter
public enum FlowTaskAssigneeTypeEnum {

    User("USER", "用户"),
    Org("ORG", "部门"),
    Role("ROLE", "角色");
    private String code;
    private String label;

    FlowTaskAssigneeTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(FlowTaskAssigneeTypeEnum.values()).map(FlowTaskAssigneeTypeEnum ->
                    new VLModel(FlowTaskAssigneeTypeEnum.getCode() + "", FlowTaskAssigneeTypeEnum.getLabel())).collect(Collectors.toList());
        }
    }
}
