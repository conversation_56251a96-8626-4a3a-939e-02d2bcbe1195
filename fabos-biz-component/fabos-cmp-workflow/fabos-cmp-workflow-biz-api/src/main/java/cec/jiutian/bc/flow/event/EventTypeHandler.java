package cec.jiutian.bc.flow.event;

import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.bc.flow.message.command.FabosWorkflowProcessEventType;
import cec.jiutian.common.exception.ServiceException;

public class EventTypeHandler {

    public static void handleEvent(FabosWorkflowEvent event, ExamineModel model) {
        switch ((FabosWorkflowProcessEventType) event.getType()) {
            case STOPPED:
            case COMPLETE:
                model.setExamineStatus(ExamineStatusEnum.AUDITED.getCode());
                break;
            case REFUSED:
                model.setExamineStatus(ExamineStatusEnum.REJECTED.getCode());
                break;
            case PROCESSING:
            case SUBMITTED:
                model.setExamineStatus(ExamineStatusEnum.AUDITING.getCode());
                break;
            case CANCELLED:
                model.setExamineStatus(ExamineStatusEnum.CANCEL.getCode());
                break;
            case DELETE:
                model.setExamineStatus(ExamineStatusEnum.DELETED.getCode());
                break;
            case WITHDRAW:
                model.setExamineStatus(ExamineStatusEnum.REVOKE.getCode());
                break;
            default:
                throw new ServiceException("未知事件类型:"+event.getType().name());
        }
    }
}
