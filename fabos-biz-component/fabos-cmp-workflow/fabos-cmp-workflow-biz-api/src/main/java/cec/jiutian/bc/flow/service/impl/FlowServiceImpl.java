package cec.jiutian.bc.flow.service.impl;

import cec.jiutian.bc.flow.domain.model.vo.ProcessInstanceInfoVO;
import cec.jiutian.bc.flow.dto.FlowTaskCompleteDTO;
import cec.jiutian.bc.flow.message.command.FlowStartOrCompleteDTO;
import cec.jiutian.bc.flow.provider.FlowInstanceRemoteService;
import cec.jiutian.bc.flow.provider.RemoteFlowProvider;
import cec.jiutian.bc.flow.service.FlowService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class FlowServiceImpl implements FlowService {


//    @DubboReference
    @Resource
    private RemoteFlowProvider remoteFlowProvider;

//    @DubboReference
    @Resource
    private FlowInstanceRemoteService flowInstanceRemoteService;

    @Override
    public String startOrCompleteTask(FlowStartOrCompleteDTO flowStartOrCompleteDTO) {
        return remoteFlowProvider.remoteStartOrCompleteTask(flowStartOrCompleteDTO);
    }

    @Override
    public ProcessInstanceInfoVO getProcessInstanceInfoById(String processInstanceId) {

        return flowInstanceRemoteService.getProcessInstanceInfoById(processInstanceId);
    }

    @Override
    public Boolean submitUserTask(FlowTaskCompleteDTO flowTaskCompleteDTO) {
        return  remoteFlowProvider.remoteSubmitUserTask(flowTaskCompleteDTO);
    }
}
