package cec.jiutian.bc.flow.domain.model.entity;

import cec.jiutian.bc.flow.domain.model.event.ExamineModelDataProxy;
import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.PreDataProxy;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据审批父类
 */
@Getter
@Setter
@MappedSuperclass
@PreDataProxy(ExamineModelDataProxy.class)
public class ExamineModel extends MetaModel {

    @Comment("审批状态")
    @Column(name = "examine_status")
    @FabosJsonField(
            views = @View(title = "审批状态",index = 99999999),
            edit = @Edit(title = "审批状态",
                    readonly = @Readonly(add = false, edit = false),
                    type = EditType.CHOICE,
                    show = false,
                    defaultVal = "1",
                    choiceType = @ChoiceType(fetchHandler = {ExamineStatusEnum.ChoiceFetch.class})
            )
    )
    private String examineStatus;

    @Comment("流程实例id")
    @Column(name = "process_instance_id")
    @FabosJsonField(
            views = @View(title = "流程实例ID", show = false),
            edit = @Edit(title = "流程实例ID", show = false,
                    readonly = @Readonly(add = false, edit = false))
    )
    private String processInstanceId;
}
