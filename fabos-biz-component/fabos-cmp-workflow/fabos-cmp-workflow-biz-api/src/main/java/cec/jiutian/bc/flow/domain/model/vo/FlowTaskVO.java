package cec.jiutian.bc.flow.domain.model.vo;

import cec.jiutian.view.config.Comment;
import jakarta.persistence.Column;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 流程任务Vo对象。
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@Comment("流程任务Vo对象")
@Data
public class FlowTaskVO implements Serializable {

    /**
     * 流程任务Id。
     */
    @Comment(value = "流程任务Id")
    private String taskId;

    /**
     * 流程任务名称。
     */
    @Comment(value = "流程任务名称")
    private String taskName;

    /**
     * 父流程ID。
     */
    @Comment(value = "父流程ID")
    private String parentTaskId;

    /**
     * 流程任务标识。
     */
    @Comment(value = "流程任务标识")
    private String taskDefinitionKey;

    /**
     * 任务的表单信息。
     */
    @Comment(value = "任务的表单信息")
    private String formKey;


    @Comment(value = "表单数据")
    private String formData;

    /**
     * 流程定义Id。
     */
    @Comment(value = "流程定义Id")
    private String processDefinitionId;

    /**
     * 流程定义名称。
     */
    @Comment(value = "流程定义名称")
    private String processDefinitionName;

    /**
     * 流程唯一编码。
     */
    @Column(name = "process_definition_key")
    private String processDefinitionKey;

    /**
     * 流程版本。
     */
    @Column(name = "process_definition_version")
    private Integer processDefinitionVersion;


    /**
     * 流程实例Id。
     */
    @Comment(value = "流程实例Id")
    private String processInstanceId;

    /**
     * 审核人
     */
    @Comment(value = "审核人")
    private String userAccount;

    /**
     * 审核人名称
     */
    @Comment(value = "审核人名称")
    private String userName;

    /**
     * 发起人用户ID
     */
    @Comment(value = "发起人用户ID")
    private String startUserId;

    /**
     * 发起人用户名称
     */
    @Comment(value = "发起人用户名称")
    private String startUserName;

    /**
     * 流程实例主表业务数据主键。
     */
    @Comment(value = "流程实例主表业务数据主键")
    private String businessKey;

    /**
     * 流程任务状态标识
     */
    @Comment(value = "流程任务标识")
    private Integer status;


    /**
     * 流程任务创建时间。
     */
    @Comment(value = "流程任务创建时间")
    private LocalDateTime startTime;

    /**
     * 流程任务创建时间。
     */
    @Comment(value = "流程任务创建时间")
    private LocalDateTime endTime;
}
