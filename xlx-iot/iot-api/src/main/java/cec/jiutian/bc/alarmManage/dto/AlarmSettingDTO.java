package cec.jiutian.bc.alarmManage.dto;

import lombok.Data;
import java.util.List;

@Data
public class AlarmSettingDTO {

    /**
     * 报警名字
     */
    private String alarmName;

    /**
     * 报警级别
     * 枚举值：general:一般
     *       serious:严重
     *       emergency:紧急
     *       disaster:灾害
     */
    private String alarmLevel;

    /**
     * 设备台账编码
     */
    private String equipmentArchiveCode;

    /**
     * 规则名字
     */
    private String ruleName;

    /**
     * 规则类型
     * 枚举值： "always":一直执行
     *        "timer":定时执行
     */
    private String ruleType;

    /**
     * 生效时间用逗号隔开 星期日对应1,星期一对应2...星期六对应7
     */
    private String effectiveTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 触发条件
     * 枚举值： all:全部
     *        or:任意一个
     */
    private String conditionType;

    /**
     * 触发条件
     */
    private List<AlarmConditionDTO> conditionList;
}
