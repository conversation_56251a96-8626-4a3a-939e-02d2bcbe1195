package cec.jiutian.bc.spotInspection.domain.insStandard.proxy;

import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

import java.util.Date;


@Component
public class SpotInspectionStandardDataProxy implements DataProxy<SpotInspectionStandard> {
    @Override
    public void beforeAdd(SpotInspectionStandard deviceInsStandard) {
        deviceInsStandard.setCreateDate(new Date());
        deviceInsStandard.setCreateName(UserContext.getUserName());
    }
}
