package cec.jiutian.bc.spotInspection.domain.insPlan.handler;

import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlanDetail;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandardItem;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/14 18:13
 * @description：
 */
@Component
public class SpotInspectionPlanDetailDynamicHandler implements DependFiled.DynamicHandler<SpotInspectionPlan> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public Map<String, Object> handle(SpotInspectionPlan deviceInsPlan) {
        Map<String, Object> result = new HashMap<>();
        List<SpotInspectionPlanDetail> deviceInsPlanDetailList = new ArrayList<>();
        if(deviceInsPlan.getDeviceInsStandard()!=null&&StringUtils.isNotBlank(deviceInsPlan.getDeviceInsStandard().getId())) {
            SpotInspectionStandard deviceInsStandard = fabosJsonDao.findById(SpotInspectionStandard.class, deviceInsPlan.getDeviceInsStandard().getId());
            if (deviceInsStandard != null && CollectionUtils.isNotEmpty(deviceInsStandard.getSpotInspectionStandardItemList())) {
                for (SpotInspectionStandardItem deviceInsStandardItem : deviceInsStandard.getSpotInspectionStandardItemList()) {
                    SpotInspectionPlanDetail deviceInsPlanDetail = new SpotInspectionPlanDetail();
                    deviceInsPlanDetail.setName(deviceInsStandardItem.getName());
                    deviceInsPlanDetail.setStandard(deviceInsStandardItem.getStandard());
                    deviceInsPlanDetail.setDescription(deviceInsStandardItem.getDescription());
                    deviceInsPlanDetail.setValueType(deviceInsStandardItem.getValueType());
                    deviceInsPlanDetail.setUpperValue(deviceInsStandardItem.getUpperValue());
                    deviceInsPlanDetail.setLowerValue(deviceInsStandardItem.getLowerValue());
                    deviceInsPlanDetail.setDefaultValue(deviceInsStandardItem.getDefaultValue());
                    deviceInsPlanDetail.setIs5s(deviceInsStandardItem.getIs5s());
                    deviceInsPlanDetailList.add(deviceInsPlanDetail);
                }
            }
        }
        result.put("spotInspectionPlanDetailList", deviceInsPlanDetailList);
        return result;
    }
}
