package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskResultEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionValueTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "点检设备清单",
        orderBy = "SpotInspectionTaskDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "isEditable != '1'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task_detail")
@FabosJsonI18n
public class SpotInspectionTaskDetail extends MetaModel {
    // 点检项目
    @FabosJsonField(
            views = @View(title = "点检项目"),
            edit = @Edit(title = "点检项目", notNull = true)
    )
    private String name;

    // 点检标准
    @FabosJsonField(
            views = @View(title = "点检标准"),
            edit = @Edit(title = "点检标准",notNull = true)
    )
    private String standard;

    // 描述
    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述")
    )
    private String description;

    // 值类型
    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionValueTypeEnum.class))
    )
    private String valueType;

    // 上限值
    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值")
    )
    private Double upperValue;

    // 下限值
    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值")
    )
    private Double lowerValue;

    // 标准值
    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值")
    )
    private Double defaultValue;

    // 是否5s
    @FabosJsonField(
            views = @View(title = "是否5S"),
            edit = @Edit(title = "是否5S")
    )
    private Boolean is5s;

    // 点检记录
    @FabosJsonField(
            views = @View(title = "点检记录"),
            edit = @Edit(title = "点检记录")
    )
    private String record;

    // 点检结果
    @FabosJsonField(
            views = @View(title = "点检结果"),
            edit = @Edit(title = "点检结果", type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(dependField = "is5s", fetchHandler = SpotInspectionTaskResultEnum.class))
    )
    private String result;

    // 其他说明
    @FabosJsonField(
            views = @View(title = "其他说明"),
            edit = @Edit(title = "其他说明", type = EditType.TEXTAREA)
    )
    private String otherDesc;

    // 关联主模型, 不展示
    @FabosJsonField(
            views = @View(title = "点检任务", show = false),
            edit = @Edit(title = "点检任务", show = false)
    )
    @ManyToOne
    @JsonIgnoreProperties("spotInspectionTaskDetails")
    private SpotInspectionTask spotInspectionTask;

    // 是否可编辑 不展示, 用于 我的点检任务 中执行时可编辑
    // 默认 null 不可编辑; 1 可编辑
    @FabosJsonField(
            views = @View(title = "是否可编辑", show = false),
            edit = @Edit(title = "是否可编辑", show = false)
    )
    private String isEditable;
}
