package cec.jiutian.bc.spotInspection.domain.insTask.proxy;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskDetail;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskSourceEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SpotInspectionTaskProxy implements DataProxy<SpotInspectionTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(SpotInspectionTask deviceInsTask) {
        this.checkParam(deviceInsTask);
        // 设置任务来源、点检状态
        deviceInsTask.setTaskSource(SpotInspectionTaskSourceEnum.Enum.SELF_BUILD.name());
        deviceInsTask.setCurrentState(SpotInspectionTaskStateEnum.Enum.PROCESSING.name());
        // 默认设置跳过点检状态为否
        deviceInsTask.setIsSkip("N");
        // 设置设备台账编码
        deviceInsTask.setEquipmentArchiveCode(deviceInsTask.getEquipmentArchive().getGeneralCode());
        // 设置设备所属检测室、所属实验室信息
        this.setEquipBelongInfo(deviceInsTask);
        // 设置任务子单可编辑状态为1
        for (SpotInspectionTaskDetail deviceInsTaskDetail : deviceInsTask.getSpotInspectionTaskDetails()) {
            deviceInsTaskDetail.setIsEditable("1");
        }
    }

    @Override
    public void beforeUpdate(SpotInspectionTask deviceInsTask) {
        this.checkParam(deviceInsTask);
    }

    /**
     * 设置点击任务设备所属实验室、检测室信息
     * <AUTHOR>
     * @date 2025/4/25 9:21
     * @param deviceInsTask
     */
    public void setEquipBelongInfo(SpotInspectionTask deviceInsTask) {
        String equipmentArchiveCode = deviceInsTask.getEquipmentArchiveCode();
        if (StringUtils.isBlank(equipmentArchiveCode)) {
            throw new FabosJsonApiErrorTip("设备台账编码信息缺失！");
        }
    }

    /**
     * 校验设备台账数据和设备点检标准是否匹配
     * <AUTHOR>
     * @date 2025/5/23 14:16
     * @param deviceInsTask
     */
    private void checkParam(SpotInspectionTask deviceInsTask) {
        EquipmentArchive equipDb = fabosJsonDao.getById(EquipmentArchive.class,
                deviceInsTask.getEquipmentArchive().getId());
        String code = equipDb.getEquipment().getCode();
        SpotInspectionStandard stdDb = fabosJsonDao.getById(SpotInspectionStandard.class,
                deviceInsTask.getDeviceInsStandard().getId());
        if (!code.equals(stdDb.getDeviceCode())) {
            throw new FabosJsonApiErrorTip("设备台账编码与点检标准不匹配，请重新选择点检标准！");
        }
    }
}
