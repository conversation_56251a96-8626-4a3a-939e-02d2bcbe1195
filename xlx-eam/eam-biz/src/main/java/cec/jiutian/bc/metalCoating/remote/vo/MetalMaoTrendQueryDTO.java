package cec.jiutian.bc.metalCoating.remote.vo;

import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description TODO
 */
@Data
public class MetalMaoTrendQueryDTO {

    @FabosJsonField(
            views = @View(title = "设备档案编码"),
            edit = @Edit(title = "设备档案编码", notNull = true, search = @Search(vague = true))
    )
    private String equipmentCode;

    @FabosJsonField(
            views = @View(title = "部件档案编码"),
            edit = @Edit(title = "部件档案编码", notNull = true, search = @Search(vague = true))
    )
    private String componentCode;

    @FabosJsonField(
            views = @View(title = "涂层材质"),
            edit = @Edit(title = "涂层材质", notNull = true, search = @Search(vague = true))
    )
    private String coatingMaterial;
}
