package cec.jiutian.bc.metalCoating.service;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.model.MetalCoatingInfo;
import cec.jiutian.bc.metalCoating.remote.vo.MetalMaoTrendQueryDTO;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description TODO
 */
@Service
@Slf4j
public class MetalMapTrendService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    public JSONObject getMetalMapTrend(MetalMaoTrendQueryDTO queryDTO) {
        MetalCoatingInfo condition = new MetalCoatingInfo();
        if (StringUtils.isNotEmpty(queryDTO.getEquipmentCode())) {
            condition.setEquipmentCode(queryDTO.getEquipmentCode());
        }
        if (StringUtils.isNotEmpty(queryDTO.getComponentCode())) {
            condition.setComponentCode(queryDTO.getComponentCode());
        }
        if (StringUtils.isNotEmpty(queryDTO.getCoatingMaterial())) {
            condition.setCoatingMaterial(queryDTO.getCoatingMaterial());
        }

        List<MetalCoatingInfo> metalCoatingInfos = fabosJsonDao.select(condition);
        Map<Object, List<Map<String,Object>>> map;
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(metalCoatingInfos)) {
            List<Map<String, Object>> list = new ArrayList<>();
            metalCoatingInfos.forEach(metalCoatingInfo -> {
                if (CollectionUtils.isNotEmpty(metalCoatingInfo.getDetailList())) {
                    metalCoatingInfo.getDetailList().forEach(detail -> {
                        Map<String, Object> detailMap = new HashMap<>();
                        detailMap.put("inspectDate", DateUtil.format(detail.getInspectDate(),"yyyy-MM-dd HH"));
                        detailMap.put("inspectTarget",detail.getInspectTarget());
                        detailMap.put("inspectResult",detail.getInspectResult());
                        list.add(detailMap);
                    });
                }
            });
            if (CollectionUtils.isNotEmpty(list)) {
                map = list.stream().collect(Collectors.groupingBy(m -> m.get("inspectTarget")));
                List<Map<String,Object>> dataList = map.values().stream()
                        .filter(Objects::nonNull)
                        .max(Comparator.comparingInt(List::size)).orElse(null);
                List<Object> dateList = dataList.stream().map(m -> m.get("inspectDate")).toList();
                result.put("data",map);
                result.put("date",dateList);
                return new JSONObject(result);
            }

        }
        return new JSONObject();
    }

    public List<Map<String,Object>> getEquipmentArchive() {
        List<EquipmentArchive> list = fabosJsonDao.queryEntityList(EquipmentArchive.class);
        List<Map<String,Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                Map<String,Object> map = new HashMap<>();
                map.put("id",e.getId());
                map.put("code",e.getGeneralCode());
                map.put("name",e.getName());
                result.add(map);
            });
        }
        return result;
    }

    public  List<Map<String,Object>> getEquipmentComponentDetail(MetalMaoTrendQueryDTO queryDTO) {
        String hql = "from EquipmentComponentDetail ed where ed.equipmentArchive.generalCode = :equipmentArchiveCode";
        TypedQuery<EquipmentComponentDetail> query = fabosJsonDao.getEntityManager().createQuery(hql, EquipmentComponentDetail.class);
        query.setParameter("equipmentArchiveCode",queryDTO.getEquipmentCode());
        List<EquipmentComponentDetail> list = query.getResultList();

        List<Map<String,Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                Map<String,Object> map = new HashMap<>();
                map.put("id",e.getId());
                map.put("code",e.getGeneralCode());
                map.put("name",e.getName());
                result.add(map);
            });
        }
        return result;
    }

    public List<String> getCoatingMaterial(MetalMaoTrendQueryDTO queryDTO) {
        MetalCoatingInfo condition = new MetalCoatingInfo();
        if (StringUtils.isNotEmpty(queryDTO.getEquipmentCode())) {
            condition.setEquipmentCode(queryDTO.getEquipmentCode());
        }
        if (StringUtils.isNotEmpty(queryDTO.getComponentCode())) {
            condition.setComponentCode(queryDTO.getComponentCode());
        }
        if (StringUtils.isNotEmpty(queryDTO.getCoatingMaterial())) {
            condition.setCoatingMaterial(queryDTO.getCoatingMaterial());
        }

        List<MetalCoatingInfo> list = fabosJsonDao.select(condition);
        List<String> coatingMaterialList = list.stream().map(MetalCoatingInfo::getCoatingMaterial).toList();

        return coatingMaterialList;
    }
}
