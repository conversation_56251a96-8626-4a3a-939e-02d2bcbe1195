package cec.jiutian.bc.spotInspection.domain.insStandard.model;

import cec.jiutian.bc.spotInspection.domain.insStandard.handler.EamComparisonMethodDynamicHandler;
import cec.jiutian.bc.spotInspection.enumeration.ComparisonMethodEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionValueTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "qms_spot_inspection_standard_item")
@Getter
@Setter
@FabosJson(name = "设备点检标准明细",
        orderBy = "DeviceInsStandardItem.createTime desc",power = @Power(export = false,importable = false)
)
@FabosJsonI18n
public class SpotInspectionStandardItem extends BaseModel {
    @FabosJsonField(
            views = @View(title = "点检标准", show = false),
            edit = @Edit(title = "点检标准", show = false)
    )
    @ManyToOne
    @JsonIgnoreProperties("spotInspectionStandardItemList")
    private SpotInspectionStandard spotInspectionStandard;
    //检查项名称
    @FabosJsonField(
            views = @View(title = "检查项名称"),
            edit = @Edit(title = "检查项名称",notNull = true)
    )
    private String name;
    //检查标准
    @FabosJsonField(
            views = @View(title = "检查标准"),
            edit = @Edit(title = "检查标准",notNull = true)
    )
    private String standard;
    //描述
    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述")
    )
    private String description;
    //值类型
    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionValueTypeEnum.class))
    )
    private String valueType;
    @FabosJsonField(
            views = @View(title = "比较方式"),
            edit = @Edit(title = "比较方式",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "valueType == 'NUMBER' || valueType == 'PERCENTAGE'"),
                    search = @Search(),
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ComparisonMethodEnum.class)),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "valueType",dynamicHandler = EamComparisonMethodDynamicHandler.class))
    )
    private String comparisonMethod;
    //上限值
    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",notNull = true,dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range'"),numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double upperValue;
    //下限值
    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",notNull = true,dependFieldDisplay = @DependFieldDisplay(showOrHide = "comparisonMethod == 'range' || comparisonMethod == 'lowerLimit'"),numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;
    //默认值
    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",notNull = true,numberType = @NumberType(min=0,max = 9999999,precision = 2)
            ,dependFieldDisplay = @DependFieldDisplay(showOrHide = "valueType == 'TEXT' || comparisonMethod == 'equal'"))

    )
    private Double defaultValue;
    //是否5s
    @FabosJsonField(
            views = @View(title = "是否5S"),
            edit = @Edit(title = "是否5S", notNull = true)
    )
    private Boolean is5s;
}
