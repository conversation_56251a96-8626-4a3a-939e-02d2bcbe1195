package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionAllocate;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class SpotInspectionTaskAllocationHandler implements OperationHandler<SpotInspectionTask, SpotInspectionAllocate> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<SpotInspectionTask> data, SpotInspectionAllocate modelObject, String[] param) {
        SpotInspectionTask deviceInsTaskDb = fabosJsonDao.getById(SpotInspectionTask.class, modelObject.getId());
        deviceInsTaskDb.setCheckPerson(modelObject.getCheckPerson());
        // 分配后状态改为 点检中
        deviceInsTaskDb.setCurrentState(SpotInspectionTaskStateEnum.Enum.PROCESSING.name());
        fabosJsonDao.update(deviceInsTaskDb);
        return null;
    }

    @Override
    public SpotInspectionAllocate fabosJsonFormValue(List<SpotInspectionTask> data, SpotInspectionAllocate fabosJsonForm, String[] param) {
        SpotInspectionTask deviceInsTask = data.get(0);
        BeanUtils.copyProperties(deviceInsTask, fabosJsonForm);
        return fabosJsonForm;
    }
}
