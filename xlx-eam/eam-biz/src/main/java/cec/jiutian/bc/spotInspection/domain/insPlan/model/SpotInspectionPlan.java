package cec.jiutian.bc.spotInspection.domain.insPlan.model;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.spotInspection.domain.insPlan.handler.SpotInspectionPlanDetailDynamicHandler;
import cec.jiutian.bc.spotInspection.domain.insPlan.handler.SpotInspectionPlanOperationHandler;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionQcPlanCycleEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionSkipTimeEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "eam_spot_inspection_plan")
@Getter
@Setter
@FabosJson(name = "设备点检计划",
        orderBy = "SpotInspectionPlan.createTime desc",power = @Power(export = false,importable = false),
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "effective == 'Y'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "effective == 'Y'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        },
        rowOperation = {
                @RowOperation(
                        title = "启用",
                        code = "SpotInspectionPlan@ENABLE",
                        operationParam={"Y"},
                        operationHandler = SpotInspectionPlanOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "请确认是否执行启用操作？",
                        ifExpr = "effective == 'Y'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SpotInspectionPlan@ENABLE"
                        )
                ),
                @RowOperation(
                        title = "停用",
                        code = "SpotInspectionPlan@DEAC",
                        operationParam={"N"},
                        operationHandler = SpotInspectionPlanOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "请确认是否执行停用操作",
                        ifExpr = "effective == 'N'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SpotInspectionPlan@DEAC"
                        )
                ),
        }
)
@FabosJsonI18n
public class SpotInspectionPlan extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.SpotInspectionPlan.name();
    }
    //计划名称
    @FabosJsonField(
            views = @View(title = "计划名称"),
            edit = @Edit(title = "计划名称",notNull = true)
    )
    private String name;
    //计划描述
    @FabosJsonField(
            views = @View(title = "计划描述"),
            edit = @Edit(title = "计划描述")
    )
    private String description;
    @Transient
    @FabosJsonField(
            views = @View(title = "点检设备编号", column = "generalCode"),
            edit = @Edit(title = "点检设备编号",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    private EquipmentArchive equipmentArchive;
    // 设备id
    @FabosJsonField(
            views = @View(title = "设备id", show = false),
            edit = @Edit(title = "设备id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "id"))
    )
    private String deviceId;

    // 设备编码
    @FabosJsonField(
            views = @View(title = "设备台账编号"),
            edit = @Edit(title = "设备台账编号", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "generalCode"))

    )
    private String deviceCode;

    // 设备名称
    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "name"))

    )
    private String deviceName;

    //开始时间
    @FabosJsonField(
            views = @View(title = "计划开始日期"),
            edit = @Edit(title = "计划开始日期", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Date startTime;
    //结束时间
    @FabosJsonField(
            views = @View(title = "计划结束日期"),
            edit = @Edit(title = "计划结束日期", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Date endTime;
    @FabosJsonField(
            views = @View(title = "跳过时间"),
            edit = @Edit(title = "跳过时间",type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionSkipTimeEnum.class))
    )
    private String skipTime;
    @FabosJsonField(
            views = @View(title = "周期",show = false),
            edit = @Edit(title = "周期",notNull = true,inputGroup = @InputGroup(postfix = "#{cycle}")
    )
    )
    private Integer cycleNum;
    @FabosJsonField(
            views = @View(title = "周期"),
            edit = @Edit(title = "周期",notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionQcPlanCycleEnum.class),defaultVal = "DAY")
    )
    private String cycle;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检标准", column = "name"),
            edit = @Edit(title = "点检标准",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private SpotInspectionStandard deviceInsStandard;
    @FabosJsonField(
            views = @View(title = "是否启用"),
            edit = @Edit(title = "是否启用", type = EditType.CHOICE,show = false,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class))
    )
    private String effective;
    //上次点检时间
    @FabosJsonField(
            views = @View(title = "上次点检日期"),
            edit = @Edit(title = "上次点检日期", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date lastCheckTime;
    //下次点检时间
    @FabosJsonField(
            views = @View(title = "下次点检日期"),
            edit = @Edit(title = "下次点检日期", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE),
                    inputType = @InputType(length = 40))
    )
    private Date nextCheckTime;

    @OneToMany(cascade = CascadeType.ALL)
    @FabosJsonField(
            views = @View(title = "明细", type = ViewType.TABLE_VIEW, index = 8),
            edit = @Edit(title = "明细", type = EditType.TAB_REFERENCE_GENERATE),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = {"deviceInsStandard"}, dynamicHandler = SpotInspectionPlanDetailDynamicHandler.class)),
            referenceGenerateType = @ReferenceGenerateType()
    )
    @JoinColumn(name = "spot_inspection_plan_id")
    private List<SpotInspectionPlanDetail> spotInspectionPlanDetailList;

}
