package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.domain.insTask.handler.*;
import cec.jiutian.bc.spotInspection.domain.insTask.proxy.SpotInspectionTaskProxy;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskSourceEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(name = "点检任务",
        orderBy = "SpotInspectionTask.createTime desc",
        power = @Power(export = false, print = false, edit = false, delete = false, viewDetails = false),
        dataProxy = SpotInspectionTaskProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "变更",
                        code = "SpotInspectionTask@CHANGE",
                        operationHandler = SpotInspectionTaskChangeHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SpotInspectionTaskChange.class,
                        show = @ExprBool(
                                params = "SpotInspectionTask@CHANGE",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].currentState != 'PROCESSING'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                // 人员为空时, 可以指派
                @RowOperation(
                        title = "指派",
                        code = "SpotInspectionTask@ALLOCATION",
                        operationHandler = SpotInspectionTaskAllocationHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SpotInspectionAllocate.class,
                        show = @ExprBool(
                                value = true,
                                params = "SpotInspectionTask@ALLOCATION",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].checkPerson.id != null || selectedItems[0].currentState == 'CLOSED' || selectedItems[0].currentState == 'COMPLETE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "跳过点检",
                        code = "SpotInspectionTask@SKIP",
                        operationHandler = SpotInspectionTaskSkipHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SpotInspectionTaskSkip.class,
                        show = @ExprBool(
                                value = true,
                                params = "SpotInspectionTask@SKIP",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].currentState == 'CLOSED' || selectedItems[0].currentState == 'COMPLETE'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
@FabosJsonI18n
@TemplateType(type = "multiTable")
public class SpotInspectionTask extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.SpotInspectionTask.name();
    }

    // 任务名称
    @FabosJsonField(
            views = @View(title = "任务名称", toolTip = true),
            edit = @Edit(title = "任务名称", notNull = true)
    )
    private String name;

    // 任务描述
    @FabosJsonField(
            views = @View(title = "任务描述", toolTip = true),
            edit = @Edit(title = "任务描述", notNull = true)
    )
    private String taskDesc;

    @Transient
    @FabosJsonField(
            views = @View(title = "设备台账编码", column = "generalCode"),
            edit = @Edit(title = "设备台账编码", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    private EquipmentArchive equipmentArchive;

    // 设备id
    @FabosJsonField(
            views = @View(title = "设备id", show = false),
            edit = @Edit(title = "设备id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "id"))
    )
    private String deviceId;

    // 用于统计, 不展示
    @FabosJsonField(
            views = @View(title = "设备台账编码", show = false),
            edit = @Edit(title = "设备台账编码", show = false)
    )
    private String equipmentArchiveCode;

    // 设备名称
    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称", readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "equipmentArchive",
                    dynamicHandler = EquipmentArchiveInfoHandler.class))
    )
    private String deviceName;

    @FabosJsonField(
            views = @View(title = "设备编码", show = false),
            edit = @Edit(title = "设备编码", show = false, defaultVal = ""),
            dynamicField = @DynamicField(passive = true)
    )
    private String code;

    // 标准名称
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检标准", column = "name"),
            edit = @Edit(title = "点检标准",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"deviceCode\":\"${code}\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private SpotInspectionStandard deviceInsStandard;

    // 计划点检时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "点检时间"),
            edit = @Edit(title = "点检时间", notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date startTime;

    // 点检人
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检人", column = "name"),
            edit = @Edit(title = "点检人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO checkPerson;

    // 点检状态
    @FabosJsonField(
            views = @View(title = "点检状态"),
            edit = @Edit(title = "点检状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionTaskStateEnum.class))
    )
    private String currentState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "任务完成时间"),
            edit = @Edit(title = "任务完成时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date finishTime;

    // 异常数量, 用于统计
    @FabosJsonField(
            views = @View(title = "异常数量"),
            edit = @Edit(title = "异常数量", show = false, type = EditType.NUMBER, readonly = @Readonly)
    )
    private Integer exceptionNum;

    // 任务来源
    @FabosJsonField(
            views = @View(title = "任务来源"),
            edit = @Edit(title = "任务来源", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionTaskSourceEnum.class))
    )
    private String taskSource;

    // 是否跳过点检
    @FabosJsonField(
            views = @View(title = "是否跳过点检"),
            edit = @Edit(title = "是否跳过点检", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
    )
    )
    private String isSkip;

    // 跳过原因
    @FabosJsonField(
            views = @View(title = "跳过原因", toolTip = true),
            edit = @Edit(title = "跳过原因", show = false)
    )
    private String skipReason;

    // 关联子模型
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_task_id")
    @FabosJsonField(
            views = @View(title = "点检设备清单", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "点检设备清单", type = EditType.TAB_REFERENCE_GENERATE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "deviceInsStandard != null")),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "deviceInsStandard",
                    dynamicHandler = SpotInspectionTaskDetailsDynamicHandler.class))
    )
    private List<SpotInspectionTaskDetail> spotInspectionTaskDetails;

    // 变更记录
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "变更记录", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "变更记录", type = EditType.TAB_TABLE_ADD, show = false)
    )
    private List<SpotInspectionChangeRecord> spotInspectionChangeRecords;
    //计划主键  通过计划创建的才有数据  不显示
    @FabosJsonField(
            views = @View(title = "计划主键", show = false),
            edit = @Edit(title = "计划主键", show = false)
    )
    private String deviceInsPlanId;

    // 设备所属检测室, 用于统计, 不展示
    @FabosJsonField(
            views = @View(title = "设备所属检测室id", show = false),
            edit = @Edit(title = "设备所属检测室id", show = false)
    )
    private String equipBelongLabId;

    // 设备所属检测室名称, 用于统计, 不展示
    @FabosJsonField(
            views = @View(title = "设备所属检测室名称", show = false),
            edit = @Edit(title = "设备所属检测室名称", show = false)
    )
    private String equipBelongLabName;

    // 设备所属实验室, 用于统计, 不展示
    @FabosJsonField(
            views = @View(title = "设备所属实验室id", show = false),
            edit = @Edit(title = "设备所属实验室id", show = false)
    )
    private String equipBelongExpLabId;

    // 设备所属实验室名称, 用于统计, 不展示
    @FabosJsonField(
            views = @View(title = "设备所属实验室名称", show = false),
            edit = @Edit(title = "设备所属实验室名称", show = false)
    )
    private String equipBelongExpLabName;

}
