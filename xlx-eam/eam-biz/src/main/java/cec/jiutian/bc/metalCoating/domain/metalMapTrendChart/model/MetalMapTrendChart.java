package cec.jiutian.bc.metalCoating.domain.metalMapTrendChart.model;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/6/9
 * @description TODO
 */
@FabosJson(
        name = "金属地图涂层趋势图",
        orderBy = "MetalMapTrendChart.createTime desc",
        power = @Power(add = false,edit = false,delete = false,viewDetails = false),
        rowOperation = {
                @RowOperation(
                        title = "查看",
                        code = "MetalMapTrendChart@VIEW",
                        mode = RowOperation.Mode.SINGLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MetalMapTrendChart@VIEW"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        )
                ),
        }
)
@Table(name = "eam_mc_metal_coating_info",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MetalMapTrendChart extends MetaModel {

    @ManyToOne
    @JoinColumn(name = "equipment_archive_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "设备", column = "name"),
            edit = @Edit(title = "设备",readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true)
            )
    )
    private EquipmentArchive equipmentArchive;

    @ManyToOne
    @JoinColumn(name = "equipment_component_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "管控部件", column = "name"),
            edit = @Edit(title = "管控部件",readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"equipmentArchive.id\":\"${equipmentArchive.id}\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true)
            )
    )
    private EquipmentComponentDetail equipmentComponent;

    @FabosJsonField(
            views = @View(title = "使用地点"),
            edit = @Edit(title = "使用地点", readonly = @Readonly)
    )
    private String equipmentLocation;

    @FabosJsonField(
            views = @View(title = "涂层材质"),
            edit = @Edit(title = "涂层材质",readonly = @Readonly)
    )
    private String coatingMaterial;
}
