package cec.jiutian.bc.spotInspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 点检任务-点检状态枚举
 * <AUTHOR>
 * @date 2025/4/2 9:52
 */
public class SpotInspectionTaskStateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        WAIT_PROCESS("待派工"),
        PROCESSING("点检中"),
        COMPLETE("已完成"),
        CLOSED("已关闭");

        private final String value;

    }
}
