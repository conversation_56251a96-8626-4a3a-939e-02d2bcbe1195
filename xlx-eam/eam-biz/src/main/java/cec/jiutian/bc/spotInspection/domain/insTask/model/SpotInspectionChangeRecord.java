package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 点检任务变更记录
 * <AUTHOR>
 * @date 2025/4/2 15:12
 */
@FabosJson(name = "变更记录",
        orderBy = "ChangeRecord.changeTime desc"
)
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_change_record")
@FabosJsonI18n
@TemplateType(type = "usual")
public class SpotInspectionChangeRecord extends MetaModel {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "变更时间"),
            edit = @Edit(title = "变更时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date changeTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "变更点检时间"),
            edit = @Edit(title = "变更点检时间",
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date nowStartTime;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "变更点检人", column = "name"),
            edit = @Edit(title = "变更点检人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO nowCheckPerson;

    @FabosJsonField(
            views = @View(title = "变更原因", toolTip = true),
            edit = @Edit(title = "变更原因")
    )
    private String changeReason;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "原点检时间"),
            edit = @Edit(title = "原点检时间",
                    notNull = true,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date originStartTime;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "原点检人", column = "name"),
            edit = @Edit(title = "原点检人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO originCheckPerson;
}
