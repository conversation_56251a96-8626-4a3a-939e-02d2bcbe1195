package cec.jiutian.bc.spotInspection.domain.insTask.proxy;

import cec.jiutian.bc.spotInspection.domain.insTask.model.MySpotInspectionTask;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MySpotInspectionTaskProxy implements DataProxy<MySpotInspectionTask> {
    @Value("${system.manage.super-account}")
    private String superUserAccount;

    @Override
    public String beforeFetch(List<Condition> conditions) {
        if (UserContext.getAccount().equals(superUserAccount)) {
            return "";
        }
        String queryHql = "MySpotInspectionTask.checkPerson.user.id = \'" + UserContext.getUserId() + "\'";
        return queryHql;
    }
}
