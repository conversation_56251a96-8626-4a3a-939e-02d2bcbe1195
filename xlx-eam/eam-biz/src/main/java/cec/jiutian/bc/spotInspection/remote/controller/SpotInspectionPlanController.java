package cec.jiutian.bc.spotInspection.remote.controller;

import cec.jiutian.bc.spotInspection.domain.insPlan.service.SpotInspectionPlanService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:
 */
@RequestMapping("/spotInspection/plan")
@RestController
public class SpotInspectionPlanController {
    @Resource
    private SpotInspectionPlanService spotInspectionPlanService;


    @GetMapping("/generateSpotInspectionPlan")
    public void generateStocktakingOrder(){
        spotInspectionPlanService.generateDeviceInsTask();
    }
}
