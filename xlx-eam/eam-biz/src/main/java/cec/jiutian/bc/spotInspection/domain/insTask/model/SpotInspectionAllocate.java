package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "指派")
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
public class SpotInspectionAllocate extends MetaModel {
    @FabosJsonField(
            views = @View(title = "点检任务单号"),
            edit = @Edit(title = "点检任务单号", readonly = @Readonly)
    )
    private String generalCode;

    // 点检人
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检人", column = "name"),
            edit = @Edit(title = "点检人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO checkPerson;
}
