package cec.jiutian.bc.spotInspection.domain.insTask.service;


import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskDetail;
import cec.jiutian.bc.spotInspection.domain.insTask.proxy.SpotInspectionTaskProxy;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskSourceEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.bc.utils.StocktakingDateUtil;
import cec.jiutian.common.util.DateUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class SpotInspectionTaskService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private NamingRuleService namingRuleService;
    @Resource
    private SpotInspectionTaskProxy spotInspectionTaskProxy;

    /**
     * 通过点检计划  生成设备点检任务
     * @param deviceInsPlan
     */
    public void generateDeviceInsTask(SpotInspectionPlan deviceInsPlan){
        List<SpotInspectionTask> taskByPlan = findTaskByPlan(deviceInsPlan);
        if(CollectionUtils.isNotEmpty(taskByPlan)){
            log.info("该巡检计划{}已生成过任务",deviceInsPlan.getGeneralCode());
            return;
        }
        SpotInspectionTask deviceInsTask = new SpotInspectionTask();
        deviceInsTask.setGeneralCode(namingRuleService.getNameCode(NamingRuleCodeEnum.SpotInspectionTask.name(), 1, null).get(0));
        deviceInsTask.setName(deviceInsPlan.getName());
        deviceInsTask.setTaskDesc(deviceInsPlan.getDescription());
        // 设置设备相关信息
        deviceInsTask.setEquipmentArchive(deviceInsPlan.getEquipmentArchive());
        deviceInsTask.setEquipmentArchiveCode(deviceInsPlan.getEquipmentArchive().getGeneralCode());
        // 设置设备所属检测室、实验室信息
        spotInspectionTaskProxy.setEquipBelongInfo(deviceInsTask);
        deviceInsTask.setDeviceName(deviceInsPlan.getDeviceName());
        deviceInsTask.setDeviceInsStandard(deviceInsPlan.getDeviceInsStandard());
        deviceInsTask.setCurrentState(SpotInspectionTaskStateEnum.Enum.WAIT_PROCESS.name());
        deviceInsTask.setTaskSource(SpotInspectionTaskSourceEnum.Enum.DEVICEP_INS_PLAN.name());
        deviceInsTask.setDeviceInsPlanId(deviceInsPlan.getId());
        // 默认设置跳过点检为否
        deviceInsTask.setIsSkip("N");
        //点检时间   获取当前日期（年月日）和计划的开始日期的时分秒 组合成点检日期
        deviceInsTask.setStartTime(StocktakingDateUtil.combineCurrentDateWithTime(deviceInsPlan.getStartTime()));
        deviceInsTask.setSpotInspectionTaskDetails(deviceInsPlan.getSpotInspectionPlanDetailList().stream().map(deviceInsPlanDetail -> {
            SpotInspectionTaskDetail detail = new SpotInspectionTaskDetail();
            detail.setName(deviceInsPlanDetail.getName());
            detail.setStandard(deviceInsPlanDetail.getStandard());
            detail.setDescription(deviceInsPlanDetail.getDescription());
            detail.setValueType(deviceInsPlanDetail.getValueType());
            detail.setUpperValue(deviceInsPlanDetail.getUpperValue());
            detail.setLowerValue(deviceInsPlanDetail.getLowerValue());
            detail.setDefaultValue(deviceInsPlanDetail.getDefaultValue());
            detail.setIs5s(deviceInsPlanDetail.getIs5s());
            detail.setIsEditable("1");
            return detail;
        }).toList());
        fabosJsonDao.persistAndFlush(deviceInsTask);
    }

    private List<SpotInspectionTask> findTaskByPlan(SpotInspectionPlan plan) {
        Date now = new Date();
        Date[] dateRange = DateUtils.getStartAndEndOfDay(now);
        // 创建 HQL 查询
        String hql = "SELECT l FROM SpotInspectionTask l " +
                "WHERE l.deviceInsPlanId = :deviceInsPlanId " +
                "AND l.startTime BETWEEN :startOfDay AND :endOfDay";
        // 执行查询
        TypedQuery<SpotInspectionTask> query = fabosJsonDao.getEntityManager().createQuery(hql, SpotInspectionTask.class);
        query.setParameter("deviceInsPlanId", plan.getId());
        query.setParameter("startOfDay", dateRange[0]);
        query.setParameter("endOfDay", dateRange[1]);
        return query.getResultList();
    }


}
