package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/21
 * @description TODO
 */
@Entity
@Table(name = "mf_prcs_oprtn_st")
@Getter
@Setter
@FabosJson(
        name = "工步信息",
        orderBy = "createTime desc",
        power = @Power(add = false, edit = false, delete = false, print = false, importable = false)
)
public class EAMProcessStepMTO {

    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id")
    )
    @Id
    @Column(name = "id", columnDefinition = "int8")
    private Long id;

    @FabosJsonField(
            edit = @Edit(title = "processId", show = false)
    )
    @Column(name = "process_id", columnDefinition = "int8")
    private Long processId;

    @FabosJsonField(
            views = @View(title = "工序编码"),
            edit = @Edit(title = "工序编码",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "process_code", length = 40)
    private String code;

    @FabosJsonField(
            views = @View(title = "工步名称"),
            edit = @Edit(title = "工步名称",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "step_name", length = 40)
    private String stepName;

    @FabosJsonField(
            views = @View(title = "工步序号"),
            edit = @Edit(title = "工步序号",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "step_index", columnDefinition = "int8")
    private Integer stepIndex;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    notNull = true,
                    search = @Search(vague = true)
            )
    )
    @Column(name = "remark", length = 40)
    private String remark;

    @Column(name = "crte_tm")
    private Date createTime;
}
