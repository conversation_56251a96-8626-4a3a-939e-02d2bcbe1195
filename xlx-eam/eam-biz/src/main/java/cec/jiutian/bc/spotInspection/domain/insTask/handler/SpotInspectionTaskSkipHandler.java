package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskSkip;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class SpotInspectionTaskSkipHandler implements OperationHandler<SpotInspectionTask, SpotInspectionTaskSkip> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<SpotInspectionTask> data, SpotInspectionTaskSkip modelObject, String[] param) {
        SpotInspectionTask deviceInsTaskDb = fabosJsonDao.getById(SpotInspectionTask.class, modelObject.getId());
        deviceInsTaskDb.setIsSkip("Y");
        deviceInsTaskDb.setSkipReason(modelObject.getSkipReason());
        // 设置单据为已关闭
        deviceInsTaskDb.setCurrentState(SpotInspectionTaskStateEnum.Enum.CLOSED.name());
        fabosJsonDao.update(deviceInsTaskDb);
        return null;
    }

    @Override
    public SpotInspectionTaskSkip fabosJsonFormValue(List<SpotInspectionTask> data, SpotInspectionTaskSkip fabosJsonForm, String[] param) {
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);
        return fabosJsonForm;
    }
}
