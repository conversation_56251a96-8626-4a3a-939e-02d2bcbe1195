package cec.jiutian.bc.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class WareUseTypeEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.getValue(), data.getLabel()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        //01备料
        BLE("备料", "10"),
        //02成品
        PRODUCT("成品", "20"),
        //03供方不良
        SUPPLIER_BAD("供方不良", "30"),
        //04供方良品
        SUPPLIER_GOOD("供方良品", "40"),
        //05不良品
        BAD("不良品", "50"),
        //06虚拟库区
        VIRTUAL("虚拟库区", "60"),
        //07正常
        NORMAL("正常", "70"),
        //08载具
        CARRIER("载具", "80"),
        //09赠品样品
        SAMPLE("赠品样品", "90"),

        ;
        private final String label;
        private final String value;

    }

}
