package cec.jiutian.bc.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class PrcsFlagEnum implements ChoiceFetchHandler {

    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum data : Enum.values()) {
            list.add(new VLModel(data.getCode(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
//        0 - 未处理，1 - 入库中，2 - 出库中
        NotProcessed(0L,"未处理"),
        InStock(1L,"入库中"),
        OutStock(2L,"出库中"),

        ;
        private Long code;
        private final String value;
    }

}
