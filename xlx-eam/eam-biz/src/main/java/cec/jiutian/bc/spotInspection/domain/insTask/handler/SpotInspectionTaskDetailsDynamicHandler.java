package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandardItem;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskDetail;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class SpotInspectionTaskDetailsDynamicHandler implements DependFiled.DynamicHandler<SpotInspectionTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(SpotInspectionTask deviceInsTask) {
        HashMap<String, Object> map = new HashMap<>();
        // 设备点检标准 或者 检测项数据为空 则返回一个空的map
        SpotInspectionStandard deviceInsStandard = deviceInsTask.getDeviceInsStandard();
        if (deviceInsStandard == null) {
            map.put("spotInspectionTaskDetails", null);
            return map;
        }
        List<SpotInspectionTaskDetail> resultList = new ArrayList<>();
        SpotInspectionStandard deviceInsStandardDb = fabosJsonDao.getById(SpotInspectionStandard.class, deviceInsStandard.getId());
        List<SpotInspectionStandardItem> standardItemList = deviceInsStandardDb.getSpotInspectionStandardItemList();
        if (standardItemList == null) {  // 防御性判空
            standardItemList = Collections.emptyList();
        }
        for (SpotInspectionStandardItem standardItem : standardItemList) {
            SpotInspectionTaskDetail deviceInsTaskDetail = new SpotInspectionTaskDetail();
            BeanUtils.copyProperties(standardItem, deviceInsTaskDetail);
            deviceInsTaskDetail.setId(null);
            resultList.add(deviceInsTaskDetail);
        }
        map.put("spotInspectionTaskDetails", resultList);
        return map;
    }

}
