package cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.model;

import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentComponentDetail;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.handler.MetalCoatingInfoConfirmOperationHandler;
import cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.proxy.MetalCoatingInfoProxy;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description TODO
 */
@FabosJson(
        name = "金属地图涂层管理",
        orderBy = "MetalCoatingInfo.createTime desc",
        dataProxy = MetalCoatingInfoProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
                @RowBaseOperation(
                        code = "delete",
                        ifExpr = "businessState !='EDIT'",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                ),
        },
        rowOperation = {
                @RowOperation(
                        title = "发布",
                        code = "MetalCoatingInfo@PUBLISH",
                        operationHandler = MetalCoatingInfoConfirmOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MetalCoatingInfo@PUBLISH"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "businessState != 'EDIT'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
        }
)
@Table(name = "eam_mc_metal_coating_info",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"generalCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class MetalCoatingInfo extends NamingRuleBaseModel {

    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.MetalCoatingInfo.name();
    }

    @ManyToOne
    @JoinColumn(name = "equipment_archive_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "设备", column = "name"),
            edit = @Edit(title = "设备",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true)
            )
    )
    private EquipmentArchive equipmentArchive;

    @FabosJsonField(
            views = @View(title = "设备档案编码",show = false),
            edit = @Edit(title = "设备档案编码",show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive",beFilledBy = "generalCode"))
    )
    private String equipmentCode;

    @FabosJsonField(
            views = @View(title = "工序ID",show = false),
            edit = @Edit(title = "工序ID",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "processId"))
    )
    private String processId;

    @FabosJsonField(
            views = @View(title = "工序名称"),
            edit = @Edit(title = "工序名称",
                    readonly = @Readonly(add = true,edit = true),
                    search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "processName"))
    )
    private String processName;

    @FabosJsonField(
            views = @View(title = "产线Id",show = false),
            edit = @Edit(title = "产线Id",
                    show = false
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "factoryLineId"))
    )
    private String factoryLineId;

    @FabosJsonField(
            views = @View(title = "产线名称"),
            edit = @Edit(title = "产线名称",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true,edit = true)
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "factoryAreaName"))
    )
    private String factoryAreaName;

    @ManyToOne
    @JoinColumn(name = "equipment_component_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "管控部件", column = "name"),
            edit = @Edit(title = "管控部件",
                    type = EditType.REFERENCE_TABLE,
                    queryCondition = "{\"equipmentArchive.id\":\"${equipmentArchive.id}\"}",
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search(vague = true)
            )
    )
    private EquipmentComponentDetail equipmentComponent;

    @FabosJsonField(
            views = @View(title = "部件档案编码",show = false),
            edit = @Edit(title = "部件档案编码",show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentComponent",beFilledBy = "generalCode"))
    )
    private String componentCode;

    @FabosJsonField(
            views = @View(title = "使用地点"),
            edit = @Edit(title = "使用地点", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "equipmentLocation"))
    )
    private String equipmentLocation;

    @FabosJsonField(
            views = @View(title = "涂层材质"),
            edit = @Edit(title = "涂层材质")
    )
    private String coatingMaterial;

    @FabosJsonField(
            views = @View(title = "部件图片"),
            edit = @Edit(title = "部件图片",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(
                            fileTypes = {".jpg,.png"},
                            size = 5120,
                            maxLimit = 10,
                            type = AttachmentType.Type.IMAGE)
            )
    )
    private String pictureAttachment;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",readonly = @Readonly,type = EditType.CHOICE,
                    defaultVal = "EDIT",
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class)),
            dynamicField = @DynamicField(passive = true)
    )
    private String businessState;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "metal_coating_info_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "金属涂层检测信息明细", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "金属涂层检测信息明细", type = EditType.TAB_TABLE_ADD)
    )
    private List<MetalCoatingInfoDetail> detailList;
}
