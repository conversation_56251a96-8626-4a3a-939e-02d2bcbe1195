package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class EquipmentArchiveInfoHandler implements DependFiled.DynamicHandler<SpotInspectionTask> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(SpotInspectionTask deviceInsTask) {
        EquipmentArchive equipmentArchive = deviceInsTask.getEquipmentArchive();
        HashMap<String, Object> map = new HashMap<>();
        if (equipmentArchive == null) {
            map.put("deviceName", "");
            map.put("code", "");
        } else {
            EquipmentArchive equipDb = fabosJsonDao.getById(EquipmentArchive.class, equipmentArchive.getId());
            if (equipDb != null) {
                map.put("deviceName", equipDb.getName());
                map.put("code", equipDb.getEquipment().getCode());
            }
        }
        return map;
    }
}
