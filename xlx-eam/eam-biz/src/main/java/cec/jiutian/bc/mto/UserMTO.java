package cec.jiutian.bc.mto;

import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;

@Table(name = "fd_meta_user")
@Entity
@Getter
@FabosJson(
        name = "用户",
        filter = @Filter("state = 'Y'"),
        power = @Power(add = false, edit = false, delete = false, importable = false, print = false)
)
@SkipMetadataScanning
public class UserMTO extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号", index = -9),
            edit = @Edit(title = "账号", search = @Search(vague = true), notNull = true,
                    readonly = @Readonly
            )
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true),
                    readonly = @Readonly, notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", notNull = true, readonly = @Readonly,
                    search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "电话号码"),
            edit = @Edit(title = "电话号码",
                    search = @Search(vague = true)
            ))
    @SubTableField
    private String phoneNumber;

    /**
     * 该状态用于业务是否能选择该用户，该字段禁用不影响登录
     */
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    defaultVal = "Y",
                    notNull = true,
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;
}
