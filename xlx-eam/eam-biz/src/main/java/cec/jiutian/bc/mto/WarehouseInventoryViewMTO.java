package cec.jiutian.bc.mto;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "库存台账MTO"
)
@Entity
@Getter
@Setter
@Table(name = "warehouse_inventory_view")
public class WarehouseInventoryViewMTO {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "物料id", show = false),
            edit = @Edit(title = "物料id", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编号"),
            edit = @Edit(title = "物料编号")
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称")
    )
    private String materialName;

    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号")
    )
    private String materialSpecification;

    @FabosJsonField(
            views = @View(title = "物料类别", show = false),
            edit = @Edit(title = "物料类别", show = false)
    )
    private String materialType;

    @FabosJsonField(
            views = @View(title = "物料类别名称"),
            edit = @Edit(title = "物料类别名称")
    )
    private String materialTypeName;
    @FabosJsonField(
            views = @View(title = "本厂批号"),
            edit = @Edit(title = "本厂批号")
    )
    private String lotSerialId;
    @FabosJsonField(
            views = @View(title = "可用数量"),
            edit = @Edit(title = "可用数量", search = @Search(vague = true))
    )
    private Double availableQuantity;
    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位")
    )
    private String accountingUnit;
}
