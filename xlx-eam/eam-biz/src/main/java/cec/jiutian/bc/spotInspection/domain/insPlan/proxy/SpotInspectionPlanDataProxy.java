package cec.jiutian.bc.spotInspection.domain.insPlan.proxy;

import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.bc.utils.StocktakingDateUtil;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class SpotInspectionPlanDataProxy implements DataProxy<SpotInspectionPlan> {
    @Override
    public void beforeAdd(SpotInspectionPlan deviceInsPlan) {
        if(deviceInsPlan.getStartTime()!=null&&deviceInsPlan.getEndTime()!=null){
            if(!StocktakingDateUtil.checkDate(deviceInsPlan.getStartTime(),deviceInsPlan.getEndTime())){
                throw new FabosJsonApiErrorTip("开始日期不能晚于截止日期");
            }
        }
        deviceInsPlan.setEffective(YesOrNoStatus.NO.getValue());
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(d->{
                if(d.get("cycleNum")!=null&&d.get("cycle")!=null){
                    //d.put("cycle",d.get("cycleNum").toString()+ QcPlanCycleEnum.getValueByName(d.get("cycle").toString()));
                }
            });
        }
    }
}
