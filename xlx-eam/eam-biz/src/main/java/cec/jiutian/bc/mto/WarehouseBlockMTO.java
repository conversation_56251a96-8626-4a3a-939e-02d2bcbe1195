package cec.jiutian.bc.mto;

import cec.jiutian.bc.enums.FreezeStatusEnum;
import cec.jiutian.bc.enums.WareUseTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Entity
@Table(name = "mo_wrhs_blck") // 根据实际表名修改
@FabosJson(
        name = "库区",
        orderBy = "onlineDate desc, priority asc",
        power = @Power(
                importable = false, export = false, add = false, delete = false, edit = false
        ),
        filter = @Filter("freezeStatus = 'NotOnHold' and stockFlag = 'N'")
)
public class WarehouseBlockMTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    @FabosJsonField(
            views = @View(title = "自增ID", show = false),
            edit = @Edit(title = "自增ID",
                    show = false,
                    inputType = @InputType(type = "number"),
                    readonly = @Readonly(add = true, edit = true))
    )
    private Long id;

    @Column(name = "wrhs_id", nullable = false)
    @FabosJsonField(
            views = @View(title = "仓库识别码"),
            edit = @Edit(title = "仓库识别码",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(type = "number")
            )
    )
    private Long warehouseId;

    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "blck_nm", length = 40, nullable = false)
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库区代码"),
            edit = @Edit(title = "库区代码",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "blck_cd", length = 40)
    private String blockCode;

    @FabosJsonField(
            views = @View(title = "库区简称"),
            edit = @Edit(title = "库区简称",
                    inputType = @InputType(length = 40))
    )
    @Column(name = "blck_shrt_nm", length = 40)
    private String blockShortName;

    @FabosJsonField(
            views = @View(title = "库区描述"),
            edit = @Edit(title = "库区描述",
                    inputType = @InputType(length = 400))
    )
    @Column(name = "blck_ds", length = 400)
    private String blockDescription;

    @FabosJsonField(
            views = @View(title = "使用类型"),
            edit = @Edit(title = "使用类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WareUseTypeEnum.class),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "blck_usg_typ_cd", length = 40)
    private String blockUsageType;

    @FabosJsonField(
            views = @View(title = "库区单位数量"),
            edit = @Edit(title = "库区单位数量",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "shlf_cn", precision = 18, scale = 2)
    private BigDecimal shiftCount;

    @FabosJsonField(
            views = @View(title = "库区容积"),
            edit = @Edit(title = "库区容积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "vol_nb", precision = 18, scale = 2)
    private BigDecimal volume;

    @FabosJsonField(
            views = @View(title = "库区使用容积"),
            edit = @Edit(title = "库区使用容积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "usd_vol_nb", precision = 18, scale = 2)
    private BigDecimal usedVolume;

    @FabosJsonField(
            views = @View(title = "库区占地面积"),
            edit = @Edit(title = "库区占地面积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "cvrd_ara_nb", precision = 18, scale = 2)
    private BigDecimal coveredArea;

    @FabosJsonField(
            views = @View(title = "库区使用面积"),
            edit = @Edit(title = "库区使用面积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "usd_ara_nb", precision = 18, scale = 2)
    private BigDecimal usedArea;

    @FabosJsonField(
            views = @View(title = "库区高"),
            edit = @Edit(title = "库区高",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "hgt_nb", precision = 18, scale = 2)
    private BigDecimal height;

    @FabosJsonField(
            views = @View(title = "库区长"),
            edit = @Edit(title = "库区长",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "lngth_nb", precision = 18, scale = 2)
    private BigDecimal length;

    @FabosJsonField(
            views = @View(title = "库区宽"),
            edit = @Edit(title = "库区宽",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "wdth_nb", precision = 18, scale = 2)
    private BigDecimal width;

    @FabosJsonField(
            views = @View(title = "建区时间"),
            edit = @Edit(title = "建区时间",
                    inputType = @InputType(type = "datetime"))
    )
    @Column(name = "onln_dt")
    private Date onlineDate;

    @FabosJsonField(
            views = @View(title = "库区冻结状态"),
            edit = @Edit(title = "库区冻结状态",
                    search = @Search,
                    inputType = @InputType(length = 40),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FreezeStatusEnum.class)
            )
    )
    @Column(name = "hld_st", length = 40)
    private String freezeStatus;


    @FabosJsonField(
            views = @View(title = "使用优先级"),
            edit = @Edit(title = "使用优先级",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "prity_nb")
    private Long priority;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    inputType = @InputType(length = 2000))
    )
    @Column(name = "rmrk_tx", length = 2000)
    private String remark;

    @FabosJsonField(
            views = @View(title = "库存标记"),
            edit = @Edit(title = "库存标记",
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    inputType = @InputType(length = 2))
    )
    @Column(name = "tak_stk_flg", length = 2, nullable = false)
    private String stockFlag;


}