package cec.jiutian.bc.spotInspection.domain.insTask.model;


import cec.jiutian.bc.enums.NamingRuleCodeEnum;
import cec.jiutian.bc.equipmentArchive.domain.equipmentArchive.model.EquipmentArchive;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandard;
import cec.jiutian.bc.spotInspection.domain.insTask.handler.SpotInspectionTaskDetailsDynamicHandler;
import cec.jiutian.bc.spotInspection.domain.insTask.handler.SpotInspectionTaskExecuteHandler;
import cec.jiutian.bc.spotInspection.domain.insTask.handler.SpotInspectionTaskSubmitHandler;
import cec.jiutian.bc.spotInspection.domain.insTask.proxy.MySpotInspectionTaskProxy;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskSourceEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.*;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.*;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(name = "我的点检任务",
        orderBy = "MySpotInspectionTask.createTime desc",
        power = @Power(add = false, export = false, print = false, edit = false, delete = false, viewDetails = false),
        dataProxy = MySpotInspectionTaskProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "执行",
                        code = "SpotInspectionTask@EXECUTE",
                        operationHandler = SpotInspectionTaskExecuteHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        popupType = RowOperation.PopupType.FORM,
                        fabosJsonClass = SpotInspectionTaskExecute.class,
                        show = @ExprBool(
                                params = "SpotInspectionTask@EXECUTE",
                                exprHandler = UserRowOperationExprHandler.class
                        ),
                        ifExpr = "selectedItems[0].currentState == 'CLOSED' || selectedItems[0].isSubmit == '2'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                ),
                @RowOperation(
                        title = "提交",
                        code = "SpotInspectionTask@SUBMIT",
                        operationHandler = SpotInspectionTaskSubmitHandler.class,
                        mode = RowOperation.Mode.HEADER,
                        callHint = "该操作不可撤回，确定提交吗？",
                        ifExpr = "selectedItems[0].isSubmit != '1'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class,
                                params = "SpotInspectionTask@SUBMIT"
                        )
                )
        }
)
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
@FabosJsonI18n
@TemplateType(type = "multiTable")
public class MySpotInspectionTask extends NamingRuleBaseModel {
    @Override
    public String getNamingCode() {
        return NamingRuleCodeEnum.MySpotInspectionTask.name();
    }

    // 任务名称
    @FabosJsonField(
            views = @View(title = "任务名称", toolTip = true),
            edit = @Edit(title = "任务名称", notNull = true)
    )
    private String name;

    // 任务描述
    @FabosJsonField(
            views = @View(title = "任务描述", toolTip = true),
            edit = @Edit(title = "任务描述", notNull = true)
    )
    private String taskDesc;

    @Transient
    @FabosJsonField(
            views = @View(title = "点检设备编码", column = "generalCode"),
            edit = @Edit(title = "点检设备编码", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode"))
    )
    private EquipmentArchive equipmentArchive;

    // 设备id
    @FabosJsonField(
            views = @View(title = "设备id", show = false),
            edit = @Edit(title = "设备id", show = false),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "id"))
    )
    private String deviceId;

    // 设备名称
    @FabosJsonField(
            views = @View(title = "设备名称"),
            edit = @Edit(title = "设备名称", readonly = @Readonly),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipmentArchive", beFilledBy = "name"))
    )
    private String deviceName;

    // 标准名称
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检标准", column = "name"),
            edit = @Edit(title = "点检标准",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private SpotInspectionStandard SpotInspectionStandard;

    // 计划点检时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "点检时间"),
            edit = @Edit(title = "点检时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date startTime;

    // 点检人
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "点检人", column = "name"),
            edit = @Edit(title = "点检人",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO checkPerson;

    // 点检状态
    @FabosJsonField(
            views = @View(title = "点检状态"),
            edit = @Edit(title = "点检状态", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionTaskStateEnum.class))
    )
    private String currentState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "任务完成时间"),
            edit = @Edit(title = "任务完成时间", show = false, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME), search = @Search(vague = true))
    )
    private Date finishTime;

    // 异常数量, 用于统计
    @FabosJsonField(
            views = @View(title = "异常数量"),
            edit = @Edit(title = "异常数量", show = false, type = EditType.NUMBER, readonly = @Readonly)
    )
    private Integer exceptionNum;

    // 任务来源
    @FabosJsonField(
            views = @View(title = "任务来源"),
            edit = @Edit(title = "任务来源", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionTaskSourceEnum.class))
    )
    private String taskSource;

    // 是否跳过点检
    @FabosJsonField(
            views = @View(title = "是否跳过点检"),
            edit = @Edit(title = "是否跳过点检", type = EditType.CHOICE, show = false,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
    )
    )
    private String isSkip;

    // 跳过原因
    @FabosJsonField(
            views = @View(title = "跳过原因", toolTip = true),
            edit = @Edit(title = "跳过原因", show = false)
    )
    private String skipReason;

    // 关联子模型
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_task_id")
    @FabosJsonField(
            views = @View(title = "点检设备清单", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "点检设备清单", type = EditType.TAB_REFERENCE_GENERATE,
            dependFieldDisplay = @DependFieldDisplay(showOrHide = "deviceInsStandard != null"))/*,
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "deviceInsStandard",
                    dynamicHandler = SpotInspectionTaskDetailsDynamicHandler.class))*/
    )
    private List<MySpotInspectionTaskDetail> spotInspectionTaskDetails;

    // 变更记录
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_task_id")
    @OrderBy
    @FabosJsonField(
            views = @View(title = "变更记录", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "变更记录", type = EditType.TAB_TABLE_ADD, show = false)
    )
    private List<SpotInspectionChangeRecord> spotInspectionChangeRecords;

    // 是否已提交 不展示, 用于控制 提交按钮权限
    // 默认 1 可提交; 2 已经提交, 不可再提交
    @FabosJsonField(
            views = @View(title = "是否已提交", show = false),
            edit = @Edit(title = "是否已提交", show = false)
    )
    private String isSubmit;
}
