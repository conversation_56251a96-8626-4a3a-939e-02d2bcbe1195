package cec.jiutian.bc.spotInspection.domain.insStandard.model;

import cec.jiutian.bc.equipmentArchive.domain.equipment.model.Equipment;
import cec.jiutian.bc.spotInspection.domain.insStandard.proxy.SpotInspectionStandardDataProxy;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.*;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 设备点检标准
 */
@Entity
@Table(name = "qms_spot_inspection_standard")
@Getter
@Setter
@FabosJson(name = "设备点检标准",
        dataProxy = SpotInspectionStandardDataProxy.class,
        orderBy = "SpotInspectionStandard.createDate desc",power = @Power(export = false,importable = false)
)
@FabosJsonI18n
public class SpotInspectionStandard extends BaseModel {
    //名称
    @FabosJsonField(
            views = @View(title = "标准名称"),
            edit = @Edit(title = "标准名称",search = @Search,notNull = true)
    )
    private String name;
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "适用设备", column = "name"),
            edit = @Edit(title = "适用设备",notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private Equipment equipment;
    //设备类型
    @FabosJsonField(
            views = @View(title = "适用设备编码"),
            edit = @Edit(title = "适用设备编码",notNull = true,readonly = @Readonly
            ),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "equipment", beFilledBy = "code"))

    )
    private String deviceCode;
    //创建人
    @FabosJsonField(
            views = @View(title = "创建人"),
            edit = @Edit(title = "创建人",show = false)
    )
    private String createName;
    //创建时间
    @FabosJsonField(
            views = @View(title = "创建时间"),
            edit = @Edit(title = "创建时间",show = false,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    //说明
    @FabosJsonField(
            views = @View(title = "其他说明",toolTip = true),
            edit = @Edit(title = "其他说明",type = EditType.TEXTAREA,
                    inputType = @InputType(length = 200))
    )
    private String description;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_standard_id")
    @FabosJsonField(
            edit = @Edit(title = "检查项目", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "检查项目", type= ViewType.TABLE_VIEW)
    )
    private List<SpotInspectionStandardItem> spotInspectionStandardItemList;

}
