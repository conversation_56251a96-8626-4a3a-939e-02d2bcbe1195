package cec.jiutian.bc.spotInspection.domain.insTask.model;


import cec.jiutian.bc.spotInspection.domain.insTask.handler.SpotInspectionTaskDetailsDynamicHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.*;
import cec.jiutian.view.field.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(name = "执行")
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
@FabosJsonI18n
public class SpotInspectionTaskExecute extends MetaModel {
    @FabosJsonField(
            views = @View(title = "点检任务单号"),
            edit = @Edit(title = "点检任务单号", readonly = @Readonly)
    )
    private String generalCode;

    // 关联子模型
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "spot_inspection_task_id")
    @FabosJsonField(
            views = @View(title = "点检设备清单", type= ViewType.TABLE_VIEW),
            edit = @Edit(title = "点检设备清单", type = EditType.TAB_REFERENCE_GENERATE),
            /*dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "deviceInsStandard",
                    dynamicHandler = SpotInspectionTaskDetailsDynamicHandler.class)),*/
            referenceGenerateType = @ReferenceGenerateType(editable = {"record", "result", "otherDesc"})
    )
    private List<ExecuteSpotInspectionTaskDetail> executeSpotInspectionTaskDetails;
}
