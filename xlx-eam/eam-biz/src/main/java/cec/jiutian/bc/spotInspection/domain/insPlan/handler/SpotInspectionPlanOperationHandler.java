package cec.jiutian.bc.spotInspection.domain.insPlan.handler;

import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Component
public class SpotInspectionPlanOperationHandler implements OperationHandler<SpotInspectionPlan,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;
//    @Resource
//    private DeviceInsTaskService deviceInsTaskService;
    @Override
    @Transactional
    public String exec(List<SpotInspectionPlan> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            SpotInspectionPlan plan = data.get(0);
            String state = param[0];
            plan.setEffective(state);
            this.checkPlan(plan);
            if(plan.getEffective().equals(YesOrNoStatus.YES.getValue())){
                this.handleNextCheckTime(plan);
            }
            fabosJsonDao.mergeAndFlush(plan);
        }
        return "msg.success('操作成功')";
    }
    private void checkPlan(SpotInspectionPlan plan){
        if(plan.getEffective().equals(YesOrNoStatus.YES.getValue())&& plan.getEndTime().before(new Date())){
            throw new FabosJsonApiErrorTip("已过计划截止日期，不能启用");
        }
    }

    /**
     *
     *
     * 启用：
     *  1.判断下一次点检时间是否存在值？
     *      1）不存在（第一次生效），那么判断当前生效时间是否在计划开始时间和结束时间之间？
     *          a.如果是，直接生成点检任务，并更新下次点检时间为当前时间的下一个周期。
     *          b.如果不是，那么不生成任务，并且更新下次点检时间为计划开始时间。（这里当前时间不可能在计划结束之间之后，这种情况会生效失败）
     *      2）存在（非第一次生效），那么判断下一次点检时间是否在当前时间之前？
     *          a.在这之前，说明需要生成任务（点检时间为当天），更新下一次点检时间为当前时间的下一个周期
     *          b.在这之后，说明不需要生成任务。不需要处理，后续定时任务会根据下次点检时间生成任务。
     * @param deviceInsPlan
     */
    private void handleNextCheckTime(SpotInspectionPlan deviceInsPlan){
        Date nowDate = new Date();
        Date nextCheckTime = deviceInsPlan.getNextCheckTime();
        if(nextCheckTime==null){
            //第一次生效
            if(nowDate.after(deviceInsPlan.getStartTime())&&nowDate.before(deviceInsPlan.getEndTime())){
                //当前生效时间在计划开始时间和结束时间之间
                this.handleGenerateOrder(deviceInsPlan,nowDate);
            }else {
                //当前生效时间不在计划开始时间和结束时间之间
                deviceInsPlan.setNextCheckTime(deviceInsPlan.getStartTime());
            }
        }else {
            //多次生效
            if(nowDate.after(nextCheckTime)){
                //在当前时间之前，说明需要生成任务
                this.handleGenerateOrder(deviceInsPlan,nowDate);
            }else {
                //在当前时间之后，说明不需要生成任务   通过定时任务处理
            }
        }
    }

    private void handleGenerateOrder(SpotInspectionPlan deviceInsPlan, Date nowDate){
//        deviceInsTaskService.generateDeviceInsTask(deviceInsPlan);
//        SpotInspectionSkipTimeEnum.Enum anEnum = StringUtils.isNotBlank(deviceInsPlan.getSkipTime()) ? SpotInspectionSkipTimeEnum.Enum.valueOf(deviceInsPlan.getSkipTime()) : null;
//        Date nextDate = StocktakingDateUtil.addTime(nowDate, deviceInsPlan.getCycleNum(), QcPlanCycleEnum.Enum.valueOf(deviceInsPlan.getCycle()), anEnum);
//        deviceInsPlan.setNextCheckTime(nextDate.before(deviceInsPlan.getEndTime())?nextDate:null);
//        deviceInsPlan.setLastCheckTime(nowDate);
    }
}
