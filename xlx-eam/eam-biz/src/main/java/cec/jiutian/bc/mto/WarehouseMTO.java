package cec.jiutian.bc.mto;


import cec.jiutian.bc.enums.FreezeStatusEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Entity
@Table(name = "mo_wrhs")
@FabosJson(
        name = "仓库",
        orderBy = "onlineDate desc",
        power = @Power(
                importable = false, export = false, add = false, delete = false, edit = false
        ),
        filter = @Filter("warehouseTypeCode = '20' and freezeStatus = 'NotOnHold' and stockFlag = 'N'")
)
public class WarehouseMTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @FabosJsonField(
            views = @View(title = "仓库ID", show = false),
            edit = @Edit(title = "仓库ID", show = false)
    )
    private Long id;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "wrhs_nm", length = 40)
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "仓库代码"),
            edit = @Edit(title = "仓库代码",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "wrhs_cd", length = 40)
    private String warehouseCode;

    @FabosJsonField(
            views = @View(title = "仓库简称"),
            edit = @Edit(title = "仓库简称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "wrhs_shrt_nm", length = 40)
    private String warehouseShortName;

    @FabosJsonField(
            views = @View(title = "仓库描述"),
            edit = @Edit(title = "仓库描述",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 400))
    )
    @Column(name = "wrhs_ds", length = 400)
    private String warehouseDescription;

    @FabosJsonField(
            views = @View(title = "仓库类型代码" ,show = false),
            edit = @Edit(title = "仓库类型代码",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "wrhs_typ_cd", length = 40)
    private String warehouseTypeCode;


    @FabosJsonField(
            views = @View(title = "管理部门名称"),
            edit = @Edit(title = "管理部门名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40)
            )
    )
    @Column(name = "mng_hrchy_nm", length = 40)
    private String managementDeptName;

    @FabosJsonField(
            views = @View(title = "冻结状态"),
            edit = @Edit(title = "冻结状态",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FreezeStatusEnum.class),
                    inputType= @InputType(length = 40))
    )
    @Column(name = "hld_st", length = 40)
    private String freezeStatus;

    @FabosJsonField(
            views = @View(title = "仓库地址"),
            edit = @Edit(title = "仓库地址",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 200))
    )
    @Column(name = "agv_lctn_nm", length = 200)
    private String dispatchAddress;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 2000))
    )
    @Column(name = "rmrk_tx", length = 2000)
    private String remark;

    @FabosJsonField(
            views = @View(title = "供应商名称"),
            edit = @Edit(title = "供应商名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 400))
    )
    @Column(name = "vndr_nm", length = 400)
    private String vendorName;

    @FabosJsonField(
            views = @View(title = "仓库容量"),
            edit = @Edit(title = "仓库容量",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true)
            )
    )
    @Column(name = "blck_cn", precision = 18, scale = 2)
    private BigDecimal blockCount;

    @FabosJsonField(
            views = @View(title = "上线日期"),
            edit = @Edit(title = "上线日期",
                    readonly = @Readonly(add = true, edit = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE)
            )
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Column(name = "onln_dt")
    private Date onlineDate;

    @FabosJsonField(
            views = @View(title = "仓库宽度"),
            edit = @Edit(title = "仓库宽度",
                    search = @Search(vague = true),
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "wdth_nb", precision = 18, scale = 2)
    private BigDecimal width;


    @FabosJsonField(
            views = @View(title = "仓库长度"),
            edit = @Edit(title = "仓库长度",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "lngth_nb", precision = 18, scale = 2)
    private BigDecimal length;

    @FabosJsonField(
            views = @View(title = "仓库高度"),
            edit = @Edit(title = "仓库高度",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "hgt_nb", precision = 18, scale = 2)
    private BigDecimal height;

    @FabosJsonField(
            views = @View(title = "仓库面积"),
            edit = @Edit(title = "仓库面积",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "cvrd_ara_nb", precision = 18, scale = 2)
    private BigDecimal coveredArea;

    @FabosJsonField(
            views = @View(title = "仓库可用面积"),
            edit = @Edit(title = "仓库可用面积",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "usd_ara_nb", precision = 18, scale = 2)
    private BigDecimal usedArea;

    @FabosJsonField(
            views = @View(title = "仓库体积"),
            edit = @Edit(title = "仓库体积",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "vol_nb", precision = 18, scale = 2)
    private BigDecimal volume;

    @FabosJsonField(
            views = @View(title = "仓库已用体积"),
            edit = @Edit(title = "仓库已用体积",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    @Column(name = "usd_vol_nb", precision = 18, scale = 2)
    private BigDecimal usedVolume;

    @FabosJsonField(
            views = @View(title = "仓库状态",show = false),
            edit = @Edit(title = "仓库状态",
                    search = @Search,
                    inputType = @InputType(length = 40),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FreezeStatusEnum.class)
            )
    )
    @Column(name = "tak_stk_flg", length = 2)
    private String stockFlag;

    @FabosJsonField(
            views = @View(title = "最大库存量"),
            edit = @Edit(title = "最大库存量",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "max_stock_quantity", precision = 18, scale = 2)
    private BigDecimal maxStockQuantity;

}