package cec.jiutian.bc.toolManagement.port.dto;

import cec.jiutian.bc.toolManagement.domain.materialRequest.model.InventoryMaterial;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class StockInDTO {
    private String stockInRecordCategoryCode;

    private String pickRequestGid;

    private String pickRequestNumber;

    private List<StockInDetailDTO> stockInDetailList;

    private String requestUserId;

    private String requestUserName;

    public static StockInDTO buildStockInDTO(String category, String id, String generalCode, String applicantName, String applicantId) {
        StockInDTO stockInDTO = new StockInDTO();
        stockInDTO.setStockInRecordCategoryCode(category);
        stockInDTO.setPickRequestGid(id);
        stockInDTO.setPickRequestNumber(generalCode);
        stockInDTO.setRequestUserName(applicantName);
        stockInDTO.setRequestUserId(applicantId);
        return stockInDTO;
    }
}
