package cec.jiutian.bc.spotInspection.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 点检任务-点检结果枚举
 * <AUTHOR>
 * @date 2025/4/2 9:52
 */
public class SpotInspectionTaskResultEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        EXCELLENT("优"),
        AVERAGE("中"),
        POOR("差"),
        YES("是"),
        NO("否");

        private final String value;

    }

    @Override
    public List<VLModel> fetchFilter(Map<String, Object> value, String[] params) {
        List<VLModel> vlModelList = new ArrayList<>();
        if (value != null) {
            boolean is5s = (boolean) value.get("is5s");
            if (is5s) {
                vlModelList.add(new VLModel("EXCELLENT", "优"));
                vlModelList.add(new VLModel("AVERAGE", "中"));
                vlModelList.add(new VLModel("POOR", "差"));
            } else {
                vlModelList = defaultList();
            }
        } else {
            vlModelList = defaultList();
        }
        return vlModelList;
    }

    private List<VLModel> defaultList() {
        List<VLModel> vlModelList = new ArrayList<>();
        vlModelList.add(new VLModel("YES", "是"));
        vlModelList.add(new VLModel("NO", "否"));
        return vlModelList;
    }
}
