package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@FabosJson(name = "变更")
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
@FabosJsonI18n
public class SpotInspectionTaskChange extends MetaModel {
    @FabosJsonField(
            views = @View(title = "点检任务单号"),
            edit = @Edit(title = "点检任务单号", readonly = @Readonly)
    )
    private String generalCode;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "当前点检时间"),
            edit = @Edit(title = "当前点检时间",
                    readonly = @Readonly,
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date startTime;

    @Transient
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "当前点检人", column = "name"),
            edit = @Edit(title = "当前点检人",
                    type = EditType.REFERENCE_TABLE,
                    readonly = @Readonly,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO checkPerson;

    @Transient
    @FabosJsonField(
            views = @View(title = "变更原因", toolTip = true),
            edit = @Edit(title = "变更原因", type = EditType.TEXTAREA)
    )
    private String changeReason;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "变更点检时间"),
            edit = @Edit(title = "变更点检时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    private Date newStartTime;

    @Transient
    @ManyToOne
    @FabosJsonField(
            views = @View(title = "变更点检人", column = "name"),
            edit = @Edit(title = "变更点检人",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    search = @Search())
    )
    private UserMTO newCheckPerson;
}
