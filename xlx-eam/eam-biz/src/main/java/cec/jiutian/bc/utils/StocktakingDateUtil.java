package cec.jiutian.bc.utils;

import cec.jiutian.bc.enums.StocktakingCycleEnum;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 */
public class StocktakingDateUtil {

    /**
     * 盘点计划
     * @param date
     * @param timeUnit
     * @return
     */
    public static Date addTime(Date date, StocktakingCycleEnum.Enum timeUnit) {
        // 将 Date 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());

        // 根据标识添加相应的时间单位
        zonedDateTime = switch (timeUnit) {
            case QUARTER -> zonedDateTime.plusMonths(3); // 一个季度是 3 个月
            case MONTH -> zonedDateTime.plusMonths(1);
            case WEEK -> zonedDateTime.plusWeeks(1);
        };
        // 将 ZonedDateTime 转换回 Date
        return Date.from(zonedDateTime.toInstant());
    }

    //校验开始时间不能晚于截止时间
    public static boolean checkDate(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = sdf.format(startDate);
        String endDateStr = sdf.format(endDate);
        return startDate.before(endDate)||startDateStr.equals(endDateStr);
    }

    /**
     * 根据传入的Date对象，提取时分秒，并与当前系统的年月日拼接成一个新的时间
     *
     * @param date 传入的Date对象（包含年月日时分秒）
     * @return 新的时间（当前系统的年月日 + 传入的时分秒）
     */
    public static Date combineCurrentDateWithTime(Date date) {
        // 将传入的Date转换为LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());

        // 提取时分秒
        LocalTime time = dateTime.toLocalTime();

        // 获取当前系统的年月日
        LocalDate currentDate = LocalDate.now();

        // 将当前系统的年月日与提取的时分秒拼接成一个新的LocalDateTime
        LocalDateTime newDateTime = LocalDateTime.of(currentDate, time);

        // 将新的LocalDateTime转换回Date
        return Date.from(newDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
