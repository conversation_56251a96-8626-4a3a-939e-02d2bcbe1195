package cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.repository;

import cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.model.MetalCoatingInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description TODO
 */
public interface MetalCoatingInfoRepository extends JpaRepository<MetalCoatingInfo, String> {

    @Query(
            value = """
            select
            ea.id as id,
            ea.general_code as general_code,
            e.code as code,
            ea.name as name,
            ea.abbreviation as abbreviation,
            ea.specification as specification,
            ea.equipment_sign as equipment_sign,
            et.code as equipment_category,
            et.name as equipment_category_name,
            mu.unit_chn_name as account_unit,
            ea.serial_code as serial_code,
            ea.asset_code as asset_code,
            ea.factory_area_id as factory_area_id,
            ea.factory_area_name as factory_area_name,
            ea.factory_line_id as factory_line_id,
            ea.factory_line_name as factory_line_name,
            ea.process_id as process_id,
            ea.process_name as process_name,
            ea.discharge_date as discharge_date,
            ea.on_line_date as on_line_date,
            ea.business_state as business_state,
            ea.manufacturer as manufacturer,
            ea.supplier as supplier,
            ea.equipment_location as equipment_location,
            ea.department as department,
            ea.inspection_flag as inspection_flag,
            ea.create_time as create_time,
            ea.update_time as update_time
            FROM eam_ea_equipment_archive ea
            left join measure_unit mu on mu.id = ea.account_unit_id
            left join eam_ea_equipment_info e on e.id = ea.equipment_id
            left join eam_ea_equipment_type et on et.id = e.equipment_type_id
            WHERE
            ((:name IS NULL OR :name = '') OR ea.name LIKE CONCAT('%', :name, '%'))
            AND ((:code IS NULL OR :code = '') OR ea.general_code LIKE CONCAT('%', :code, '%'))
            AND ((:equipmentSign IS NULL OR :equipmentSign = '') OR ea.equipment_sign = :equipmentSign)
            AND (coalesce (:excludeList , null) is null or ea.id NOT IN ( :excludeList ))
            """,
            nativeQuery = true
    )
    List<MetalCoatingInfo> getMetalCoatingInfo(@Param("name") String name, @Param("code") String code, @Param("equipmentSign") String equipmentSign);
}
