package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.spotInspection.domain.insTask.model.ExecuteSpotInspectionTaskDetail;
import cec.jiutian.bc.spotInspection.domain.insTask.model.MySpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskDetail;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskExecute;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionValueTypeEnum;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SpotInspectionTaskExecuteHandler implements OperationHandler<MySpotInspectionTask, SpotInspectionTaskExecute> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    // 值类型： 数字类型
    private static final String NUMBER = SpotInspectionValueTypeEnum.Enum.NUMBER.name();
    // 值类型： 百分比类型
    private static final String PERCENTAGE = SpotInspectionValueTypeEnum.Enum.PERCENTAGE.name();

    @Override
    @Transactional
    public String exec(List<MySpotInspectionTask> data, SpotInspectionTaskExecute modelObject, String[] param) {
        // 校验数据: 数据完整性、数据合法性(根据数值类型)
        this.checkParam(modelObject);
        // 数据录入完成之后, 设置提交状态为1 可提交
        MySpotInspectionTask myDeviceInsTaskDb = fabosJsonDao.getById(MySpotInspectionTask.class, modelObject.getId());
        myDeviceInsTaskDb.setIsSubmit("1");
        // 将弹窗中的执行数据复制到任务主单中 并更新
        List<ExecuteSpotInspectionTaskDetail> execDetailList = modelObject.getExecuteSpotInspectionTaskDetails();
        Map<String, ExecuteSpotInspectionTaskDetail> detailMap = execDetailList.stream()
                .collect(Collectors.toMap(ExecuteSpotInspectionTaskDetail::getId, detail -> detail));
        myDeviceInsTaskDb.getSpotInspectionTaskDetails().forEach(
                detail -> {
                    if (detailMap.containsKey(detail.getId())) {
                        ExecuteSpotInspectionTaskDetail d = detailMap.get(detail.getId());
                        detail.setRecord(d.getRecord());
                        detail.setResult(d.getResult());
                        detail.setOtherDesc(d.getOtherDesc());
                    }
        });
        fabosJsonDao.update(myDeviceInsTaskDb);
        return null;
    }

    @Override
    public SpotInspectionTaskExecute fabosJsonFormValue(List<MySpotInspectionTask> data, SpotInspectionTaskExecute fabosJsonForm, String[] param) {
        if (CollectionUtils.isEmpty(data)) {
            return fabosJsonForm;
        }

        MySpotInspectionTask mySpotInspectionTask = data.get(0);
        BeanUtils.copyProperties(data.get(0), fabosJsonForm);

        List<ExecuteSpotInspectionTaskDetail> detailList = new ArrayList<>();
        mySpotInspectionTask.getSpotInspectionTaskDetails().forEach(d -> {
            ExecuteSpotInspectionTaskDetail taskDetail = new ExecuteSpotInspectionTaskDetail();
            BeanUtils.copyProperties(d, taskDetail, "id");
            detailList.add(taskDetail);
        });

        fabosJsonForm.setExecuteSpotInspectionTaskDetails(detailList);
        return fabosJsonForm;
    }

    private void checkParam(SpotInspectionTaskExecute modelObject) {
        List<ExecuteSpotInspectionTaskDetail> executeList = modelObject.getExecuteSpotInspectionTaskDetails();
        for (ExecuteSpotInspectionTaskDetail deviceInsTaskDetail : executeList) {
            // 先检测参数完整性
            if (deviceInsTaskDetail.getResult() == null || deviceInsTaskDetail.getRecord() == null) {
                throw new FabosJsonApiErrorTip("点检记录或点检结果数据不完整，请确认！");
            }
            // 检测数值类型是否符合
            if (NUMBER.equals(deviceInsTaskDetail.getValueType())) {
                boolean isNumber = NumberUtils.isCreatable(deviceInsTaskDetail.getRecord());
                if (!isNumber) throw new FabosJsonApiErrorTip("点检记录数据非数值类型，请确认！");
            }
            // 检测百分比类型是否符合
            if (PERCENTAGE.equals(deviceInsTaskDetail.getValueType())) {
                if (!isValidPercentage(Double.parseDouble(deviceInsTaskDetail.getRecord()))) {
                    throw new FabosJsonApiErrorTip("点检记录数据非有效百分比数值，请确认！");
                }
            }
        }
    }

    public boolean isValidPercentage(double value) {
        return value >= 0 && value <= 100;
    }

}
