package cec.jiutian.bc.spotInspection.domain.insTask.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "跳过点检")
@Entity
@Getter
@Setter
@Table(name = "qms_spot_inspection_task")
public class SpotInspectionTaskSkip extends MetaModel {
    @FabosJsonField(
            views = @View(title = "点检任务单号"),
            edit = @Edit(title = "点检任务单号", readonly = @Readonly)
    )
    private String generalCode;

    // 跳过原因
    @FabosJsonField(
            views = @View(title = "跳过原因", toolTip = true),
            edit = @Edit(title = "跳过原因", type = EditType.TEXTAREA, notNull = true)
    )
    private String skipReason;
}
