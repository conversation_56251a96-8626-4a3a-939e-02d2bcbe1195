package cec.jiutian.bc.metalCoating.remote.controller;

import cec.jiutian.bc.metalCoating.remote.vo.MetalMaoTrendQueryDTO;
import cec.jiutian.bc.metalCoating.service.MetalMapTrendService;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description TODO
 */
@RequestMapping("/metalMapTrendController")
@RestController
public class MetalMapTrendController {

    @Resource
    private MetalMapTrendService metalMapTrendService;

    @PostMapping("/getMetalMapTrend")
    public JSONObject getMetalMapTrend(@RequestBody MetalMaoTrendQueryDTO queryDTO) {
        return metalMapTrendService.getMetalMapTrend(queryDTO);
    }

    @PostMapping("/getEquipmentArchive")
    public List<Map<String,Object>> getEquipmentArchive() {
        return metalMapTrendService.getEquipmentArchive();
    }

    @PostMapping("/getEquipmentComponentDetail")
    public List<Map<String,Object>> getEquipmentComponentDetail(@RequestBody MetalMaoTrendQueryDTO queryDTO) {
        return metalMapTrendService.getEquipmentComponentDetail(queryDTO);
    }

    @PostMapping("/getCoatingMaterial")
    public List<String> getCoatingMaterial(@RequestBody MetalMaoTrendQueryDTO queryDTO) {
        return metalMapTrendService.getCoatingMaterial(queryDTO);
    }
}
