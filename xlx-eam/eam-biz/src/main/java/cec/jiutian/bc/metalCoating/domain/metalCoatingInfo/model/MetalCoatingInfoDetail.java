package cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.model;

import cec.jiutian.bc.enums.CheckResultEnum;
import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionQcPlanCycleEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description TODO
 */
@Table(name = "eam_mc_metal_coating_info_detail")
@FabosJson(
        name = "金属涂层检查信息详情",
        orderBy = "MetalCoatingInfoDetail.createTime desc"
)
@Entity
@Getter
@Setter
public class MetalCoatingInfoDetail extends MetaModel {

    @FabosJsonField(
            views = {
                    @View(title = "金属涂层检查信息",show = false, column = "generalCode")
            },
            edit = @Edit(title = "金属涂层检查信息", type = EditType.REFERENCE_TABLE,show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "generalCode")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("detailList")
    private MetalCoatingInfo metalCoatingInfo;

    @FabosJsonField(
            views = @View(title = "动作状态"),
            edit = @Edit(title = "动作状态", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "ActionStatus"))
    )
    private String actionStatus;

    @FabosJsonField(
            views = @View(title = "磨损风险"),
            edit = @Edit(title = "磨损风险")
    )
    private String wearRisk;

    @FabosJsonField(
            views = @View(title = "检查周期",show = false),
            edit = @Edit(title = "检查周期",notNull = true,inputGroup = @InputGroup(postfix = "#{cycle}")
            )
    )
    private Integer cycleNum;
    @FabosJsonField(
            views = @View(title = "周期"),
            edit = @Edit(title = "周期",notNull = true,type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionQcPlanCycleEnum.class),defaultVal = "DAY")
    )
    private String cycle;

    @FabosJsonField(
            views = @View(title = "检查工具"),
            edit = @Edit(title = "检查工具")
    )
    private String inspectTool;

    @FabosJsonField(
            views = @View(title = "检测指标"),
            edit = @Edit(title = "检测指标", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "USUAL_UNIT"),
                    search = @Search(vague = true)
            )
    )
    private String inspectTarget;

    @FabosJsonField(
            views = @View(title = "实测结果"),
            edit = @Edit(title = "实测结果",notNull = true)
    )
    private String inspectResult;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "检查时间"),
            edit = @Edit(title = "检查时间", notNull = true, type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE_TIME),
                    inputType = @InputType(length = 40))
    )
    private Date inspectDate;

    @FabosJsonField(
            views = @View(title = "处理措施"),
            edit = @Edit(title = "处理措施")
    )
    private String handleMeasure;

    @FabosJsonField(
            views = @View(title = "处理结果"),
            edit = @Edit(title = "处理结果")
    )
    private String handleResult;

    @FabosJsonField(
            views = @View(title = "检查人", column = "name"),
            edit = @Edit(title = "检查人",
                    type = EditType.REFERENCE_TABLE,
                    allowAddMultipleRows = false,
                    referenceTableType = @ReferenceTableType(type = TabTableReferType.SelectShowTypeMTM.LIST)
            )
    )
    @ManyToOne
    @JoinColumn(name = "inspect_person_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private UserMTO userMTO;

    @FabosJsonField(
            views = @View(title = "品质确认"),
            edit = @Edit(title = "品质确认",type = EditType.CHOICE,choiceType = @ChoiceType(fetchHandler = CheckResultEnum.class))
    )
    private String qualityConfirm;
}
