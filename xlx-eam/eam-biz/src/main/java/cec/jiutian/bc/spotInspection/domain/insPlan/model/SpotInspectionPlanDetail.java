package cec.jiutian.bc.spotInspection.domain.insPlan.model;

import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionValueTypeEnum;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "eam_spot_inspection_plan_detail")
@Getter
@Setter
@FabosJson(name = "设备点检计划",
        orderBy = "SpotInspectionPlanDetail.createTime desc",power = @Power(edit = false,export = false,importable = false),
        rowBaseOperation = @RowBaseOperation(
                code = "edit",
                ifExpr = "1==1",//子表不需要编辑按钮
                ifExprBehavior = RowBaseOperation.IfExprBehavior.HIDE
        )
)
@FabosJsonI18n
public class SpotInspectionPlanDetail extends BaseModel {
    //检查项名称
    @FabosJsonField(
            views = @View(title = "检查项名称"),
            edit = @Edit(title = "检查项名称",notNull = true)
    )
    private String name;
    //检查标准
    @FabosJsonField(
            views = @View(title = "检查标准"),
            edit = @Edit(title = "检查标准",notNull = true)
    )
    private String standard;
    //描述
    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述")
    )
    private String description;
    //值类型
    @FabosJsonField(
            views = @View(title = "值类型"),
            edit = @Edit(title = "值类型", type = EditType.CHOICE,notNull = true,
                    choiceType = @ChoiceType(fetchHandler = SpotInspectionValueTypeEnum.class))
    )
    private String valueType;

    //上限值
    @FabosJsonField(
            views = @View(title = "上限值"),
            edit = @Edit(title = "上限值",numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double upperValue;
    //下限值
    @FabosJsonField(
            views = @View(title = "下限值"),
            edit = @Edit(title = "下限值",numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double lowerValue;
    //默认值
    @FabosJsonField(
            views = @View(title = "标准值"),
            edit = @Edit(title = "标准值",numberType = @NumberType(min=0,max = 9999999,precision = 2))
    )
    private Double defaultValue;
    //是否5s
    @FabosJsonField(
            views = @View(title = "是否5S"),
            edit = @Edit(title = "是否5S")
    )
    private Boolean is5s;


    @FabosJsonField(
            views = {
                    @View(title = "点检计划", column = "name", show = false)
            },
            edit = @Edit(title = "点检计划", type = EditType.REFERENCE_TABLE, show = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("spotInspectionPlanDetailList")
    private SpotInspectionPlan spotInspectionPlan;

}
