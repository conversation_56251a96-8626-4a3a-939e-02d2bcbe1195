package cec.jiutian.bc.spotInspection.domain.insStandard.handler;

import cec.jiutian.bc.spotInspection.domain.insStandard.model.SpotInspectionStandardItem;
import cec.jiutian.view.DependFiled;

import java.util.HashMap;
import java.util.Map;

public class EamComparisonMethodDynamicHandler implements DependFiled.DynamicHandler<SpotInspectionStandardItem> {
    @Override
    public Map<String, Object> handle(SpotInspectionStandardItem inspectionItemTarget) {
        Map<String, Object> result = new HashMap<>();
        result.put("comparisonMethod",null);
        return result;
    }
}
