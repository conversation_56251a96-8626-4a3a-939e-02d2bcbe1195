package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.mto.UserMTO;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionChangeRecord;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTask;
import cec.jiutian.bc.spotInspection.domain.insTask.model.SpotInspectionTaskChange;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class SpotInspectionTaskChangeHandler implements OperationHandler<SpotInspectionTask, SpotInspectionTaskChange> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Transactional
    @Override
    public String exec(List<SpotInspectionTask> data, SpotInspectionTaskChange modelObject, String[] param) {
        String generalCode = modelObject.getGeneralCode();
        SpotInspectionTask query = new SpotInspectionTask();
        query.setGeneralCode(generalCode);
        SpotInspectionTask deviceInsTaskDb = fabosJsonDao.selectOne(query);
        // 校验是否有变更, 有变更才记录, 无变更不记录
        if (isTaskModified(deviceInsTaskDb, modelObject)) {
            // 先更新任务主单记录
            deviceInsTaskDb.setCheckPerson(modelObject.getNewCheckPerson() == null ? deviceInsTaskDb.getCheckPerson() : modelObject.getNewCheckPerson());
            deviceInsTaskDb.setStartTime(modelObject.getNewStartTime() == null ? deviceInsTaskDb.getStartTime() : modelObject.getNewStartTime());
            deviceInsTaskDb.setUpdateBy(modelObject.getUpdateBy());
            // 再添加到变更记录中
            SpotInspectionChangeRecord newChangeRecord = new SpotInspectionChangeRecord();
            // 变更时间、变更原因
            newChangeRecord.setChangeTime(new Date());
            newChangeRecord.setChangeReason(modelObject.getChangeReason());
            // 原点检人、原点检开始时间
            newChangeRecord.setOriginCheckPerson(modelObject.getCheckPerson());
            newChangeRecord.setOriginStartTime(modelObject.getStartTime());
            // 新点检人、新点检开始时间
            newChangeRecord.setNowCheckPerson(modelObject.getNewCheckPerson());
            newChangeRecord.setNowStartTime(modelObject.getNewStartTime());
            List<SpotInspectionChangeRecord> changeRecords = deviceInsTaskDb.getSpotInspectionChangeRecords();
            changeRecords.add(newChangeRecord);
            fabosJsonDao.update(deviceInsTaskDb);
        }
        return null;
    }

    @Override
    public SpotInspectionTaskChange fabosJsonFormValue(List<SpotInspectionTask> data, SpotInspectionTaskChange fabosJsonForm, String[] param) {
        SpotInspectionTask deviceInsTask = data.get(0);
        fabosJsonForm.setGeneralCode(deviceInsTask.getGeneralCode());
        fabosJsonForm.setCheckPerson(deviceInsTask.getCheckPerson());
        fabosJsonForm.setStartTime(deviceInsTask.getStartTime());
        return fabosJsonForm;
    }

    /**
     * 检测是否有变更
     * <AUTHOR>
     * @date 2025/4/2 16:26
     * @param deviceInsTask
     * @param modelObject
     * @return
     */
    private boolean isTaskModified(SpotInspectionTask deviceInsTask, SpotInspectionTaskChange modelObject) {
        // 1. 检查变更原因是否为空（若不为空，则认为有变更）
        if (StringUtils.isNotBlank(modelObject.getChangeReason())) {
            return true;
        }
        // 2. 检查点检人是否变更（需判空）
        UserMTO originalCheckPerson = deviceInsTask.getCheckPerson();
        UserMTO newCheckPerson = modelObject.getNewCheckPerson();
        if (newCheckPerson != null && originalCheckPerson != null
                && !newCheckPerson.getId().equals(originalCheckPerson.getId())) {
            return true;
        }
        // 3. 检查开始时间是否变更（需处理 null 情况）
        Date originalStartDate = deviceInsTask.getStartTime();
        Date newStartDate = modelObject.getNewStartTime();
        if (newStartDate != null && !newStartDate.equals(originalStartDate)) {
            return true;
        }
        // 默认无变更
        return false;
    }
}
