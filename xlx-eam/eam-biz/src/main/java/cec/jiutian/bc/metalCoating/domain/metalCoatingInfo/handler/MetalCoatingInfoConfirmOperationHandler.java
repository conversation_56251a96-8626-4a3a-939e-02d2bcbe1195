package cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.handler;

import cec.jiutian.bc.metalCoating.domain.metalCoatingInfo.model.MetalCoatingInfo;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description TODO
 */
@Component
public class MetalCoatingInfoConfirmOperationHandler implements OperationHandler<MetalCoatingInfo,Void> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public String exec(List<MetalCoatingInfo> data, Void modelObject, String[] param) {
        MetalCoatingInfo metalCoatingInfo = data.get(0);
        if (CollectionUtils.isEmpty(metalCoatingInfo.getDetailList())) {
            throw new FabosJsonApiErrorTip("金属涂层检查信息详情不能为空");
        }
        metalCoatingInfo.setBusinessState(OrderCurrentStateEnum.Enum.EXECUTE.name());
        fabosJsonDao.mergeAndFlush(metalCoatingInfo);

        return "alert('操作成功')";
    }
}
