package cec.jiutian.bc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum NamingRuleCodeEnum {

    MaintenanceRequest("维修需求单"),
    RepairSupply("物料领料单"),
    ProtectionConfirm("维修前防护确认单"),
    PermitToWorkManagement("安全作业许可证"),
    COPPER_RELATED_APPLICATION("涉铜申请单"),
    MAGNETIC_PARTICLE_APPLICATION("磁颗申请单"),
    MaterialRequisition("线边领料单"),
    ReleaseSlip("放行条"),
    OutsourceMaintenance("委外维修单"),
    ReceiveStockIn("领料入库申请"),
    LotSerialId("车间库存批次号"),
    LineStockOut("线边领用出库单号"),
    SPARE_PARTS_BORROWING_FORM("备品备件借用单"),
    CalibrationItem("仪器仪表校验项"),
    CalibrationPlan("仪器仪表校验计划"),
    WorkshopReturnRequest("车间退库申请单"),
    WorkshopReturnStockIn("车间退料入库单"),
    SPARE_PARTS_LIFE_MANAGEMENT("备品备件寿命管理"),
    BorrowReturnRecord("仪器仪表借用单"),
    SPARE_PARTS_LIFE_LEDGER("备品备件寿命台账"),
    SparePartsAbnormal("备品备件异常管理"),
    INSTRUMENT_INSPECTION_MANAGEMENT("仪器送检管理"),
    CalibrationTask("仪器仪表检验任务单"),
    EQUIPMENT_MAINTENANCE_STANDARD("设备保养标准"),
    MAIN_LUBR_PLAN_MANAGEMENT("保养润滑计划管理"),
    SpotInspectionPlan("点检计划"),
    MAIN_LUBR_TASK_MANAGEMENT("保养润滑任务管理"),
    MAIN_LUBR_MATERIAL_REQUISITION("保养润滑物料领料"),
    SpecialEquipmentVerificationItem("特种设备校验项"),
    SpecialEquipmentVerificationPlan("特种设备校验计划"),
    SpecialEquipmentVerificationTask("特种设备校验任务"),
    SpecialEquipmentMaintenanceTask("特种设备维修任务"),
    MaterialRequest("领料申请"),
    ToolBorrowingAndReturn("工具借用与归还"),
    MySpotInspectionTask("我的点检任务"),
    SpotInspectionTask("点检任务"),
    MetalCoatingInfo("金属涂层检查信息"),
    AlarmInformation("告警信息查询"),
    ;

    private final String value;
}
