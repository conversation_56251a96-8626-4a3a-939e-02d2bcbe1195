package cec.jiutian.bc.spotInspection.domain.insTask.handler;

import cec.jiutian.bc.spotInspection.domain.insTask.model.MySpotInspectionTask;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskResultEnum;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionTaskStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class SpotInspectionTaskSubmitHandler implements OperationHandler<MySpotInspectionTask, Void> {
    @Resource
    private FabosJsonDao fabosJsonDao;
    // 异常状态：否
    private static final String NO = SpotInspectionTaskResultEnum.Enum.NO.name();
    // 异常状态：差
    private static final String POOR = SpotInspectionTaskResultEnum.Enum.POOR.name();

    @Transactional
    @Override
    public String exec(List<MySpotInspectionTask> data, Void modelObject, String[] param) {
        // 提交后修改状态为已完成
        MySpotInspectionTask myDeviceInsTask = data.get(0);
        // 设置提交状态为2 已经提交, 不可再提交
        myDeviceInsTask.setIsSubmit("2");
        myDeviceInsTask.setCurrentState(SpotInspectionTaskStateEnum.Enum.COMPLETE.name());
        // 统计异常数量
        int exceptionNum = (int) myDeviceInsTask.getSpotInspectionTaskDetails().stream()
                .filter(deviceInsTaskDetail -> NO.equals(deviceInsTaskDetail.getResult())
                    || POOR.equals(deviceInsTaskDetail.getResult()))
                .count();
        myDeviceInsTask.setExceptionNum(exceptionNum);
        // 添加任务完成时间
        myDeviceInsTask.setFinishTime(new Date());
        fabosJsonDao.update(myDeviceInsTask);
        return null;
    }
}
