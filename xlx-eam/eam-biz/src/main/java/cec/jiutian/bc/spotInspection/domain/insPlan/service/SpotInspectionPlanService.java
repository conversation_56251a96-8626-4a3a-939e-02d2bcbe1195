package cec.jiutian.bc.spotInspection.domain.insPlan.service;


import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.bc.spotInspection.domain.insTask.service.SpotInspectionTaskService;
import cec.jiutian.bc.spotInspection.enumeration.SpotInspectionSkipTimeEnum;
import cec.jiutian.common.util.DateUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class SpotInspectionPlanService {

    @Resource
    private SpotInspectionTaskService spotInspectionTaskService;
    @Resource
    private FabosJsonDao fabosJsonDao;

    /**
     * 定时任务  处理 生成任务
     */
    @Transactional
    public void generateDeviceInsTask() {
        log.info("定时任务：设备点检计划生成点检任务 开始执行-------------");
        List<SpotInspectionPlan> deviceInsPlanList = findPlans();
        if(CollectionUtils.isNotEmpty(deviceInsPlanList)){
            //生成任务
            deviceInsPlanList.forEach(d->{
                log.info("定时任务：设备点检计划生成点检任务 -------------{}",d.getGeneralCode());
                spotInspectionTaskService.generateDeviceInsTask(d);
                SpotInspectionSkipTimeEnum.Enum anEnum = StringUtils.isNotBlank(d.getSkipTime()) ? SpotInspectionSkipTimeEnum.Enum.valueOf(d.getSkipTime()) : null;
                //Date nextGenerateDate = StocktakingDateUtil.addTime(d.getNextCheckTime(), d.getCycleNum(),QcPlanCycleEnum.Enum.valueOf(d.getCycle()), anEnum);
//                if(nextGenerateDate.before(d.getEndTime())){
//                    d.setLastCheckTime(d.getNextCheckTime());
//                    d.setNextCheckTime(nextGenerateDate);
//                }else {
//                    d.setLastCheckTime(d.getNextCheckTime());
//                    d.setNextCheckTime(null);
//                }
                fabosJsonDao.mergeAndFlush(d);
            });
        }
    }

    /**
     * 查询需要生成任务的计划
     * @return
     */
    private List<SpotInspectionPlan> findPlans() {
        Date now = new Date();
        Date[] dateRange = DateUtils.getStartAndEndOfDay(now);

        // 创建 HQL 查询
        String hql = "SELECT l FROM SpotInspectionPlan l " +
                "WHERE l.endTime > :currentDate " +
                "AND l.effective = 'Y'"+
                "AND l.startTime < :currentDate "+
                "AND l.nextCheckTime BETWEEN :startOfDay AND :endOfDay";
        // 执行查询
        TypedQuery<SpotInspectionPlan> query = fabosJsonDao.getEntityManager().createQuery(hql, SpotInspectionPlan.class);
        query.setParameter("currentDate", now);
        query.setParameter("startOfDay", dateRange[0]);
        query.setParameter("endOfDay", dateRange[1]);
        return query.getResultList();
    }
}
