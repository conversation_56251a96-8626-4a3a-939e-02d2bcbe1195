package cec.jiutian.bc.spotInspection.remote.provider;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.spotInspection.domain.insPlan.model.SpotInspectionPlan;
import cec.jiutian.bc.spotInspection.domain.insPlan.service.SpotInspectionPlanService;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description:
 */
@FabosCustomizedService(value = SpotInspectionPlan.class)
@Component
@Transactional
@Slf4j
public class SpotInspectionPlanProvider implements IJobProvider {
    @Resource
    private SpotInspectionPlanService spotInspectionPlanService;
    @Override
    @FabosJob(comment = "自动创建设备点检任务方法")
    public String exec(String code, String param) {
        log.info("自动创建设备点检任务方法,定时任务开始执行");
        spotInspectionPlanService.generateDeviceInsTask();
        return null;
    }
}
