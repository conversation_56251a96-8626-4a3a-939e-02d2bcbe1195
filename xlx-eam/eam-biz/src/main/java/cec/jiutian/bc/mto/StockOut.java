package cec.jiutian.bc.mto;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.common.enums.OrderCurrentStateEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.SkipMetadataScanning;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@FabosJson(
        name = "其他出库",
        orderBy = "createTime desc",
        filter = @Filter("currentState = 'COMPLETE'"),
        power = @Power(importable = false, export = false,add = false,edit = false,delete = false)
)
@Table(name = "stock_out")
@Entity
@Getter
@Setter
@SkipMetadataScanning
public class StockOut extends NamingRuleModel {

    @FabosJsonField(
            views = @View(title = "来源单号", show = false),
            edit = @Edit(title = "来源单号", show = false)
    )
    private String sourceNumber;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", show = false, type = EditType.CHOICE,
                    search = @Search,
                    choiceType = @ChoiceType(fetchHandler = OrderCurrentStateEnum.class))
    )
    private String currentState;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "stock_out_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "其他出库明细", type = EditType.TAB_REFER_ADD),
            views = @View(title = "其他出库明细", type = ViewType.TABLE_VIEW, extraPK = "inventoryId")
    )
    private List<StockOutDetail> stockOutDetailList;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "申请日期", type = ViewType.DATE),
            edit = @Edit(title = "申请日期", show = false, search = @Search(vague = true),
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date applyDate;
}
