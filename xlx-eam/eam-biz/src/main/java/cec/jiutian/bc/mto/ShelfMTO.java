package cec.jiutian.bc.mto;

import cec.jiutian.bc.enums.FreezeStatusEnum;
import cec.jiutian.bc.enums.PrcsFlagEnum;
import cec.jiutian.bc.enums.WareUseTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Entity
@Table(name = "mo_wrhs_shlf")
@FabosJson(
        name = "库位",
        orderBy = "onlineDate desc, priority asc",
        power = @Power(
                importable = false, export = false, add = false, delete = false, edit = false
        ),
        filter = @Filter("freezeStatus = 'NotOnHold' and stockFlag = 'N'")
)
public class ShelfMTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    @FabosJsonField(
            views = @View(title = "自增ID", show = false),
            edit = @Edit(title = "自增ID",
                    show = false,
                    readonly = @Readonly(add = true, edit = true))
    )
    private Long id;

    @Column(name = "blck_id", nullable = false)
    @FabosJsonField(
            views = @View(title = "库区ID"),
            edit = @Edit(title = "库区ID",
                    readonly = @Readonly(add = true, edit = true)
            )
    )
    private Long blockId;

    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称",
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "blck_nm", length = 40, nullable = false)
    private String blockName;

    @FabosJsonField(
            views = @View(title = "库位名称"),
            edit = @Edit(title = "库位名称",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_nm", length = 40, nullable = false)
    private String locationName;

    @FabosJsonField(
            views = @View(title = "库位代码"),
            edit = @Edit(title = "库位代码",
                    search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_cd", length = 40, nullable = false)
    private String locationCode;

    @FabosJsonField(
            views = @View(title = "库位简称"),
            edit = @Edit(title = "库位简称",
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_shrt_nm", length = 40, nullable = false)
    private String locationShortName;

    @FabosJsonField(
            views = @View(title = "库位描述"),
            edit = @Edit(title = "库位描述",
                    inputType = @InputType(length = 400))
    )
    @Column(name = "shlf_ds", length = 400, nullable = false)
    private String locationDescription;

    @FabosJsonField(
            views = @View(title = "使用类型"),
            edit = @Edit(title = "使用类型",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WareUseTypeEnum.class),
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_usg_typ_cd", length = 40, nullable = false)
    private String usageType;

    @FabosJsonField(
            views = @View(title = "库位材质"),
            edit = @Edit(title = "库位材质",
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_mtrl_typ_cd", length = 40, nullable = false)
    private String materialType;

    @FabosJsonField(
            views = @View(title = "储位类型"),
            edit = @Edit(title = "储位类型",
                    inputType = @InputType(length = 40))
    )
    @Column(name = "shlf_typ_cd", length = 40, nullable = false)
    private String storageType;

    @FabosJsonField(
            views = @View(title = "立体层数"),
            edit = @Edit(title = "立体层数",
                    inputType = @InputType(length = 10))
    )
    @Column(name = "lyer_tx", length = 10, nullable = false)
    private String layer;

    @FabosJsonField(
            views = @View(title = "平面区域数"),
            edit = @Edit(title = "平面区域数",
                    inputType = @InputType(length = 10))
    )
    @Column(name = "ara_tx", length = 10, nullable = false)
    private String area;

    @FabosJsonField(
            views = @View(title = "满载信息"),
            edit = @Edit(title = "满载信息",
                    inputType = @InputType(length = 2),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)
            )
    )
    @Column(name = "full_flg", length = 2, nullable = false)
    private String fullFlag;

    @FabosJsonField(
            views = @View(title = "建位时间"),
            edit = @Edit(title = "建位时间",
                    dateType = @DateType(type = DateType.Type.DATE_TIME)
            )
    )
    @Column(name = "onln_dt", nullable = false)
    private Date onlineDate;

    @FabosJsonField(
            views = @View(title = "库位宽"),
            edit = @Edit(title = "库位宽",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "wdth_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal width;

    @FabosJsonField(
            views = @View(title = "库位长"),
            edit = @Edit(title = "库位长",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "lngth_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal length;

    @FabosJsonField(
            views = @View(title = "库位高"),
            edit = @Edit(title = "库位高",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "hgt_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal height;

    @FabosJsonField(
            views = @View(title = "使用面积"),
            edit = @Edit(title = "使用面积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "usd_ara_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal usedArea;

    @FabosJsonField(
            views = @View(title = "占地面积"),
            edit = @Edit(title = "占地面积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "cvrd_ara_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal coveredArea;

    @FabosJsonField(
            views = @View(title = "使用容积"),
            edit = @Edit(title = "使用容积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "usd_vol_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal usedVolume;

    @FabosJsonField(
            views = @View(title = "容积"),
            edit = @Edit(title = "容积",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "vol_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal volume;

    @FabosJsonField(
            views = @View(title = "最大承重"),
            edit = @Edit(title = "最大承重",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "max_load_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal maxLoad;

    @FabosJsonField(
            views = @View(title = "最大层高"),
            edit = @Edit(title = "最大层高",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "lyer_max_hgt_nb", precision = 18, scale = 2, nullable = false)
    private BigDecimal maxLayerHeight;

    @FabosJsonField(
            views = @View(title = "是否允许混放"),
            edit = @Edit(title = "是否允许混放",
                    inputType = @InputType(length = 2),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class)
            )
    )
    @Column(name = "mx_stor_flg", length = 2, nullable = false)
    private String mixedStorageFlag;

    @FabosJsonField(
            views = @View(title = "库位地址信息"),
            edit = @Edit(title = "库位地址信息",
                    inputType = @InputType(length = 200))
    )
    @Column(name = "agv_lctn_nm", length = 200, nullable = false)
    private String agvLocation;

    @FabosJsonField(
            views = @View(title = "AGV原始点位名称"),
            edit = @Edit(title = "AGV原始点位名称",
                    inputType = @InputType(length = 40))
    )
    @Column(name = "orgnl_agv_lctn_nm", length = 40, nullable = false)
    private String originalAgvLocation;

    @FabosJsonField(
            views = @View(title = "处理状态"),
            edit = @Edit(title = "处理状态",
                    inputType = @InputType(length = 2),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = PrcsFlagEnum.class)
            )
    )
    @Column(name = "shl_prcs_flg", length = 2, nullable = false)
    private String processingStatus;

    @FabosJsonField(
            views = @View(title = "使用优先级"),
            edit = @Edit(title = "使用优先级",
                    inputType = @InputType(type = "number"))
    )
    @Column(name = "prity_nb", nullable = false)
    private Long priority;

    @FabosJsonField(
            views = @View(title = "仓库状态", show = false),
            edit = @Edit(title = "仓库状态",
                    readonly = @Readonly(add = true, edit = true),
                    inputType = @InputType(length = 2))
    )
    @Column(name = "tak_stk_flg", length = 2)
    private String stockFlag;


    @FabosJsonField(
            views = @View(title = "仓库冻结状态"),
            edit = @Edit(title = "仓库冻结状态",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = FreezeStatusEnum.class)
            )
    )
    @Column(name = "hld_st", length = 40, nullable = false)
    private String freezeStatus;
}